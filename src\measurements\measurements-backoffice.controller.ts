import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiNotFoundResponse, ApiOkResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { CustomerId } from "src/customers/domain/customer.entity";
import { listToDtoCollection } from "src/serialization/mappers";
import { User } from "src/users/domain/user.entity";
import { DatasourceService } from "./datasource.service";
import {
  LocationMeasurementNotFoundResDto,
  LocationMeasurementResDto,
} from "./dto/location-by-measure.dto";
import {
  CreateMeasurementTypeDto,
  UpdateMeasurementTypeDto,
} from "./dto/measurement-type.dto";
import { MeasurementsBackofficeService } from "./measurements-backoffice.service";
import { UnitOfMeasureService } from "./unit-of-measure.service";

@Controller({ version: "0", path: "measurements-backoffice" })
@UseGuards(JwtAuthGuard, CsrfGuard)
export class MeasurementsBackofficeApiController {
  constructor(
    private readonly metadataService: MeasurementsBackofficeService,
    private readonly unitsOfMeasureService: UnitOfMeasureService,
    private readonly datasourceService: DatasourceService
  ) {}

  @Get("data-types")
  async getAllDataTypes() {
    return listToDtoCollection(await this.metadataService.getAll("data"));
  }

  @Get("value-types")
  async getAllValueTypes() {
    return listToDtoCollection(await this.metadataService.getAll("value"));
  }

  @Get("/customer/:customerId")
  async getAllMeasurementsByCustomer(
    @Param("customerId") customerId: CustomerId,
    @CookieToken() headers: Request["headers"],
    @Query("lastreadings") lastReadings?: boolean
  ) {
    return listToDtoCollection(
      await this.metadataService.getAllMeasurementsByCustomer(
        customerId,
        headers,
        lastReadings
      )
    );
  }

  @Get("measurement-types")
  async getAllMeasurementTypes() {
    return listToDtoCollection(
      await this.metadataService.getAll("measurement")
    );
  }

  @Get("measurement-types/:id")
  @HasRole(Role.USER)
  async getMeasurementTypeById(@Param("id") id: number) {
    return await this.metadataService.getMeasurementTypeById(id);
  }

  @Post("measurement-types")
  @HasRole(Role.ADMIN)
  @HttpCode(201)
  async createMeasurementType(
    @AuthUser() authUser: User,
    @Body() body: CreateMeasurementTypeDto
  ) {
    return await this.metadataService.createMeasurementType({
      name: body.name,
      createdBy: authUser,
    });
  }

  @Put("measurement-types/:id")
  @HasRole(Role.ADMIN)
  @HttpCode(204)
  async updateMeasurementType(
    @AuthUser() authUser: User,
    @Param("id") id: number,
    @Body() body: UpdateMeasurementTypeDto
  ) {
    await this.metadataService.updateMeasurementType(id, {
      name: body.name,
      updatedBy: authUser,
    });
  }

  // @Delete("measurement-types/:id")
  // @HasRole(Role.ADMIN)
  // @HttpCode(204)
  // async deleteMeasurementType(@Param("id") id: number) {
  //   await this.metadataService.deleteMeasurementType(id);
  // }

  @Get("measurement-types/:measurementTypeId/units-of-measure")
  async getAllMeasurementTypeUnitsOfMeasure(
    @Param("measurementTypeId") measurementTypeId: string
  ) {
    return listToDtoCollection(
      await this.unitsOfMeasureService.getAllByMeasurementType(
        Number(measurementTypeId)
      )
    );
  }

  @Get("locations")
  async getAllLocations() {
    return listToDtoCollection(await this.metadataService.getAll("location"));
  }

  @Get("location-by-measure/:measurementId")
  @ApiOkResponse({ type: LocationMeasurementResDto })
  @ApiNotFoundResponse({ type: LocationMeasurementNotFoundResDto })
  async getLocationByMeasure(
    @Param("measurementId") assetMeasurementId: number
  ) {
    return await this.metadataService.getLocationByMeasurement(
      assetMeasurementId
    );
  }

  @Get("datasources")
  async getAllDatasources(@Query("extra") extra?: boolean) {
    return listToDtoCollection(await this.datasourceService.getAll(extra));
  }
}
