import { MikroOrmModule } from '@mikro-orm/nestjs';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from 'src/app.module';
import config from 'src/mikro-orm.config';
import authConfig from 'src/authentication/auth.config';
import redisConfig from 'src/redis/redis.config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { Asset, AssetFactory } from 'src/assets/domain/asset.entity';
import { AssetRepository } from 'src/assets/repository/asset.repository';
import { AssetModule } from 'src/assets/asset.module';
import {
  assetFixtureFactory,
  createAssetType,
  deleteAssetByTag,
  deleteAssetType,
} from './fixtures/asset.fixture';
import { userFixtureFactory } from './fixtures/user.fixture';
import { customerFixtureFactory } from './fixtures/customer.fixture';
import { HierarchyGraphService } from 'src/assets/hierarchy-graph.service';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { Metric } from 'src/assets/domain/metric.entity';

type AssetRepositoryFixture = Awaited<
  ReturnType<typeof assetRepositoryFixtureFactory.createOfficeHierarchy>
>;
const assetRepositoryFixtureFactory = {
  createOfficeHierarchy: async (testingModule: TestingModule) => {
    const userFixture = await userFixtureFactory.createSuperUser(testingModule);
    const customerFixture = await customerFixtureFactory.createKawasaki(
      testingModule,
      userFixture.superUserId,
    );
    const fixture = await assetFixtureFactory.createOfficeSite(
      testingModule,
      userFixture.superUserId,
      customerFixture.customerId,
    );
    return {
      cleanUp: async () => {
        await fixture.cleanUp();
        await customerFixture.cleanUp();
        await userFixture.cleanUp();
      },
      fordBuildingAssetId: fixture.fordBuildingAssetId,
      officeAssetId: fixture.officeAssetId,
      customerId: customerFixture.customerId,
      superUserId: userFixture.superUserId,
    };
  },
};

describe('AssetRepository', () => {
  let testingModule: TestingModule;
  let repository: AssetRepository;
  let fixture: AssetRepositoryFixture;

  beforeEach(async () => {
    testingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [authConfig, redisConfig],
          isGlobal: true,
          envFilePath: `${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env`,
          cache: true,
        }),
        MikroOrmModule.forRoot({
          ...config,
          entitiesTs: [Asset, AssetType, Customer, Metric],
          allowGlobalContext: true,
        }),
        MikroOrmModule.forFeature([Asset]),
        EventEmitterModule.forRoot(),
        AssetModule,
      ],
      providers: [
        {
          provide: HierarchyGraphService,
          useValue: new HierarchyGraphService(),
        },
        AssetRepository,
      ],
    }).compile();

    fixture = await assetRepositoryFixtureFactory.createOfficeHierarchy(
      testingModule,
    );

    repository = testingModule.get(AssetRepository);
  });

  afterEach(async () => {
    await fixture.cleanUp();
  });

  test('given an asset hierarchy, when removing its root all of it assets should be removed', async () => {
    const rootAsset = await repository.findById(fixture.fordBuildingAssetId);

    const result = await repository.removeHierarchy(
      rootAsset as Asset,
      fixture.superUserId,
    );

    expect(result).toStrictEqual([
      fixture.fordBuildingAssetId,
      fixture.officeAssetId,
    ]);
    expect(
      (await repository.getAllByCustomerId(fixture.customerId)).length,
    ).toBe(0);
  });

  test('given a dangling customer asset, when querying it should not be visible', async () => {
    const lightBulbCleanUp = await createLightbulb(
      testingModule,
      fixture.customerId,
      repository,
    );

    const rootAssets = await repository.getAllByCustomerId(fixture.customerId, {
      parentIds: [-1],
    });

    expect(
      rootAssets.find((asset) => asset.tag === 'Light bulb'),
    ).toBeUndefined();
    await lightBulbCleanUp();
  });

  test('given an asset hierarchy, dag count should return 2', async () => {
    const count = await repository.countHierarchyNodes(
      fixture.fordBuildingAssetId,
      [],
    );

    expect(count).toBe(2);
  });

  test('given an asset hierarchy, dag count filtering by office id should return 1', async () => {
    const count = await repository.countHierarchyNodes(
      fixture.fordBuildingAssetId,
      [fixture.officeAssetId],
    );

    expect(count).toBe(1);
  });

  test('given an asset hierarchy, dag count filtering by office and building id should return 2', async () => {
    const count = await repository.countHierarchyNodes(
      fixture.fordBuildingAssetId,
      [fixture.officeAssetId, fixture.fordBuildingAssetId],
    );

    expect(count).toBe(2);
  });
});

async function createLightbulb(
  testingModule: TestingModule,
  customerId: number,
  repository: AssetRepository,
) {
  const lightAssetTypeId = (await createAssetType(testingModule, 'Light')).id;
  const danglingLightBulbAsset = AssetFactory.create({
    tag: 'Light bulb',
    assetTypeId: lightAssetTypeId,
    customerId: customerId,
  });
  await repository.add(danglingLightBulbAsset);

  return async () => {
    await deleteAssetByTag(testingModule, 'Light bulb');
    await deleteAssetType(testingModule, 'Light');
  };
}
