import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  UseGuards,
} from "@nestjs/common";
import { ApiOkResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "src/users/domain/user.entity";
import { FactorSchedule } from "./domain/factor-schedule.entity";
import { FactorType } from "./domain/factor-type.entity";
import { TimeVaryingFactor } from "./domain/TimeVaryingFactor.entity";
import { FactorTypeDto } from "./dto/factor-type.dto";
import { TimeVaryingFactorCreateDTO } from "./dto/time-varing-factor.dto";
import { FactorService } from "./factor.service";

@Controller({
  version: "0",
  path: "factors",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class FactorApiController {
  constructor(private readonly factorService: FactorService) {}

  @Get("/types")
  @HasRole(Role.POWER_USER)
  @ApiOkResponse({
    description: "Factor Types",
    type: FactorTypeDto,
    isArray: true,
  })
  async getFactorTypes(): Promise<{
    total: number;
    items: FactorType[];
  }> {
    const factorTypes = await this.factorService.getFactorTypes();
    return {
      total: factorTypes.length,
      items: factorTypes,
    };
  }

  @Get("/types/:id")
  @HasRole(Role.ADMIN)
  @ApiOkResponse({
    description: "Factor Type",
    type: FactorTypeDto,
  })
  async getFactorType(@Param("id") id: number): Promise<FactorType> {
    return await this.factorService.getFactorTypeById(id);
  }

  @Post("/types")
  @HasRole(Role.ADMIN)
  @HttpCode(201)
  @ApiOkResponse({
    description: "Factor Type",
    type: FactorTypeDto,
  })
  async createFactorType(
    @AuthUser() authUser: User,
    @Body() factorType: FactorTypeDto
  ) {
    return await this.factorService.createFactorType({
      ...factorType,
      createdBy: authUser,
      updatedBy: authUser,
    });
  }

  @Get("/schedules/:factorTypeId")
  @HasRole(Role.ADMIN)
  @ApiOkResponse({
    description: "Factor Schedules",
    type: FactorSchedule,
    isArray: true,
  })
  async getFactorSchedules(
    @Param("factorTypeId") factorTypeId: number
  ): Promise<{
    total: number;
    items: FactorSchedule[];
  }> {
    const factorSchedules = await this.factorService.getFactorSchedules(
      factorTypeId
    );
    return {
      total: factorSchedules.length,
      items: factorSchedules,
    };
  }

  @Get("/schedules/:factorTypeId/weekTime/:schduleId")
  @HasRole(Role.ADMIN)
  @ApiOkResponse({
    description: "Factor Schedule",
    type: FactorSchedule,
  })
  async getFactorSchedule(@Param("schduleId") schduleId: number) {
    const timeOfDay = await this.factorService.getFactorScheduleTimeOfDay(
      schduleId
    );
    return {
      total: timeOfDay.length,
      items: timeOfDay,
    };
  }

  @Post("/timeVariangFactor")
  @HasRole(Role.ADMIN)
  @HttpCode(201)
  @ApiOkResponse({
    description: "Factor Time Of Day Value",
    type: TimeVaryingFactor,
  })
  async createTimeVaryingFactor(
    @AuthUser() authUser: User,
    @Body() factoryTimeVaring: TimeVaryingFactor
  ) {
    return await this.factorService.createTimeVaryingFactor({
      ...factoryTimeVaring,
      createdBy: authUser,
      updatedBy: authUser,
    });
  }

  @Post("/create-factor")
  @HasRole(Role.POWER_USER)
  @HttpCode(201)
  @ApiOkResponse({
    description: "Factor Type",
    type: TimeVaryingFactorCreateDTO,
  })
  async createFactor(
    @AuthUser() authUser: User,
    @Body() factorType: TimeVaryingFactorCreateDTO
  ) {
    await this.factorService.createTimeVaryingFactorSchedule(
      factorType,
      authUser
    );
  }

  @Get("/timeVariangFactor/:measureId")
  @HasRole(Role.ADMIN)
  @ApiOkResponse({
    description: "Factor Time Of Day Value",
    type: TimeVaryingFactor,
    isArray: true,
  })
  async getTimeVaryingFactor(@Param("measureId") measureId: number) {
    return await this.factorService.getTimeVaryingFactorByMeasure(measureId);
  }

  @Put("/timeVariangFactor/:id")
  @HasRole(Role.ADMIN)
  @ApiOkResponse({
    description: "Factor Time Of Day Value",
    type: FactorTypeDto,
  })
  async updateTimeVaryingFactor(
    @Param("id") id: number,
    @AuthUser() authUser: User,
    @Body() factoryTimeVaring: TimeVaryingFactorCreateDTO
  ) {
    return await this.factorService.updateTimeVaryingFactor(
      id,
      authUser,
      factoryTimeVaring
    );
  }
}
