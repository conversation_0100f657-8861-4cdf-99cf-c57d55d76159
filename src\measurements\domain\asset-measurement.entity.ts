import {
  <PERSON><PERSON><PERSON>,
  Filter,
  <PERSON><PERSON><PERSON><PERSON>,
  PrimaryKey,
  Property,
  Ref,
  Reference,
} from "@mikro-orm/core";
import { Metric } from "src/assets/domain/metric.entity";
import { Asset } from "../../assets/domain/asset.entity";
import { User, UserId } from "../../users/domain/user.entity";
import { DataType } from "./data-type.entity";
import { Datasource } from "./datasource.entity";
import { Location } from "./location.entity";
import { MeasurementType } from "./measurement-type.entity";
import { Measurement } from "./measurement.entity";
import { UnitOfMeasure } from "./unit-of-measure.entity";
import { ValueType } from "./value-type.entity";

export type AssetMeasurementCreationParams = {
  tag: string;
  metric?: Metric;
  assetId: number;
  measurementType: MeasurementType;
  dataType: DataType;
  valueType: ValueType;
  meterFactor?: number;
  unitOfMeasure?: UnitOfMeasure;
  datasource?: Datasource;
  description?: string;
  location?: Location;
  writeback?: boolean;
};
export type AssetMeasurementId = number;

@Entity()
@Filter({
  name: "isNotDeleted",
  cond: { deletedAt: { $eq: null } },
  default: true,
})
export class AssetMeasurement {
  constructor(params: AssetMeasurementCreationParams) {
    const { assetId, location, ...rest } = params;
    const measurement = new Measurement(rest);

    this.measurement = measurement;
    // this.writeback = params.writeback;
    this.assetId = assetId;
    this.asset = Reference.createFromPK(Asset, assetId);
    this.location = location;
  }

  @PrimaryKey()
  id!: AssetMeasurementId;

  @ManyToOne({
    fieldName: "measurement",
    onDelete: "cascade",
    eager: true,
    hidden: true,
  })
  measurement!: Measurement;

  @Property({ persist: false, serializedName: "measurement_id" })
  get measurementId() {
    return this.measurement.id;
  }

  @Property({ persist: false })
  get tag() {
    return this.measurement._tag;
  }

  set tag(value: string) {
    this.measurement._tag = value;
  }

  @Property({ persist: false, serializedName: "metric_id" })
  get metricId() {
    return this.measurement.metric?.id ?? null;
  }

  @Property({ persist: false })
  get description() {
    return this.measurement.description;
  }

  @Property({ persist: false, serializedName: "meter_factor" })
  get meterFactor() {
    return this.measurement.meterFactor ?? null;
  }

  @Property({ persist: false, serializedName: "type_id" })
  get typeId() {
    return this.measurement.typeId;
  }

  @Property({ persist: false, serializedName: "data_type_id" })
  get dataTypeId() {
    return this.measurement.dataTypeId;
  }

  @Property({ persist: false, serializedName: "value_type_id" })
  get valueTypeId() {
    return this.measurement.valueTypeId;
  }

  @Property({ persist: false, serializedName: "unit_of_measure_id" })
  get unitOfMeasureId() {
    return this.measurement.unitOfMeasureId ?? null;
  }

  @Property({ persist: false, serializedName: "writeback" })
  get writeback() {
    return this.measurement.writeback;
  }
  @ManyToOne(() => Asset, {
    fieldName: "asset",
    onDelete: "cascade",
    hidden: true,
  })
  asset!: Ref<Asset>;

  @Property({ fieldName: "asset", serializedName: "asset_id", persist: false })
  assetId!: number;

  @ManyToOne({ fieldName: "location", nullable: true, hidden: true })
  location?: Location;

  @Property({ persist: false, serializedName: "location_id" })
  get locationId() {
    return this.location?.id ?? null;
  }

  @Property({ persist: false, serializedName: "datasource_id" })
  get datasourceId() {
    return this.measurement.datasourceId ?? null;
  }

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "created_by",
  })
  createdBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "created_by" })
  createdById?: UserId;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;

  @Property({ hidden: true, length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "deleted_by",
  })
  deletedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "deleted_by" })
  deletedById?: UserId;
}
