import {
  Injectable,
  Logger,
  OnM<PERSON>uleD<PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as amqp from "amqplib";
import { AlertService } from "src/alert/alert.service";
import { Alerts } from "src/alert/domain/alert.entity";
import { NotificationDTO } from "src/notification/notification.dto";

@Injectable()
export class RabbitMqService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RabbitMqService.name);
  private channel: amqp.Channel;
  private connection: amqp.Connection;
  private readonly topicExchange = "notifications_topic_exchange";
  private rabbitMQConfig: {
    brokers: string;
    topicAlert: string;
    clientId: string;
    topicNotification: string;
    kafkaGroupId: string;
    kafkaGroupIdInstance: string;
    frontendUrl: string;
    username: string;
    password: string;
    alertQueueName: string;
  };
  constructor(
    private readonly configService: ConfigService,
    private readonly alertService: AlertService
  ) {
    const {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
      username,
      password,
      alertQueueName,
    } = configService.get("rabbitMQ");
    this.rabbitMQConfig = {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
      username,
      password,
      alertQueueName,
    };
    this.logger.log("RabbitMQ initalized");
  }

  async onModuleInit() {
    const { username, password, brokers } = this.rabbitMQConfig;
    const connectionUrl = `amqp://${username}:${password}@${brokers}`;
    this.logger.log(`Connecting to RabbitMQ at ${connectionUrl}`);

    this.connection = await amqp.connect(connectionUrl);
    this.channel = await this.connection.createChannel();

    await this.channel.assertExchange(this.topicExchange, "topic", {
      durable: true,
    });

    await this.channel.assertQueue(this.rabbitMQConfig.alertQueueName, {
      durable: true,
    });
    await this.channel.bindQueue(
      this.rabbitMQConfig.alertQueueName,
      this.topicExchange,
      "alert.*"
    );

    await this.startConsumer();
    this.logger.log("RabbitMQ setup complete.");
  }

  async onModuleDestroy() {
    await this.channel.close();
    await this.connection.close();
    this.logger.log("RabbitMQ connection closed.");
  }

  async sendMessage(routingKey: string, payload: any) {
    const message = Buffer.from(JSON.stringify(payload));
    this.channel.publish(this.topicExchange, routingKey, message);
    this.logger.log(
      `Message published to exchange with routingKey=${routingKey}`
    );
  }

  private async startConsumer() {
    this.channel.consume(this.rabbitMQConfig.alertQueueName, async (msg) => {
      if (!msg) return;

      try {
        const data = JSON.parse(msg.content.toString());
        this.logger.log(`📩 Consumed message: ${JSON.stringify(data)}`);

        const {
          alert_id,
          input_value,
          state,
          timestamp,
          comparator,
          event_id,
        } = data;

        const alertDto = await this.alertService.getAlertNew(alert_id, true);

        for (const user of alertDto.alertUsers) {
          let notify: NotificationDTO;
          try {
            user.user = await this.alertService.getAlertUsers(user.user);

            if (user.notificationtype === 2 || user.notificationtype === 3) {
              notify = new NotificationDTO(
                user.user.id,
                await this.smsTemplate(
                  alertDto,
                  input_value,
                  state,
                  timestamp,
                  comparator,
                  event_id,
                  alertDto.thresholdType?.threshold ?? undefined
                ),
                new Date(),
                false,
                0,
                "sms",
                user.user.country_code + user.user.phone_no,
                (alertDto.thresholdType?.threshold !== undefined &&
                  alertDto.thresholdType?.threshold === "STALE") ??
                undefined
                  ? "STALE - Alert Notification"
                  : "Alert Notification"
              );

              if (user.user.phone_no) {
                this.sendMessage("notification.sms", notify);
              }
            }

            if (user.notificationtype === 1 || user.notificationtype === 3) {
              notify = new NotificationDTO(
                user.user.id,
                await this.htmlEmailTemplate(
                  alertDto,
                  input_value,
                  state,
                  timestamp,
                  comparator,
                  event_id,
                  alertDto.thresholdType?.threshold ?? undefined
                ),
                new Date(),
                false,
                0,
                "email",
                user.user.email,
                (alertDto.thresholdType?.threshold !== undefined &&
                  alertDto.thresholdType?.threshold === "STALE") ??
                undefined
                  ? "STALE - Alert Notification"
                  : "Alert Notification"
              );

              if (user.user.email) {
                this.sendMessage("notification.email", notify);
              }
            }
          } catch (err) {
            this.logger.error(`Notification error: ${(err as Error).message}`);
            this.sendMessage("notification.dlq", notify);
          }
        }
      } catch (err) {
        this.logger.error(
          `Failed to process message: ${(err as Error).message}`
        );
      } finally {
        this.logger.log(`message: ${msg}`);
        this.channel.ack(msg);
      }
    });
  }

  private async htmlEmailTemplate(
    alert: Alerts,
    inputValue: any,
    state: string,
    timestamp: number,
    alertCondition: string,
    event_id: number,
    threshold_type?: string
  ) {
    const now = new Date();
    const start = new Date(now.getTime() - 4 * 60 * 60 * 1000).getTime();
    const formattedTimestamp = new Date(timestamp).toUTCString();
    const link = event_id
      ? `<a href="${this.rabbitMQConfig.frontendUrl}/customer/${alert.customerId}/dashboard/0?event_id=${event_id}">Visit Dashboard</a>`
      : "";
    return `
    <html>
      <body>
        <h3>
          ${
            threshold_type !== undefined && threshold_type === "STALE"
              ? state === "STALE"
                ? "Stale Alert Raised"
                : "Stale Alert Resolved"
              : threshold_type !== undefined && threshold_type === "DEAD"
              ? state === "DEAD"
                ? "Dead Measurement Alert Raised"
                : "Dead Measurement Resolved"
              : state === "EXCEEDED"
              ? "Limit Alert"
              : "Returned to Normal"
          }
        </h3>
        <p><strong>At:</strong> ${formattedTimestamp}</p>
        <p><strong>Asset:</strong> ${alert.asset.tag}</p>
        <p><strong>Measurement:</strong> ${alert.measurement.tag}</p>
        <p><strong>Current Value:</strong> ${inputValue}</p>
        <h2>Settings:</h2>
        <p><strong>Threshold:</strong> ${
          threshold_type === "DEAD"
            ? state === "DEAD"
              ? `No data received for ${alert.thresholdValue} minutes`
              : `New Data received for ${alert.thresholdValue} minutes`
            : threshold_type === "STALE"
            ? state === "STALE"
              ? `No fresh data received for ${alert.thresholdValue} minutes`
              : `Fresh data received after ${alert.thresholdValue} minutes`
            : `${alertCondition} ${alert.thresholdValue}`
        }</p>
        ${
          threshold_type !== "DEAD" && threshold_type !== "STALE"
            ? `<p><strong>Return to Normal:</strong> ${alertCondition} ${alert.resetDeadband}</p>`
            : ""
        }
        ${link}
      </body>
    </html>
  `;
  }

  private async smsTemplate(
    alert: Alerts,
    inputValue: any,
    state: string,
    timestamp: number,
    alertCondition: string,
    event_id: number,
    threshold_type?: string
  ) {
    const now = new Date();
    const start = new Date(now.getTime() - 4 * 60 * 60 * 1000).getTime();
    return `${
      threshold_type !== undefined && threshold_type === "STALE"
        ? state === "STALE"
          ? "Stale Alert Raised"
          : "Stale Alert Resolved"
        : threshold_type !== undefined && threshold_type === "DEAD"
        ? state === "DEAD"
          ? "Dead Measurement Alert Raised"
          : "Dead Measurement Resolved"
        : state === "EXCEEDED"
        ? "Limit Alert"
        : "Returned to Normal"
    }
      At: ${new Date(new Date(timestamp).getTime()).toUTCString()}
      Asset: ${alert.asset.tag}
      Measurement: ${alert.measurement.tag}
      CurrentValue: ${inputValue}
      Settings:
      Threshold: ${
        threshold_type === "DEAD"
          ? state === "DEAD"
            ? `No data received for ${alert.thresholdValue} minutes`
            : `New Data received for ${alert.thresholdValue} minutes`
          : threshold_type === "STALE"
          ? state === "STALE"
            ? `No fresh data received for ${alert.thresholdValue} minutes`
            : `Fresh data received after ${alert.thresholdValue} minutes`
          : `${alertCondition} ${alert.thresholdValue}`
      }
      ${
        threshold_type !== "DEAD" && threshold_type !== "STALE"
          ? `Return to Normal: ${alertCondition} ${alert.resetDeadband}`
          : ""
      }
      ${
        event_id !== undefined
          ? `Link : ${this.rabbitMQConfig.frontendUrl}/customer/${alert.customerId}/dashboard/0?event_id=${event_id}`
          : ""
      }`;
  }
}
