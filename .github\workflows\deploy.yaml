name: Validation

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - prod
      release-tag:
        description: 'Backend Release Tag to deploy'
        required: false
        default: ''

#Set ENV variables
env:
  AWS_REGION: 'us-east-1'  # Set your AWS region
  ECR_REGISTRY: '067172429169.dkr.ecr.us-east-1.amazonaws.com'

permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout

jobs:
  build:
    name: Backend Build
    runs-on: on-prem
    steps:
      - name: Checkout backend repository
        uses: actions/checkout@v2
        with:
          repository: bromptonenergy/BromptonDash
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          token: ${{ secrets.GHA_TOKEN }}
          fetch-depth: 0

      - name: Navigate to Admin API folder
        run: |
          pwd
          ls -al 
          echo "Listing Contents of brompton-admin-api..........."
          ls -alh

      - uses: actions/setup-node@v4
        with:
          node-version: '16.15.0'

      - name: Install NextJS
        run: |
          npm i
          npm i @nestjs/core
          node --version
          pwd

      - name: Backend Build
        run: |
          ls -al
          echo "Running NPM RUN BUILD.............."
          npm run build
          pwd

      - name: Navigate to DIST and update the orm file
        run: |
          echo "Current Working Directory"
          pwd
          echo "Listing contents of current Directory"
          ls -al 
          echo "Navigating into DIST folder "
          cd dist
          echo "Listing contents of DIST folder"
          ls -al
          sed -i "s|'dist/\*\*/\*.entity.js'|'**/\*.entity.js'|g" mikro-orm.config.js
          echo "++++++++++++++++++++++UPDATED FILE++++++++++++++++++++++"
          cat mikro-orm.config.js

      - name: Check package.json
        run: |
          cd dist
          ls -alh
          cd ..
          ls -alh
          cp package.json ./dist/

      - name: Create Dockerfile
        run: |
          # Set command based on the environment
          DEPLOY_COMMAND="deploy:${{ github.event.inputs.environment }}"
          
          cat <<EOF > Dockerfile
          FROM node:16.15.0-slim
          # Set the working directory in the container
          WORKDIR /backend
          
          # Copy the package.json file to the working directory
          COPY package.json .
           
          # Copy the sparkplug_b.proto file to the working directory
          COPY sparkplug_b.proto .
             
          
          # Install npm dependencies
          RUN npm install
          
          # Copy the dist directory to /backend/dist
          ADD dist .
          
          # List contents of /backend to verify structure
          RUN ls -al
          
          # COPY package.json ./dist/

          # RUN cd dist 
          RUN ls -al
          
          # Make port 8081 available to the world outside this container
          EXPOSE 8081
          
          # Run the deploy script for the current environment when the container launches
          CMD ["npm", "run", "${DEPLOY_COMMAND}"]
          EOF

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v1.7.0
        with:
          role-to-assume: ${{ secrets.PIPELINE_ROLE }} 
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}
  
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Validate release tag for prod
        run: |
          if [ "${{ github.event.inputs.environment }}" == "prod" ] && [ -z "${{ github.event.inputs.release-tag || github.sha }}" ]; then
            echo "Release tag is required for prod deployment!"
            exit 1
          fi

      - name: Set ECR Repo Environment Variable
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment }}
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend-$ENVIRONMENT" >> $GITHUB_ENV
            echo "CLUSTER_NAME=k3s" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend-$ENVIRONMENT" >> $GITHUB_ENV
            echo "CLUSTER_NAME=brompton-energy-eks-prod" >> $GITHUB_ENV
          fi

      - name: Build, tag, and push image to Amazon ECR
        run: |
          IMAGE_TAG=${{ github.event.inputs.release-tag || github.sha }}
          echo "IMAGE_TAG: $IMAGE_TAG"
          echo "DOCKERFILE=Dockerfile"
          echo "DOCKER FILE : $DOCKERFILE"
          pwd
          ls -al
          echo $ECR_REPO_NAME
          echo $CLUSTER_NAME
          docker build -t $ECR_REPO_NAME:$IMAGE_TAG -f Dockerfile .
          docker tag $ECR_REPO_NAME:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
  
  deploy:
    name: Deploy on Kubernetes
    needs: build
    runs-on: on-prem
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          repository: bromptonenergy/BromptonDash
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          token: ${{ secrets.GHA_TOKEN }}

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "NAMESPACE=application" >> $GITHUB_ENV  # Set namespace to application
          
          IMAGE_TAG="${{ github.event.inputs.release-tag || github.sha }}"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "CLUSTER_NAME=k3s" >> $GITHUB_ENV
            echo "ECR_REPO_NAME=brompton-energy/backend-dev" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "CLUSTER_NAME=brompton-energy-eks-prod" >> $GITHUB_ENV
            echo "ECR_REPO_NAME=brompton-energy/backend-prod" >> $GITHUB_ENV
          else
            echo "Error: Unknown environment '$ENVIRONMENT'"
            exit 1
          fi

      - name: Update Kubernetes Context
        run: |
          if [[ "$ENVIRONMENT" == "prod" ]]; then
            echo "Updating kubeconfig for production..."
            aws eks update-kubeconfig --name "$CLUSTER_NAME" --region "${{ env.AWS_REGION }}"
          elif [[ "$ENVIRONMENT" == "dev" ]]; then
            echo "Using existing kubeconfig for dev environment..."
            kubectl config current-context  
          else
            echo "Unknown environment: $ENVIRONMENT"
            exit 1
          fi

      - name: Deploy to Kubernetes
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          IMAGE_TAG="${{ github.event.inputs.release-tag || github.sha }}"
          
          echo "Deploying to $ENVIRONMENT environment with image tag $IMAGE_TAG..."
          
          # Apply ConfigMap for the environment
          echo "Applying ConfigMap for $ENVIRONMENT..."
          kubectl apply -f k8s-manifest/configmaps/bromptondash-config-$ENVIRONMENT.yaml
          
          # Apply Secrets for the environment
          echo "Applying Secrets for $ENVIRONMENT..."
          kubectl apply -f k8s-manifest/secrets/bromptondash-secrets-$ENVIRONMENT.yaml
          
          # Apply Deployment with variables replaced
          echo "Applying Deployment..."
          cat k8s-manifest/deployments/admin-api-deployment.yaml | \
            sed -e "s|\${ENVIRONMENT}|$ENVIRONMENT|g" \
                -e "s|\${IMAGE_TAG}|$IMAGE_TAG|g" | \
            kubectl apply -f -
          
          # Apply Service
          echo "Applying Service..."
          cat k8s-manifest/services/admin-api-service.yaml | sed "s/\${ENVIRONMENT}/$ENVIRONMENT/g" | kubectl apply -f -
          
          # # Wait for deployment to complete
          # echo "Waiting for deployment to roll out..."
          # kubectl rollout status deployment/admin-api-$ENVIRONMENT -n application --timeout=300s
          
          # echo "Checking pod status"
          # kubectl get pods -n application

  notification:
    name: Teams Notification
    needs: [build, deploy]
    if: always()   # Ensures notification runs regardless of the previous job outcomes.
    runs-on: on-prem
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          # If 'release-tag' is set, use it; otherwise use the SHA from the push event
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          fetch-depth: 0
          
      - name: Set variables
        id: set_vars
        run: |
          # If branch_name is empty (not set via workflow_dispatch), derive from push context
          if [ -z "${{ github.event.inputs.branch_name }}" ]; then
            BN="${GITHUB_REF#refs/heads/}"
          else
            BN="${{ github.event.inputs.branch_name }}"
          fi

          # If release-tag is empty, fallback to the current commit SHA
          if [ -z "${{ github.event.inputs.release-tag }}" ]; then
            RT="${GITHUB_SHA}"
          else
            RT="${{ github.event.inputs.release-tag }}"
          fi

          # Grab the latest commit message
          CM="$(git log -1 --pretty=%B | tr '\n' ' ' | sed 's/  */ /g')"
          ENVIRONMENT="${{ github.event.inputs.environment }}"

          echo "BRANCH_NAME=$BN" >> $GITHUB_ENV
          echo "RELEASE_TAG=$RT" >> $GITHUB_ENV
          echo "COMMIT_MESSAGE=$CM" >> $GITHUB_ENV
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV

      - name: Get current time (IST)
        id: current_time
        run: echo "time=$(TZ=Asia/Kolkata date)" >> $GITHUB_OUTPUT

      # Send a Success Card if both build and deploy succeeded.
      - name: Send Teams Card on Success
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          echo "Sending success card to Teams..."
          cat <<EOF > card_payload.json
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "00FF00",
            "summary": "Build Succeeded",
            "sections": [
              {
                "activityTitle": "✅ Build Succeeded",
                "facts": [
                  {
                    "name": "ENVIRONMENT",
                    "value": "${ENVIRONMENT}"
                  },
                  {
                    "name": "Branch Name",
                    "value": "${BRANCH_NAME}"
                  },
                  {
                    "name": "Repository",
                    "value": "${GITHUB_REPOSITORY}"
                  },
                  {
                    "name": "Tag/Commit",
                    "value": "${RELEASE_TAG}"
                  },
                  {
                    "name": "Commit Link",
                    "value": "[${GITHUB_SHA}](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})"
                  },
                  {
                    "name": "Commit Message",
                    "value": "${COMMIT_MESSAGE}"
                  },
                  {
                    "name": "Time (IST)",
                    "value": "${{ steps.current_time.outputs.time }}"
                  },
                  {
                    "name": "Author",
                    "value": "${GITHUB_ACTOR}"
                  }
                ],
                "markdown": true
              }
            ],
            "potentialAction": [
              {
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [
                  {
                    "os": "default",
                    "uri": "${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
                  }
                ]
              }
            ]
          }
          EOF

          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"
          
          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION_ARGS }}"

      # Send a Failure Card if either build or deploy failed.
      - name: Send Teams Card on Failure
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          echo "Sending failure card to Teams..."
          cat <<EOF > card_payload.json
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "FF0000",
            "summary": "Build Failed",
            "sections": [
              {
                "activityTitle": "❌ Build Failed",
                "facts": [
                  {
                    "name": "ENVIRONMENT",
                    "value": "${ENVIRONMENT}"
                  },
                  {
                    "name": "Branch Name",
                    "value": "${BRANCH_NAME}"
                  },
                  {
                    "name": "Repository",
                    "value": "${GITHUB_REPOSITORY}"
                  },
                  {
                    "name": "Tag/Commit",
                    "value": "${RELEASE_TAG}"
                  },
                  {
                    "name": "Commit Link",
                    "value": "[${GITHUB_SHA}](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})"
                  },
                  {
                    "name": "Commit Message",
                    "value": "${COMMIT_MESSAGE}"
                  },
                  {
                    "name": "Time (IST)",
                    "value": "${{ steps.current_time.outputs.time }}"
                  },
                  {
                    "name": "Author",
                    "value": "${GITHUB_ACTOR}"
                  }
                ],
                "markdown": true
              }
            ],
            "potentialAction": [
              {
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [
                  {
                    "os": "default",
                    "uri": "${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
                  }
                ]
              }
            ]
          }
          EOF

          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"
          
          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION_ARGS }}"
        
