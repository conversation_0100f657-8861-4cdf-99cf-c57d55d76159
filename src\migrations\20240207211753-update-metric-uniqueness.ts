import { Migration } from '@mikro-orm/migrations';

export class Migration20240209211753 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "metric" drop constraint "metric_name_unique";');
    this.addSql(
      'alter table "metric" add constraint "metric_name_asset_type_unique" unique ("name", "asset_type");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "metric" drop constraint "metric_name_asset_type_unique";',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_name_unique" unique ("name");',
    );
  }
}
