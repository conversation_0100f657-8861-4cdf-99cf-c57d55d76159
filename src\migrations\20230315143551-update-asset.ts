import { Migration } from '@mikro-orm/migrations';

export class Migration20230315143551 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "asset" drop column "created";');
    this.addSql('alter table "asset" drop column "updated";');
    this.addSql('alter table "asset" drop column "createdby";');
    this.addSql('alter table "asset" drop column "updatedby";');

    this.addSql('alter table "asset" drop constraint "type_fk";');

    this.addSql('alter table "asset" drop constraint "cust_fk";');

    this.addSql(
      'alter table "asset" add constraint "asset_a_type_foreign" foreign key ("a_type") references "asset_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql('alter table "asset" drop constraint "asset_a_type_foreign";');
    this.addSql(
      'alter table "asset" drop constraint "asset_customer_foreign";',
    );

    this.addSql(
      'alter table "asset" add constraint "type_fk" foreign key ("a_type") references "asset_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset" add constraint "cust_fk" foreign key ("customer") references "customer" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "asset" drop constraint "asset_created_by_foreign";',
    );
    this.addSql(
      'alter table "asset" drop constraint "asset_updated_by_foreign";',
    );

    this.addSql(
      'alter table "asset" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "asset" drop column "created_at";');
    this.addSql('alter table "asset" drop column "updated_at";');
    this.addSql('alter table "asset" drop column "created_by";');
    this.addSql('alter table "asset" drop column "updated_by";');
  }
}
