import { Injectable } from '@nestjs/common';

export type HierarchyEdge = {
  id: number;
  parent: number | null;
  child: number;
};

type ChildLink = {
  linkId: number;
  nodeId: number;
};

@Injectable()
export class HierarchyGraphService {
  /**
   * Identifies all asset links and nodes that need to be removed recursively
   * when a specific root node is deleted.
   *
   * @param rootNodeId The ID of the root node to be removed.
   * @param parentChildrenMap A map where the key is a parent node ID,
   * and the value is an array of its child links.
   * @param childParentsMap A map where the key is a child node ID,
   * and the value is an array of its parent node IDs.
   * @returns An object containing two sets of IDs:
   *  - linkIds: An array of IDs of all links that need to be removed.
   *  - nodeIds: An array of IDs of all nodes (assets) that need to be removed.
   */
  findCascadeRemoveIds(
    rootNodeId: number,
    parentChildrenMap: Map<number, ChildLink[]>,
    childParentsMap: Map<number, number[]>,
  ) {
    const linkIds = new Set<number>();
    const nodeIds = new Set<number>();

    // Find root node links to ancestors
    childParentsMap
      .get(rootNodeId)
      ?.flatMap((rootNodeParentId) => parentChildrenMap.get(rootNodeParentId))
      .filter(
        (link): link is ChildLink =>
          link !== undefined && link.nodeId === rootNodeId,
      )
      .forEach((link) => linkIds.add(link.linkId));
    nodeIds.add(rootNodeId);

    // Initialize a queue to process child nodes in a breadth-first manner
    const childrenToVisit = parentChildrenMap.get(rootNodeId) ?? [];

    while (childrenToVisit.length > 0) {
      const childToVisit = childrenToVisit.shift() as ChildLink;

      // Mark the link associated with the child node for removal
      linkIds.add(childToVisit.linkId);
      // Check if all parents of the child node are scheduled for removal
      if (
        childParentsMap
          .get(childToVisit.nodeId)
          ?.every((parentId) => nodeIds.has(parentId))
      ) {
        // Mark the child node for removal and add its unvisited children to the queue
        nodeIds.add(childToVisit.nodeId);

        (parentChildrenMap.get(childToVisit.nodeId) ?? [])
          .filter((nodeId) => !childrenToVisit.includes(nodeId))
          .map((nodeId) => childrenToVisit.push(nodeId));
      }
    }

    return {
      linkIds: [...linkIds],
      nodeIds: [...nodeIds],
    };
  }

  /**
   * Converts an array of database rows representing asset hierarchy edges into parent-children and child-parents maps
   * @param dbRows An array of objects representing individual asset hierarchy edges.
   * @returns
   * An object containing two maps:
   *  - parentChildrenMap: A map where the key is a parent node ID and the value is an array of
   *    ChildLink objects representing its child links.
   *  - childParentsMap: A map where the key is a child node ID and the value is an array of
   *    parent node IDs.
   */
  mapDbRowsToHierarchy(dbRows: HierarchyEdge[]) {
    const parentChildrenMap = new Map();
    const childParentsMap = new Map();

    dbRows.map((row) => {
      if (row.parent === null) {
        row.parent = -1;
      }
      const parentChildren = parentChildrenMap.get(row.parent) ?? [];
      parentChildren.push({
        linkId: row.id,
        nodeId: row.child,
      });
      parentChildrenMap.set(row.parent, parentChildren);

      const nodeParents = childParentsMap.get(row.child) ?? [];
      nodeParents.push(row.parent);
      childParentsMap.set(row.child, nodeParents);
    });

    return {
      parentChildrenMap,
      childParentsMap,
    };
  }
}
