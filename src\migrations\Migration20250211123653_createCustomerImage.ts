import { Migration } from '@mikro-orm/migrations';

export class Migration20250211123653_createCustomerImage extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "custom_image" ("id" serial primary key, "customer" int not null, "logo" text null, "created_at" timestamptz(6) not null, "created_by" int null, "updated_at" timestamptz(6) null, "updated_by" int null);');

    this.addSql('alter table "custom_image" add constraint "custom_image_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade;');
    this.addSql('alter table "custom_image" add constraint "custom_image_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "custom_image" add constraint "custom_image_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "custom_image" cascade;');
  }

}
