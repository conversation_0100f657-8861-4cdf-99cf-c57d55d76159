import {
  Role,
  RoleKey,
} from 'src/authorization/domain/customer-user-role.entity';
import { User, UserCreationParams } from '../domain/user.entity';
import { UserCreationDto, UserDto } from './user.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserMapper {
  toDto = (user: User): UserDto => {
    const {
      createdAt: _createdAt,
      createdBy: _createdBy,
      updatedAt: _updatedAt,
      updatedBy: _updatedBy,
      customerRoles: _customer,
      firstName: _firstName,
      lastName: _lastName,
      globalRole: _globalRole,
      password: _password,
      ...rest
    } = user;
    return {
      ...rest,
      first_name: user.firstName,
      last_name: user.lastName,
      global_role: user.globalRoleName(),
      scoped_roles: user.scopedRoles.map((scopedRole) => ({
        role: Role[scopedRole.role] as <PERSON><PERSON><PERSON>,
        customer_ids: scopedRole.customerIds,
      })),
    };
  };

  creationDtoToParams = (userCreation: UserCreationDto): UserCreationParams => {
    const {
      first_name: _firstName,
      last_name: _lastName,
      global_role: _globalRole,
      scoped_roles: _scopedRoles,
      ...rest
    } = userCreation;
    return {
      ...rest,
      firstName: userCreation.first_name,
      lastName: userCreation.last_name,
      globalRole: userCreation.global_role
        ? Role[userCreation.global_role]
        : undefined,
      scopedRoles:
        userCreation.scoped_roles?.map((scopedRole) => ({
          role: Role[scopedRole.role],
          customerIds: scopedRole.customer_ids,
        })) ?? [],
    };
  };
}
