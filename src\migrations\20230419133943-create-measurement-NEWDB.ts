import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230419133943 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "measurement" ("id" serial primary key, "tag" varchar(150) not null, \
      "enabled" boolean null, "created" timestamptz(6) null, "updated" timestamptz(6) null, \
      "createdby" int null, "updatedby" int null, "m_type" int not null, "unit_of_measure" int null, \
      "meter_factor" double precision null, "data_type" int not null, "description" varchar(100) null, \
      "value_type" int null default 1);',
    );

    this.addSql(
      'create index "measurement_m_type_idx" on "measurement" ("m_type");',
    );
    this.addSql(
      'create index "measurement_uom_idx" on "measurement" ("unit_of_measure");',
    );
    this.addSql(
      'create index "measurement_data_type_idx" on "measurement" ("data_type");',
    );
    this.addSql(
      'create index "measurement_value_type_idx" on "measurement" ("value_type");',
    );

    this.addSql(
      'alter table "measurement" add constraint "type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "uom_fk" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement" add constraint "data_type_fk" foreign key ("data_type") references "data_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "v_type_fk" foreign key ("value_type") references "value_type" ("id") on update cascade on delete set null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "measurement" cascade;');
  }
}
