import { MikroOrmModule } from '@mikro-orm/nestjs';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from 'src/app.module';
import { AssetMeasurement } from 'src/measurements/domain/asset-measurement.entity';
import { AssetMeasurementRepository } from 'src/measurements/repository/asset-measurement.repository';
import config from 'src/mikro-orm.config';
import authConfig from 'src/authentication/auth.config';
import {
  createAssetMeasurement,
  deleteMeasurementByTag,
  measurementFixtureFactory,
} from './fixtures/measurement.fixture';
import { MeasurementModule } from 'src/measurements/measurement.module';
import { TimeSeriesService } from 'src/measurements/time-series.service';
import redisConfig from 'src/redis/redis.config';
import { userFixtureFactory } from './fixtures/user.fixture';
import { customerFixtureFactory } from './fixtures/customer.fixture';
import { assetFixtureFactory } from './fixtures/asset.fixture';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { EntityManager, EntityRepository } from '@mikro-orm/postgresql';
import { Measurement } from 'src/measurements/domain/measurement.entity';

type AssetMeasurementFixture = Awaited<
  ReturnType<typeof assetMeasurementFixtureFactory.createBikeEngineCharge>
>;

const assetMeasurementFixtureFactory = {
  createBikeEngineCharge: async (testingModule: TestingModule) => {
    const superUserFixture = await userFixtureFactory.createSuperUser(
      testingModule,
    );
    const kawasakiCustomerFixture = await customerFixtureFactory.createKawasaki(
      testingModule,
      superUserFixture.superUserId,
    );

    const engineAssetsFixture = await assetFixtureFactory.createEngines(
      testingModule,
      superUserFixture.superUserId,
      kawasakiCustomerFixture.customerId,
    );

    const measurementFixture = await measurementFixtureFactory.createMetadata(
      testingModule,
    );

    const bikeEngineCharge = await createAssetMeasurement(
      testingModule,
      {
        tag: 'Bike_Engine/Charge',
        typeId: measurementFixture.chargeTypeId,
        dataTypeId: measurementFixture.realDataTypeId,
        valueTypeId: measurementFixture.nominalValueTypeId,
        locationId: measurementFixture.outputLocationId,
        description: 'A motorbike engine',
        meterFactor: 1,
        assetId: engineAssetsFixture.bikeEngineAssetId,
      },
      kawasakiCustomerFixture.customerId,
      superUserFixture.superUserId,
    );

    return {
      superUserId: superUserFixture.superUserId,
      customerId: kawasakiCustomerFixture.customerId,
      carEngineAssetId: engineAssetsFixture.carEngineAssetId,
      bikeEngineAssetId: engineAssetsFixture.bikeEngineAssetId,
      bikeEngineCharge,
      cleanUp: async () => {
        await deleteMeasurementByTag(testingModule, bikeEngineCharge.tag);
        await measurementFixture.cleanUp();
        await engineAssetsFixture.cleanUp();
        await kawasakiCustomerFixture.cleanUp();
        await superUserFixture.cleanUp();
      },
    };
  },
};

describe('AssetMeasurementRepository', () => {
  let testingModule: TestingModule;
  let fixture: AssetMeasurementFixture;
  let repository: AssetMeasurementRepository;
  let measurementEntityRepository: EntityRepository<Measurement>;

  beforeAll(async () => {
    const timeSeriesServiceMock: Pick<TimeSeriesService, 'create'> = {
      create: jest.fn(),
    };
    testingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [authConfig, redisConfig],
          isGlobal: true,
          envFilePath: `${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env`,
          cache: true,
        }),
        MikroOrmModule.forRoot({
          ...config,
          allowGlobalContext: true,
        }),
        MikroOrmModule.forFeature([AssetMeasurement]),
        EventEmitterModule.forRoot(),
        MeasurementModule,
      ],
      providers: [AssetMeasurementRepository],
    })
      // can be replaced with overrideModule when upgrading to nestjs 10
      .overrideProvider(TimeSeriesService)
      .useValue(timeSeriesServiceMock)
      .compile();

    fixture = await assetMeasurementFixtureFactory.createBikeEngineCharge(
      testingModule,
    );

    repository = testingModule.get(AssetMeasurementRepository);

    const em = testingModule.get(EntityManager);
    measurementEntityRepository = em.getRepository(Measurement);
  });

  afterAll(async () => {
    await fixture.cleanUp();
  });

  describe('findById', () => {
    test('with matching filters should return bike engine charge', async () => {
      const measurement = await repository.findById(
        fixture.bikeEngineCharge.id,
        {
          assetId: fixture.bikeEngineAssetId,
          customerId: fixture.customerId,
        },
      );

      expect(measurement).not.toBeNull();
      expect(measurement?.tag).toBe(fixture.bikeEngineCharge.tag);
    });

    test('with non-matching filters should return nothing', async () => {
      const measurement = await repository.findById(
        fixture.bikeEngineCharge.id,
        {
          assetId: fixture.bikeEngineAssetId,
          customerId: 404,
        },
      );

      expect(measurement).toBeNull();
    });
  });

  describe('remove', () => {
    beforeAll(async () => {
      const measurement = await repository.findById(
        fixture.bikeEngineCharge.id,
      );

      if (measurement) {
        await repository.remove(measurement);
      }
    });

    test('should hide measurement from queries', async () => {
      expect(await repository.findById(fixture.bikeEngineCharge.id)).toBeNull();
    });

    test('should remove measurement entity', async () => {
      expect(
        await measurementEntityRepository.findOne({
          id: fixture.bikeEngineCharge.measurementId,
          deletedAt: { $ne: null },
        }),
      ).not.toBeNull();
    });

    afterAll(async () => {
      // Re-create for other tests
      await fixture.cleanUp();
      fixture = await assetMeasurementFixtureFactory.createBikeEngineCharge(
        testingModule,
      );
    });
  });

  describe('batchRemove', () => {
    beforeAll(async () => {
      await repository.batchRemoveByAssetId(
        [fixture.bikeEngineAssetId, fixture.carEngineAssetId],
        new Date(),
        fixture.superUserId,
      );
    });

    test('bike engine charge should be removed', async () => {
      expect(await repository.findById(fixture.bikeEngineCharge.id)).toBeNull();
    });

    test('should remove measurement entity', async () => {
      expect(
        await measurementEntityRepository.findOne({
          id: fixture.bikeEngineCharge.measurementId,
          deletedAt: { $ne: null },
        }),
      ).not.toBeNull();
    });

    afterAll(async () => {
      await fixture.cleanUp();
      fixture = await assetMeasurementFixtureFactory.createBikeEngineCharge(
        testingModule,
      );
    });
  });
});
