import {
  <PERSON><PERSON><PERSON>,
  <PERSON>lter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ey,
  Property,
  Ref,
  Unique,
} from "@mikro-orm/core";
import { Customer } from "src/customers/domain/customer.entity";
import { User } from "../../users/domain/user.entity";
import { Asset } from "src/assets/domain/asset.entity";
import { DashboardTemplate } from "src/dashboard-template/domain/dashboard-template.entity";

export type DashboardId = number;

@Entity()
@Filter({
  name: "isNotDeleted",
  cond: { deletedAt: { $eq: null } },
  default: true,
})
@Unique({ properties: ["title", "customer"] }) // Composite unique constraint on title and customer_id
export class Dashboard {
  @PrimaryKey()
  id!: DashboardId;

  @Property({ length: 50 })
  title!: string;

  @Property({ length: 65536, type: "text" })
  data!: string;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "created_by" })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: false, fieldName: "customer_id" }) // Ensure customer is not nullable
  customer!: Ref<Customer>;

  @ManyToOne({
    entity: () => Asset,
    fieldName: "asset",
    nullable: true,
  })
  asset?: Asset;

  @ManyToOne({
    entity: () => DashboardTemplate,
    fieldName: "dashboardTemplate",
    nullable: true,
  })
  dashboardTemplate?: DashboardTemplate;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @Property({ hidden: true, length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "deleted_by",
  })
  deletedBy?: Ref<User>;
}
