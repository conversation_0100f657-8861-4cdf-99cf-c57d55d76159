import { Migration } from '@mikro-orm/migrations';
import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20240515140321 extends NewDbMigration {

  async conditionalUp(): Promise<void> {
    this.addSql('create table "calculation_period" ("id" serial primary key, "value" varchar(20) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);');

    this.addSql('create table "calculation_template" ("id" serial primary key, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "name" varchar(50) not null, "expression" varchar(150) not null, "description" varchar(150) null, "imports_list" varchar(150) null, "data_type" int not null);');
    this.addSql('alter table "calculation_template" add constraint "calculation_name_unique" unique ("name");');

    this.addSql('create table "calculation_instance" ("id" serial primary key, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "calculation" int not null, "output_measurement" int not null, "ispersisted" boolean not null, "poll_period" int null);');
    this.addSql('alter table "calculation_instance" add constraint "calc_meas_unique" unique ("output_measurement");');

    this.addSql('create table "calculation_input" ("id" serial primary key, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "input_label" varchar(255) not null, "calculation_instance" int not null, "measurement" int null, "constant_number" varchar(255) null, "constant_string" varchar(30) null, "comment" varchar(50) null);');
    this.addSql('alter table "calculation_input" add constraint "calc_instance_input_unique" unique ("calculation_instance", "input_label");');

    this.addSql('alter table "calculation_template" add constraint "calc_templ_dtype_fk" foreign key ("data_type") references "data_type" ("id") on update cascade;');

    this.addSql('alter table "calculation_instance" add constraint "calculation_fk" foreign key ("calculation") references "calculation_template" ("id") on update cascade;');
    this.addSql('alter table "calculation_instance" add constraint "output_fk" foreign key ("output_measurement") references "measurement" ("id") on update cascade;');

    this.addSql('alter table "calculation_input" add constraint "calculation_instance_fk" foreign key ("calculation_instance") references "calculation_instance" ("id") on update cascade;');
    this.addSql('alter table "calculation_input" add constraint "calc_instance_meas_fk" foreign key ("measurement") references "measurement" ("id") on update cascade on delete set null;');

    this.addSql('alter table "calculation_instance" add constraint "poll_period_fk" foreign key (poll_period) references "calculation_period" ("id");');

  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "calculation_instance" drop constraint "poll_period_fk"');
    this.addSql('alter table "calculation_instance" drop constraint "calculation_fk";');

    this.addSql('alter table "calculation_input" drop constraint "calculation_instance_fk";');

    this.addSql('drop table if exists "calculation_period" cascade;');

    this.addSql('drop table if exists "calculation_template" cascade;');

    this.addSql('drop table if exists "calculation_instance" cascade;');

    this.addSql('drop table if exists "calculation_input" cascade;');
  }

}
