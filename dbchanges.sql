CREATE TABLE public.dashboard (
	id serial4 NOT NULL,
	title varchar(50) NOT NULL,
	"data" varchar(65536) not null,
	customer_id int4 NOT NULL,
	created_at timestamptz(6) NULL,
	updated_at timestamptz(6) NULL,
	created_by int4 NULL,
	updated_by int4 NULL,
	CONSTRAINT dashboard_pkey PRIMARY KEY (id)
);


-- public.dashboard foreign keys

ALTER TABLE public.dashboard ADD CONSTRAINT dashboard_created_by_foreign FOREIGN KEY (created_by) REFERENCES public."user"(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE public.dashboard ADD CONSTRAINT dashboard_updated_by_foreign FOREIGN KEY (updated_by) REFERENCES public."user"(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE public.dashboard ADD CONSTRAINT dashboard_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES public."customer"(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE public.customer ADD logo text NULL;
