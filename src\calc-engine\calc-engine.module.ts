import { MikroOrmModule } from "@mikro-orm/nestjs";
import { HttpModule } from "@nestjs/axios";
import { forwardRef, Logger, Module } from "@nestjs/common";
import { AssetModule } from "src/assets/asset.module";
import { AuthModule } from "src/authentication/auth.module";
import { CustomersModule } from "src/customers/customers.module";
import { TransactionFactory } from "src/db/TransactionFactory";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { DataType } from "src/measurements/domain/data-type.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { AssetMeasurementRepository } from "src/measurements/repository/asset-measurement.repository";
import { TimeSeriesService } from "src/measurements/time-series.service";
import { TimeseriesModule } from "src/timeseries/timeseries.module";
import { CalcEngineController } from "./calc-engine.controller";
import { CalcEngineService } from "./calc-engine.service";
import { CalculationInput } from "./domain/calculation-input.entity";
import { CalculationInstance } from "./domain/calculation-instance.entity";
import { CalculationPeriod } from "./domain/calculation-period.entity";
import { CalculationTemplate } from "./domain/calculation-template.entity";
import { CalcEngineRepository } from "./repository/calc-engine.repository";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      Measurement,
      CalculationInput,
      CalculationInstance,
      CalculationPeriod,
      CalculationTemplate,
      DataType,
      AssetMeasurement,
    ]),
    forwardRef(() => AuthModule),
    CustomersModule,
    AssetModule,
    HttpModule,
    TimeseriesModule,
  ],
  controllers: [CalcEngineController],
  providers: [
    CalcEngineService,
    CalcEngineRepository,
    TransactionFactory,
    TimeSeriesService,
    AssetMeasurementRepository,
    {
      provide: Logger,
      useValue: new Logger(CalcEngineService.name),
    },
  ],
  exports: [CalcEngineRepository],
})
export class CalcEngineModule {}
