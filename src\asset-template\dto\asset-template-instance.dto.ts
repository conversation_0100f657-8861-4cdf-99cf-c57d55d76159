import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { AssetCreationDto, AssetDto } from 'src/assets/dto/asset.dto';
import {
  AssetMeasurementCreationDto,
  AssetMeasurementDto,
} from 'src/measurements/dto/asset-measurement.dto';

class AssetInstanceDto extends AssetDto {
  @ApiProperty()
  customer_id!: number;
}

class AssetInstanceCreationDto extends OmitType(AssetCreationDto, ['type_id']) {
  @ApiProperty()
  customer_id!: number;
}

export class MeasurementInstanceDto extends OmitType(AssetMeasurementDto, [
  'metric_id',
]) {
  @ApiProperty()
  metric_id!: number;
}

export class MeasurementInstanceCreationDto extends OmitType(
  AssetMeasurementCreationDto,
  ['type_id', 'data_type_id', 'value_type_id', 'tag', 'metric_id'],
) {
  @ApiPropertyOptional()
  tag?: string;

  @ApiProperty()
  metric_id!: number;
}

export class AssetTemplateInstanceDto {
  @ApiProperty()
  units_group_id!: number;

  @ApiProperty()
  asset!: AssetInstanceDto;

  @ApiProperty({ type: [MeasurementInstanceDto] })
  measurements!: MeasurementInstanceDto[];
}

export class AssetTemplateInstanceCreationDto {
  @ApiProperty()
  units_group_id!: number;

  @ApiProperty()
  asset!: AssetInstanceCreationDto;

  @ApiProperty({ type: [MeasurementInstanceCreationDto] })
  measurements!: MeasurementInstanceCreationDto[];
}
