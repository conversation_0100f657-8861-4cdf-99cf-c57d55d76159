import { Injectable } from "@nestjs/common";
import { UpdateRequiredAndNonNull } from "../domain/asset-template-instance";
import { AssetTemplateMeasurement } from "../domain/asset-template-measurement.entity";
import { AssetTemplate } from "../domain/asset-template.entity";
import { AssetTemplateDto } from "./asset-template.dto";
import { Asset } from "src/assets/domain/asset.entity";

@Injectable()
export class AssetTemplateMapper {
  toDto(assetTemplate: AssetTemplate, asset?: Asset[]): AssetTemplateDto {
    const {
      id,
      assetType,
      manufacturer,
      modelNumber: model_number,
      measurements,
      customer,
    } = assetTemplate;
    return {
      id,
      asset_type_id: assetType.id,
      manufacturer,
      model_number,
      customer: customer?.id ?? null,
      asset: asset
        ? asset.map((a) => ({
            id: a.id,
            tag: a.tag,
            type_id: a.assetTypeId,
            parent_ids: a.parentIds.map((parent) => parent),
            children_ids: a.childrenIds.map((child) => child),
            time_zone: a.timeZone ?? null,
            latitude: a.latitude ?? null,
            longitude: a.longitude ?? null,
            description: a.description ?? null,
            customer: a.customer?.id ?? null,
          }))
        : undefined,
      measurements: measurements
        .getItems()
        .filter(
          (
            measurement
          ): measurement is UpdateRequiredAndNonNull<
            AssetTemplateMeasurement,
            "metric"
          > => measurement.metric !== undefined && measurement.metric !== null
        )
        .map((measurement) => {
          const {
            metric,
            measurementType,
            dataType,
            valueType,
            meterFactor,
            description,
            datasource,
            location,
            writeback,
            id,
          } = measurement;

          return {
            id,
            metric_id: metric.id,
            name: metric.name,
            type_id: measurementType.id,
            data_type_id: dataType.id,
            value_type_id: valueType.id,
            meter_factor: meterFactor ?? null,
            description: description ?? null,
            datasource_id: datasource?.id ?? null,
            location_id: location?.id ?? null,
            writeback: writeback ?? null,
          };
        }),
    };
  }
}
