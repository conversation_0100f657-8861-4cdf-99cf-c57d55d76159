import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { setupApp } from './test-utils';
import { createSuperUser, deleteUser } from './fixtures/user.fixture';

describe('/sessions', () => {
  let app: INestApplication;
  let testingModule: TestingModule;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    await deleteUser(testingModule, 'testuser');
    await createSuperUser(testingModule);
  });

  afterAll(async () => {
    await deleteUser(testingModule, 'testuser');
    await app.close();
  });

  test('POST with no credentials should return unauthorized', () => {
    return request(app.getHttpServer()).post('/v0/sessions').expect(401);
  });

  test('POST with wrong credentials should return unauthorized', () => {
    return request(app.getHttpServer())
      .post('/v0/sessions')
      .send({ username: 'testuser', password: 'wrongpassword' })
      .expect(401);
  });

  describe('POST with correct credentials', () => {
    let response: request.Response;

    beforeAll(async () => {
      response = await request(app.getHttpServer())
        .post('/v0/sessions')
        .send({ username: 'testuser', password: 'testpassword' });
    });

    it('should return a 201', () => {
      expect(response.status).toBe(201);
    });

    it('should contain a JWT access token in its response', () => {
      expect(response.body.access_token).not.toBeUndefined();
      expect(response.body.access_token).not.toBeNull();
    });

    it('should set a JWT access token cookie', () => {
      const authCookieData = Object.fromEntries(
        response
          .get('Set-Cookie')[0]
          .split(';')
          .map((data) => data.trim().split('='))
          .map((data) => [data[0], data[1] ?? true]),
      );

      expect(authCookieData.HttpOnly).toBeTruthy();
      expect(authCookieData.Path).toBe('/');
      expect(authCookieData.Expires === undefined).toBeFalsy();
      expect(authCookieData['BE-AccessToken'] === undefined).toBeFalsy();
    });

    it('should set a CSRF token in its payload', () => {
      expect(response.body.csrf_token).not.toBeUndefined();
      expect(response.body.csrf_token).not.toBeNull();
    });

    it('should set a CSRF token cookie', () => {
      const csrfCookieData = Object.fromEntries(
        response
          .get('Set-Cookie')[1]
          .split(';')
          .map((data) => data.trim().split('='))
          .map((data) => [data[0], data[1] ?? true]),
      );

      expect(csrfCookieData.HttpOnly).toBeFalsy();
      expect(csrfCookieData.Path).toBe('/');
      expect(csrfCookieData.Expires === undefined).toBeTruthy();
      expect(csrfCookieData['BE-CSRFToken'] === undefined).toBeFalsy();
    });
  });
});
