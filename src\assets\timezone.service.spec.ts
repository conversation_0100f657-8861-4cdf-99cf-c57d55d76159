import { Test } from '@nestjs/testing';
import { TimeZoneService } from './timezone.service';
import { EntityManager } from '@mikro-orm/postgresql';
import { TimeZone } from './domain/time-zone.entity';

describe('Timezone service', () => {
  describe('when calling getAll', () => {
    let entityManagerArgs;

    beforeAll(async () => {
      const { timezonesService, entityManagerMock } =
        await createTimezonesService();

      await timezonesService.getAll();

      entityManagerArgs = entityManagerMock.find.mock.calls[0];
    });

    it('should load TimeZone entity', () => {
      expect(entityManagerArgs[0]).toBe(TimeZone);
    });

    it('should not have a where clause', () => {
      expect(entityManagerArgs[1]).toStrictEqual({});
    });

    it('should order by name', () => {
      expect(entityManagerArgs[2]).toStrictEqual({ orderBy: { name: 'asc' } });
    });
  });

  describe('exists', () => {
    it('should return true if the timezone exists', async () => {
      const { timezonesService } = await createTimezonesService();

      const result = await timezonesService.exists('ART');

      expect(result).toBeTruthy();
    });

    it('should return false if the timezone does not exist', async () => {
      const { timezonesService } = await createTimezonesService();

      const result = await timezonesService.exists('NOT VALID TZ');

      expect(result).toBeFalsy();
    });
  });
});

async function createTimezonesService() {
  const entityManagerMock = {
    find: jest.fn(),
    count: jest.fn((_, filter) => (filter.name === 'ART' ? 1 : 0)),
  };
  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: EntityManager,
        useValue: entityManagerMock,
      },
      TimeZoneService,
    ],
  }).compile();
  const timezonesService = moduleRef.get(TimeZoneService);
  return { timezonesService, entityManagerMock };
}
