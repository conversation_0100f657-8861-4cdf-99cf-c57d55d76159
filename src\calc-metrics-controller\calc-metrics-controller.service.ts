import { Injectable } from "@nestjs/common";
import { CalcMetricDTO } from "./dto/calculation-metric-create.dto";
import { CalcMetricRepository } from "./repositoty/calc-metric.repositoy";
import { User } from "src/users/domain/user.entity";

@Injectable()
export class CalcMetricsControllerService {
  constructor(private readonly calMetricRepository: CalcMetricRepository) {}

  async createCalcMetrics(calcMetric: CalcMetricDTO[], authUser: User) {
    return await this.calMetricRepository.createCalcMetrics(
      calcMetric,
      authUser
    );
  }
}
