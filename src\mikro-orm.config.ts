import { Options } from '@mikro-orm/core';
import { TsMorphMetadataProvider } from '@mikro-orm/reflection';

const config: Options = {
  entities: ['dist/**/*.entity.js'],
  entitiesTs: ['src/**/*.entity.ts'],
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? Number(process.env.DB_PORT) : 5432,
  dbName: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  type: 'postgresql',
  debug: false, // Enable for debugging
  pool: {
    min: 5,
    max: 10,
    idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
  },
  driverOptions: {
    connection: {
      connectTimeoutMS: 60000, // Set connection timeout to 60 seconds
      queryTimeout: 60000, // Set query timeout to 60 seconds (if supported by driver)
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    },
  },
  migrations: {
    path: './dist/migrations',
    pathTs: './src/migrations',
    tableName: 'mikro_orm_migrations',
    transactional: true,
    glob: '!(*.d).{js,ts}',
    emit: 'ts',
  },
  metadataProvider: TsMorphMetadataProvider,
};

export default config;
