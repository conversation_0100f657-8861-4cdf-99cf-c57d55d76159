import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230712185006 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "datasource" ("id" serial primary key, "name" varchar(50) not null, \
      "description" varchar(100) null, "uri" varchar(256) not null, "created" timestamptz(6) null, \
      "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "datasource" cascade;');
  }
}
