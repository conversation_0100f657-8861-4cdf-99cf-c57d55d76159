import { <PERSON>, <PERSON>, Param, Post } from "@nestjs/common";
import { DashboardSyncService } from "./dashboard-sync.service";

@Controller("dashboard-sync")
export class DashboardSyncController {
  constructor(private readonly dashboardSyncService: DashboardSyncService) {}

  @Post("start")
  async startSync() {
    await this.dashboardSyncService.scheduleAutoSync();
    return { message: "Auto sync scheduled for all dashboards" };
  }

  @Get("status/:jobId")
  async getJobStatus(@Param("jobId") jobId: string) {
    const job = await this.dashboardSyncService.findOne(jobId);
    if (!job) {
      throw new Error("Job not found");
    }
    return job;
  }
}
