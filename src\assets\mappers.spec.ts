import { mapQueryParamArray } from './mappers';

describe('Mappers', () => {
  describe('Query param array mapper', () => {
    test('empty string should map to an empty list', () => {
      const result = mapQueryParamArray('');

      expect(result).toStrictEqual([]);
    });

    test('single value should map to one element array', () => {
      const result = mapQueryParamArray('5');

      expect(result).toStrictEqual(['5']);
    });

    test('multiple comma separated values should map to an array including them', () => {
      const result = mapQueryParamArray('5,42,55');

      expect(result).toStrictEqual(['5', '42', '55']);
    });

    test('comma separated values with spaces should map to an array including them trimmed', () => {
      const result = mapQueryParamArray(' 5  ,42 ,  55');

      expect(result).toStrictEqual(['5', '42', '55']);
    });
  });
});
