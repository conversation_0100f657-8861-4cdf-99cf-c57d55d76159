import {
  <PERSON>tity,
  ManyToOne,
  OptionalProps,
  PrimaryKey,
  Property,
  Unique,
} from "@mikro-orm/core";
import { TimeVaryingFactor } from "./TimeVaryingFactor.entity";
import { User } from "../../users/domain/user.entity";

@Entity()
@Unique({
  name: "factor_schedule_unique",
  properties: ["factor", "effectiveDate"],
})
export class FactorSchedule {
  [OptionalProps]?: "effectiveDate";

  @PrimaryKey()
  id!: number;

  @ManyToOne({
    entity: () => TimeVaryingFactor,
    fieldName: "factor",
    nullable: true,
  })
  factor?: TimeVaryingFactor;

  @Property({ columnType: "date", defaultRaw: `now()` })
  effectiveDate!: string;

  @Property({ length: 6, nullable: true })
  createdAt?: Date;

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @ManyToOne({
    entity: () => User,
    fieldName: "created_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  createdBy?: User;

  @ManyToOne({
    entity: () => User,
    fieldName: "updated_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  updatedBy?: User;
}
