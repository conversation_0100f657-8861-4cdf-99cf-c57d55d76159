import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  ValidationPipe,
} from "@nestjs/common";
import { ApiOkResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { CustomerId } from "src/customers/domain/customer.entity";
import { listToDtoCollection } from "src/serialization/mappers";
import { User } from "src/users/domain/user.entity";
import { CalcEngineService } from "./calc-engine.service";
import { CalculationTemplate } from "./domain/calculation-template.entity";
import {
  CalcGetAllInstanceDto,
  CalcInstanceDto,
  GetInstanceResponseDto,
} from "./dto/calc-inputs-create.dto";

@Controller({ version: "0", path: "calc-engine" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class CalcEngineController {
  constructor(private readonly calcEngineService: CalcEngineService) {}

  @Get("/templates")
  @HasRole(Role.POWER_USER)
  async getAllCalcTemplates() {
    return listToDtoCollection(
      await this.calcEngineService.getAllCalcTemplates()
    );
  }

  @Post("/templates")
  @HttpCode(201)
  @HasRole(Role.POWER_USER)
  @ApiOkResponse({ type: CalcInstanceDto })
  async createCalcTemplate(
    @AuthUser() authUser: User,
    @Body(ValidationPipe) expressionTemplate: CalculationTemplate
  ) {
    return await this.calcEngineService.createCalcTemplate(
      expressionTemplate,
      authUser
    );
  }

  @Put("/templates/:templateId")
  @HttpCode(204)
  @HasRole(Role.POWER_USER)
  async updateInstance(
    @AuthUser() authUser: User,
    @Param("templateId") templateId: number,
    @Body(ValidationPipe) expressionTemplate: CalculationTemplate
  ) {
    return await this.calcEngineService.updateCalcTemplate(
      templateId,
      expressionTemplate,
      authUser
    );
  }

  @Get("/data-types")
  @HasRole(Role.USER)
  async getAllDataTypes() {
    return listToDtoCollection(await this.calcEngineService.getAllDataTypes());
  }

  @Get("/poll-periods")
  @HasRole(Role.USER)
  async getAllTimePeriods() {
    return listToDtoCollection(
      await this.calcEngineService.getAllTimePeriods()
    );
  }

  @Get("/instances/:customerId")
  @HttpCode(200)
  @ApiOkResponse({ type: GetInstanceResponseDto })
  @HasRole(Role.ADMIN)
  async getAllInstance(
    @Param("customerId") customerId: CustomerId,
    @Query() query: CalcGetAllInstanceDto
  ) {
    return listToDtoCollection(
      await this.calcEngineService.getAllInstances(customerId, query)
    );
  }

  @Put("/templates/instance/:instanceId")
  @HttpCode(204)
  @HasRole(Role.POWER_USER)
  async update(
    @AuthUser() authUser: User,
    @Param("instanceId") instanceId: number,
    @Body(ValidationPipe) body: CalcInstanceDto,
    @CookieToken() headers: Request["headers"]
  ) {
    return await this.calcEngineService.updateCalcInputs(
      instanceId,
      body,
      authUser,
      body.customerId,
      headers
    );
  }

  @Post("/templates/instance/:customerId")
  @HttpCode(201)
  @HasRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Body(ValidationPipe) body: CalcInstanceDto,
    @CookieToken() headers: Request["headers"],
    @Param("customerId") customerId: CustomerId
  ) {
    return await this.calcEngineService.createCalcInputs(
      body,
      authUser,
      headers,
      customerId
    );
  }

  @Get("/measurement/:mesurementId")
  @HasRole(Role.POWER_USER)
  async getCalculation(@Param("mesurementId") mesurementId: number) {
    return await this.calcEngineService.getCalculationByMesurementId(
      mesurementId
    );
  }
}
