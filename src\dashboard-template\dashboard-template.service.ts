import { Injectable } from "@nestjs/common";
import { User } from "src/users/domain/user.entity";
import { DashboardTemplateRepository } from "./repository/dashboard-template.repository";

@Injectable()
export class DashboardTemplateService {
  constructor(
    private readonly dashboardTemplateRepository: DashboardTemplateRepository
  ) {}

  async findAll(assetTypeId?: string, customerId?: number) {
    const dashboardTemplates = await this.dashboardTemplateRepository.findAll(
      assetTypeId,
      customerId
    );
    return {
      total: dashboardTemplates.length,
      items: dashboardTemplates,
    };
  }

  async create(
    dashboard_template: {
      asset_template: number;
      title: string;
      data: string;
      is_global?: boolean;
    },
    authUser: User,
    customerId: number
  ) {
    return this.dashboardTemplateRepository.createDashboardTemplate({
      ...dashboard_template,
      customerId: dashboard_template?.is_global ? undefined : customerId,
      authUser,
    });
  }

  async update(
    dashboard_template: {
      id: number;
      asset_template: number;
      title: string;
      data: string;
      is_global?: boolean;
    },
    authUser: User,
    customerId: number
  ) {
    return this.dashboardTemplateRepository.updateDashboardTemplate({
      ...dashboard_template,
      customerId: dashboard_template?.is_global ? undefined : customerId,
      authUser,
    });
  }

  async delete(id: number) {
    return this.dashboardTemplateRepository.deleteDashboardTemplate(id);
  }

  async findDashboardTemplateById(id: number, customerId: number) {
    return this.dashboardTemplateRepository.findDashboardTemplateById(
      id,
      customerId
    );
  }

  async createDashboardFromTemplate(
    id: number,
    dashboardTemplate: {
      asset_template: number;
      title: string;
      data: string;
      id: number;
    },
    authUser: User,
    customerId: number
  ) {
    return this.dashboardTemplateRepository.createDashboardFromTemplate(
      id,
      customerId
    );
  }

  async asyncDashboardTemplate(id: number, customerId: number) {
    return await this.dashboardTemplateRepository.autoSyncDashboardTempate(
      id,
      customerId
    );
  }
}
