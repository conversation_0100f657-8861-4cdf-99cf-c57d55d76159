import { Migration } from '@mikro-orm/migrations';

export class Migration20240515190246 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "calculation_instance" drop constraint "calc_meas_unique"; ');
    this.addSql('alter table "calculation_template" drop constraint "calc_templ_dtype_fk";');
    this.addSql('alter table "calculation_instance" drop constraint "calculation_fk";');
    this.addSql('alter table "calculation_instance" drop constraint "output_fk" ;');
    this.addSql('alter table "calculation_input" drop constraint "calculation_instance_fk";');
    this.addSql('alter table "calculation_input" drop constraint "calc_instance_meas_fk";');
    this.addSql('alter table "calculation_instance" drop constraint "poll_period_fk";')


    this.addSql('alter table "calculation_instance" add constraint "calculation_instance_output_measurement_unique" unique ("output_measurement");');
    this.addSql('alter table "calculation_template" add constraint "calculation_template_data_type_foreign" foreign key ("data_type") references "data_type" ("id") on update cascade;');
    this.addSql('alter table "calculation_instance" add constraint "calculation_instance_calculation_foreign" foreign key ("calculation") references "calculation_template" ("id") on update cascade;');
    this.addSql('alter table "calculation_instance" add constraint "calculation_instance_output_measurement_foreign" foreign key ("output_measurement") references "measurement" ("id") on update cascade;');
    this.addSql('alter table "calculation_input" add constraint "calculation_input_calculation_instance_foreign" foreign key ("calculation_instance") references "calculation_instance" ("id") on update cascade;');
    this.addSql('alter table "calculation_input" add constraint "calculation_input_measurement_foreign" foreign key ("measurement") references "measurement" ("id") on update cascade on delete set null;');
    this.addSql('alter table "calculation_instance" add constraint "calculation_instance_calculation_period_fk" foreign key (poll_period) references "calculation_period" ("id");');

  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_instance" drop constraint "calculation_instance_calculation_period_fk"');
    this.addSql('alter table "calculation_instance" drop constraint "calculation_instance_output_measurement_unique";');
    this.addSql('alter table "calculation_template" drop constraint "calculation_template_data_type_foreign";');
    this.addSql('alter table "calculation_instance" drop constraint "calculation_instance_calculation_foreign";');
    this.addSql('alter table "calculation_instance" drop constraint "calculation_instance_output_measurement_foreign";');
    this.addSql('alter table "calculation_input" drop constraint "calculation_input_calculation_instance_foreign";');
    this.addSql('alter table "calculation_input" drop constraint "calculation_input_measurement_foreign";');


    this.addSql('alter table "calculation_instance" add constraint "poll_period_fk" foreign key (poll_period) references "calculation_period" ("id");');
    this.addSql('alter table "calculation_instance" add constraint "calc_meas_unique" unique ("output_measurement");');
    this.addSql('alter table "calculation_template" add constraint "calc_templ_dtype_fk" foreign key ("data_type") references "data_type" ("id") on update cascade;');
    this.addSql('alter table "calculation_instance" add constraint "calculation_fk" foreign key ("calculation") references "calculation_template" ("id") on update cascade;');
    this.addSql('alter table "calculation_instance" add constraint "output_fk" foreign key ("output_measurement") references "measurement" ("id") on update cascade;');
    this.addSql('alter table "calculation_input" add constraint "calculation_instance_fk" foreign key ("calculation_instance") references "calculation_instance" ("id") on update cascade;');
    this.addSql('alter table "calculation_input" add constraint "calc_instance_meas_fk" foreign key ("measurement") references "measurement" ("id") on update cascade on delete set null;');
  }

}
