import { Test } from '@nestjs/testing';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Asset } from 'src/assets/domain/asset.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import {
  assetMeasurementFactory,
  measurementBackofficeFactory,
} from '../__tests__/factories';
import { AssetMeasurement } from './asset-measurement.entity';

describe('AssetMeasurement', () => {
  beforeAll(async () => {
    await Test.createTestingModule({
      imports: [createMikroOrmTestModule([Asset, AssetType, Customer])],
    }).compile();
  });

  describe('create', () => {
    test('with valid arguments should create an instance', () => {
      const nasaPumpVoltage = assetMeasurementFactory.createNasaPumpVoltage(34);

      expect(nasaPumpVoltage.tag).toBe('NASA-Pump/voltage');
    });

    describe('tag containing illegal characters should throw an exception', () => {
      const createAssetMeasurementWithTag = (tag: string) => () =>
        new AssetMeasurement({
          tag,
          assetId: 4,
          measurementType:
            measurementBackofficeFactory.createVoltageMeasurementType(),
          dataType: measurementBackofficeFactory.createRealDataType(),
          valueType: measurementBackofficeFactory.createNominalValueType(),
        });

      test('"', () => {
        expect(createAssetMeasurementWithTag('"Pump"voltage')).toThrow(
          'Tag contains invalid characters',
        );
      });

      test("'", () => {
        expect(createAssetMeasurementWithTag("'Pump'voltage")).toThrow(
          'Tag contains invalid characters',
        );
      });

      test('<space>', () => {
        expect(createAssetMeasurementWithTag('Pump voltage')).toThrow(
          'Tag contains invalid characters',
        );
      });
    });
  });

  describe('update', () => {
    test('with valid tag should succeed', () => {
      const nasaPumpVoltage = assetMeasurementFactory.createNasaPumpVoltage(34);

      nasaPumpVoltage.measurement.tag = 'NewNasaTag';

      expect(nasaPumpVoltage.tag).toBe('NewNasaTag');
    });

    describe('tag containing illegal characters should throw an exception', () => {
      const updateAssetMeasurementWithTag = (tag: string) => () => {
        const nasaPumpVoltage =
          assetMeasurementFactory.createNasaPumpVoltage(34);
        nasaPumpVoltage.measurement.tag = tag;
      };

      test('"', () => {
        expect(updateAssetMeasurementWithTag('"Pump"voltage')).toThrow(
          'Tag contains invalid characters',
        );
      });

      test("'", () => {
        expect(updateAssetMeasurementWithTag("'Pump'voltage")).toThrow(
          'Tag contains invalid characters',
        );
      });

      test('<space>', () => {
        expect(updateAssetMeasurementWithTag('Pump voltage')).toThrow(
          'Tag contains invalid characters',
        );
      });
    });
  });
});
