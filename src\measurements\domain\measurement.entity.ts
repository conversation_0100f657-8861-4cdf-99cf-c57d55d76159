import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property, Ref } from "@mikro-orm/core";
import { Metric } from "../../assets/domain/metric.entity";
import { InvalidInputException } from "../../errors/exceptions";
import { User, UserId } from "../../users/domain/user.entity";
import { DataType } from "./data-type.entity";
import { Datasource } from "./datasource.entity";
import { MeasurementType } from "./measurement-type.entity";
import { UnitOfMeasure } from "./unit-of-measure.entity";
import { ValueType } from "./value-type.entity";

export type MeasurementCreationParams = {
  valueType: ValueType;
  dataType: DataType;
  measurementType: MeasurementType;
  unitOfMeasure?: UnitOfMeasure;
  meterFactor?: number;
  datasource?: Datasource;
  description?: string;
  tag: string;
  metric?: Metric;
  writeback?: boolean;
};

export type MeasurementId = number;

@Entity()
export class Measurement {
  constructor(params: MeasurementCreationParams) {
    const {
      tag,
      metric,
      measurementType,
      dataType,
      valueType,
      description,
      unitOfMeasure,
      datasource,
      meterFactor,
      writeback,
    } = params;

    if (this.containsInvalidTagCharacters(tag)) {
      throw new InvalidInputException("Tag contains invalid characters");
    }

    this._tag = tag;
    this.metric = metric;
    this.measurementType = measurementType;
    this.dataType = dataType;
    this.valueType = valueType;
    this.description = description;
    this.unitOfMeasure = unitOfMeasure;
    this.datasource = datasource;
    this.meterFactor = meterFactor;
    this.createdAt = new Date();
    this.enabled = true;
    this.writeback = writeback;
  }

  @PrimaryKey()
  id!: MeasurementId;

  @Property({ length: 150, fieldName: "tag" })
  _tag!: string;

  get tag() {
    return this._tag;
  }

  set tag(newTag: string) {
    if (this.containsInvalidTagCharacters(newTag)) {
      throw new InvalidInputException("Tag contains invalid characters");
    }
    this._tag = newTag;
  }

  @Property({ nullable: true, hidden: true })
  enabled?: boolean;

  @ManyToOne({
    hidden: true,
    eager: true,
    fieldName: "metric",
  })
  metric?: Metric;

  @ManyToOne({
    index: "measurement_m_type_idx",
    joinColumn: "m_type",
    hidden: true,
    eager: true,
  })
  measurementType!: MeasurementType;

  private containsInvalidTagCharacters(tag: string) {
    return /[^a-zA-Z0-9_\-:\/\\ ]/.test(tag);
  }

  @Property({ persist: false, serializedName: "type_id" })
  get typeId() {
    return this.measurementType.id;
  }

  @ManyToOne({
    fieldName: "unit_of_measure",
    nullable: true,
    index: "measurement_uom_idx",
    hidden: true,
    eager: true,
  })
  unitOfMeasure?: UnitOfMeasure;

  @Property({ persist: false, serializedName: "unit_of_measure_id" })
  get unitOfMeasureId() {
    return this.unitOfMeasure?.id;
  }

  @ManyToOne({
    fieldName: "data_type",
    index: "measurement_data_type_idx",
    hidden: true,
    eager: true,
  })
  dataType!: DataType;

  @Property({ persist: false, serializedName: "data_type_id" })
  get dataTypeId() {
    return this.dataType.id;
  }

  @Property({ length: 100, nullable: true })
  description?: string;

  @ManyToOne({
    fieldName: "value_type",
    index: "measurement_value_type_idx",
    hidden: true,
    eager: true,
  })
  valueType!: ValueType;

  @Property({ persist: false, serializedName: "value_type_id" })
  get valueTypeId() {
    return this.valueType.id;
  }

  @ManyToOne({
    fieldName: "datasource",
    nullable: true,
  })
  readonly datasource?: Datasource;

  @Property({
    persist: false,
    fieldName: "datasource",
    serializedName: "datasource_id",
  })
  get datasourceId() {
    return this.datasource?.id;
  }

  @Property({ columnType: "double precision", serializedName: "meter_factor" })
  meterFactor?: number;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @Property({ nullable: true, fieldName: "writeback" })
  writeback?: boolean; // Ensure this matches the primitive type 'boolean'
  
  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "created_by",
  })
  private readonly createdBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "created_by" })
  createdById?: UserId;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;

  @Property({ length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "deleted_by",
  })
  private readonly deletedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "deleted_by" })
  deletedById?: UserId;
}
