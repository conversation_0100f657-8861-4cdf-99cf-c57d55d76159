FROM node:16.20.1-alpine3.17 as build

RUN mkdir -p /opt/brompton-admin-api
WORKDIR /opt/brompton-admin-api 

COPY yarn.lock package.json .
RUN yarn install --prod --frozen-lockfile
COPY . .
RUN yarn build

# runtime stage
FROM node:16.20.1-alpine3.17 as production

ARG CONFIG_PATH=/etc/brompton-admin-api/.production.env

RUN mkdir -p /opt/brompton-admin-api && mkdir -p /etc/brompton-admin-api
WORKDIR /opt/brompton-admin-api

COPY --from=build /opt/brompton-admin-api/.production.env $CONFIG_PATH
COPY --from=build /opt/brompton-admin-api/node_modules node_modules
COPY --from=build /opt/brompton-admin-api/dist dist
COPY --from=build /opt/brompton-admin-api/package.json package.json

ENV MIKRO_ORM_ENV=$CONFIG_PATH
CMD yarn run migrations:up:prod && yarn run start:prod