{"info": {"_postman_id": "8f1131bd-8ead-45e2-9d0e-f47f39b8473f", "name": "BromptonEnergy", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "14604535", "_collection_link": "https://lnotionvision.postman.co/workspace/Team-Workspace~ee54c8b3-0a1f-415b-859c-41d2796ec14d/collection/14604535-8f1131bd-8ead-45e2-9d0e-f47f39b8473f?action=share&source=collection_link&creator=14604535"}, "item": [{"name": "Measurements Backoffice", "item": [{"name": "Get All Data Types", "event": [{"listen": "prerequest", "script": {"exec": ["const cookieJar = pm.cookies.jar();", "cookieJar.get(\"localhost:3030\", \"BE_CSRFToken\", ((error, cookie) => {", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/metadata/data-types", "host": ["{{appUrl}}"], "path": ["v0", "metadata", "data-types"]}}, "response": []}, {"name": "Get All Value Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/value-types", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "value-types"]}}, "response": []}, {"name": "Get All Measurement Types", "event": [{"listen": "prerequest", "script": {"exec": ["const cookieJar = pm.cookies.jar();", "cookieJar.get(\"localhost:3030\", \"BE_CSRFToken\", ((error, cookie) => {", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/metadata/measurement-types", "host": ["{{appUrl}}"], "path": ["v0", "metadata", "measurement-types"]}}, "response": []}, {"name": "Get All Unit of Measure Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/measurement-types/2/units-of-measure", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "measurement-types", "2", "units-of-measure"]}}, "response": []}, {"name": "Get All Units Groups", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/units-groups", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "units-groups"]}}, "response": []}, {"name": "Get All Units Group Units", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/units-groups", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "units-groups"]}}, "response": []}]}, {"name": "Timeseries", "item": [{"name": "Get Current", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/current/2?", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "current", "2"], "query": [{"key": "", "value": null}]}}, "response": []}, {"name": "Get History", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/history/2?start=0&meas_id=2", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "history", "2"], "query": [{"key": "start", "value": "0"}, {"key": "meas_id", "value": "2"}]}}, "response": []}, {"name": "Get Aggregation", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/agg/2?start=0&meas_id=2", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "agg", "2"], "query": [{"key": "start", "value": "0"}, {"key": "meas_id", "value": "2"}]}}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Default Dashboard", "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dashboard_id\": 42,\n    \"user_id\": 2,\n    \"status\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/default", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "default"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Get All Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{appUrl}}/v0/sessions", "host": ["{{appUrl}}"], "path": ["v0", "sessions"]}}, "response": []}, {"name": "Get User Details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/users/me", "host": ["{{appUrl}}"], "path": ["v0", "users", "me"]}}, "response": []}, {"name": "Create Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Apple today\",\n    \"data\": \"{apple:ioioiio}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}:3030/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "port": "3030", "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "9"}]}}, "response": []}, {"name": "Edit Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Apple edited 2\",\n    \"data\": \"{apple1:ioioiio1}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/8/dashboards/118", "host": ["{{appUrl}}"], "path": ["v0", "customers", "8", "dashboards", "118"]}}, "response": []}, {"name": "Get customer logo", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/85/logo", "host": ["{{appUrl}}"], "path": ["v0", "customers", "85", "logo"]}}, "response": []}, {"name": "Update User Preference", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"preferences\": {\n        \"DATE_FORMAT\": \"DD-MM-YYYY\",\n        \"CURRENCY\": \"INR\",\n        \"DEFAULT_CUSTOMER\": \"1\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users/preference", "host": ["{{appUrl}}"], "path": ["v0", "users", "preference"]}}, "response": []}, {"name": "Get User Preference", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/users/preference", "host": ["{{appUrl}}"], "path": ["v0", "users", "preference"]}}, "response": []}, {"name": "Get Dashboard by customer", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "9"}]}}, "response": []}, {"name": "Get Dashboard details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/118", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "118"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Delete Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/128", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "128"], "variable": [{"key": "customerId", "value": ""}]}}, "response": []}, {"name": "Patch User Details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users/me", "host": ["{{appUrl}}"], "path": ["v0", "users", "me"]}}, "response": []}, {"name": "Create User", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"customer_user\",\n    \"password\": \"asdfasdf\",\n    \"first_name\": \"Just\",\n    \"last_name\": \"Customer\",\n    \"scoped_roles\": [{\"role\":\"USER\", \"cusotmer_ids\":[1]}],\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users", "host": ["{{appUrl}}"], "path": ["v0", "users"]}}, "response": []}, {"name": "Create Scoped User", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"scoped_user\",\n    \"password\": \"asdfasdf\",\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"User\",\n    \"scoped_roles\": [{\"role\": \"ADMIN\", \"customer_ids\": [1, 2]}],\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users", "host": ["{{appUrl}}"], "path": ["v0", "users"]}}, "response": []}, {"name": "Create Customer", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"name_id\": \"apple\",\n    \"address\": \"Palo Altro\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers", "host": ["{{appUrl}}"], "path": ["v0", "customers"]}}, "response": []}, {"name": "Get All Customers", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers", "host": ["{{appUrl}}"], "path": ["v0", "customers"]}}, "response": []}, {"name": "Get All Customer Users", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/users", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "users"], "query": [{"key": "user_name", "value": "demo_sc_admin", "disabled": true}, {"key": "email", "value": "demo_sc_admin", "disabled": true}, {"key": "customer_name", "value": "Test_DemoCustomer", "disabled": true}], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Patch Customer User by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/users/5", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "users", "5"]}}, "response": []}, {"name": "Get All Customers by NameId", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/apple", "host": ["{{appUrl}}"], "path": ["v0", "customers", "apple"]}}, "response": []}, {"name": "Get Asset by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Patch Asset by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"dubid<PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Get All Asset Measurements", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2/measurements", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2", "measurements"]}}, "response": []}, {"name": "Get Asset Measurement by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements/14661", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements", "14661"]}}, "response": []}, {"name": "Patch Asset Measurement by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"meter_factor\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements/14664", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements", "14664"]}}, "response": []}, {"name": "Delete Asset Measurement", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2/measurements/9500", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2", "measurements", "9500"]}}, "response": []}, {"name": "Delete Asset", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Get Asset Measurement by Id Location", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/customers/8/assets/348/measurements/22329/location", "host": ["{{appUrl}}"], "path": ["v0", "customers", "8", "assets", "348", "measurements", "22329", "location"]}}, "response": []}, {"name": "Get All Assets by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/82/assets?parentIds=-1,318", "host": ["{{appUrl}}"], "path": ["v0", "customers", "82", "assets"], "query": [{"key": "parentIds", "value": "-1,318"}]}}, "response": []}, {"name": "Get All Asset Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types"]}}, "response": []}, {"name": "Create Asset Type", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Rocket Engine\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types"]}}, "response": []}, {"name": "Get All Asset Type Metrics", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/metrics", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "metrics"]}}, "response": []}, {"name": "Get All Asset Templates", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/asset-templates", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "asset-templates"]}}, "response": []}, {"name": "Create As<PERSON> Templates Instance", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"units_group_id\": 40,\n    \"asset\": {\n        \"tag\": \"Testing Toyota Generator\",\n        \"description\": \"Generator used for testing\",\n        \"parent_ids\": [],\n        \"time_zone\": \"America/Argentina/Buenos_Aires\",\n        \"customer_id\": 8\n    },\n    \"measurements\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/asset-templates/11/instances", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "asset-templates", "11", "instances"]}}, "response": []}, {"name": "Create As<PERSON> Template", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"manufacturer\": \"SpaceX\",\n    \"model_number\": \"Raptor\",\n    \"measurements\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3030/v0/assets-backoffice/asset-types/9/asset-templates", "host": ["localhost"], "port": "3030", "path": ["v0", "assets-backoffice", "asset-types", "9", "asset-templates"]}}, "response": []}, {"name": "Get All Measurement Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/measurement-types", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "measurement-types"]}}, "response": []}, {"name": "Get All Datasources", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/datasources", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "datasources"]}}, "response": []}, {"name": "Get All Asset Time zones", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/time-zones", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "time-zones"]}}, "response": []}, {"name": "Create Asset", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tag\": \"Sub Sub Compressor\",\n    \"type_id\": 2,\n    \"description\": \"Heavy duty compressor\",\n    \"parent_ids\": [],\n    \"is_customer_primary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/2/assets", "host": ["{{appUrl}}"], "path": ["v0", "customers", "2", "assets"]}}, "response": []}, {"name": "Get Asset Measurement by Id Location", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/location-by-measure/22329", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "location-by-measure", "22329"]}}, "response": []}, {"name": "Create Asset Measurement", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tag\": \"Espresso Pressure\",\n    \"type_id\": 6,\n    \"data_type_id\": 2,\n    \"value_type_id\": 1,\n    \"description\": \"Coffee pressure\",\n    \"location_id\":10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const cookieJar = pm.cookies.jar();", "const appCookieDomain = pm.environment.get(\"appCookieDomain\");", "cookieJar.get(appCookieDomain, \"BE-CSRFToken\", ((error, cookie) => {", "    console.log('error', error)", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "password", "value": "3e7daccab1060e5a36b1c04891087c9a", "type": "string"}, {"key": "username", "value": "", "type": "string"}, {"key": "csrfToken", "value": ""}]}