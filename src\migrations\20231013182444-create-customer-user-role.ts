import { Migration } from '@mikro-orm/migrations';

export class Migration20231013182444 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'create table "customer_user_role" ("id" serial primary key, "customer_id" int not null, "user_id" int not null, "role" smallint not null);',
    );
    this.addSql(
      'alter table "customer_user_role" add constraint "customer_user_role_customer_id_user_id_unique" unique ("customer_id", "user_id");',
    );

    this.addSql(
      'alter table "customer_user_role" add constraint "customer_user_role_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "customer_user_role" add constraint "customer_user_role_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;',
    );

    this.addSql('alter table "user" add column "global_role" smallint null;');
    this.addSql('alter table "user" drop column "role_id";');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "customer_user_role" cascade;');

    this.addSql(
      'alter table "user" add column "role_id" int2 not null default null;',
    );
    this.addSql('alter table "user" drop column "global_role";');
  }
}
