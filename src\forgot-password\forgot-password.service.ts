import { Injectable } from "@nestjs/common";
import { ForgotPasswordRepository } from "./repository/forgot-password.repository";

@Injectable()
export class ForgotPasswordSerivce {
  constructor(private readonly forgotPassword: ForgotPasswordRepository) {}

  async forgotUserPassword(emailOrUserName: string) {
    return await this.forgotPassword.findUserByEmailUserNameSendEmail(
      emailOrUserName
    );
  }

  async findUserByTokenAndResetPassowrd(token: string) {
    return await this.forgotPassword.findUserByTokenAndResetPassowrd(token);
  }

  async resetPassword(resetParam: {
    token: string;
    password: string;
    reset_password: string;
  }) {
    return await this.forgotPassword.resetPassword(resetParam);
  }
}
