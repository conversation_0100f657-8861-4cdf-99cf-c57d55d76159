import { Migration } from '@mikro-orm/migrations';

export class Migration20250115135405_createAnnotationTable extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "annotation" ("id" serial primary key, "dashboard" int not null, "measurement_id" int not null, "description" varchar(255) not null, "widget_id" int not null, "time_of_annotation" bigint not null, "value" numeric(10,0) not null, "settings" varchar(65536) not null);');

    this.addSql('alter table "annotation" add constraint "annotation_dashboard_foreign" foreign key ("dashboard") references "dashboard" ("id") on update cascade;');
    this.addSql('alter table "annotation" add constraint "annotation_measurement_id_foreign" foreign key ("measurement_id") references "measurement" ("id") on update cascade;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "annotation" cascade;');
  }

}
