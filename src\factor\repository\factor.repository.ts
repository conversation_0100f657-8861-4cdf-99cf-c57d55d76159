import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { NotFoundException } from "@nestjs/common";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { User } from "src/users/domain/user.entity";
import { FactorSchedule } from "../domain/factor-schedule.entity";
import { FactorTimeOfDayValue } from "../domain/factor-time-of-day-value.entity";
import { FactorType } from "../domain/factor-type.entity";
import { TimeVaryingFactor } from "../domain/TimeVaryingFactor.entity";
import { TimeVaryingFactorCreateDTO } from "../dto/time-varing-factor.dto";
import { FactorTimeOfDayValueDTO } from "../dto/factor-time-of-day-value.dto";
import { TimeVaryingFactorDetailsDTO } from "../dto/time-varing-factor.dto";
import { FactorScheduleDTO } from "../dto/factor-schedule.dto";
import { Loaded } from "@mikro-orm/core";

export class FactorRepository {
  constructor(
    @InjectRepository(FactorType)
    private readonly factorType: EntityRepository<FactorType>,
    @InjectRepository(FactorSchedule)
    private readonly factorSchedule: EntityRepository<FactorSchedule>,
    @InjectRepository(FactorTimeOfDayValue)
    private readonly factorTimeOfDayValue: EntityRepository<FactorTimeOfDayValue>,
    @InjectRepository(TimeVaryingFactor)
    private readonly timeVaryingFactor: EntityRepository<TimeVaryingFactor>,
    @InjectRepository(User)
    private readonly user: EntityRepository<User>,
    @InjectRepository(Measurement)
    private readonly measurement: EntityRepository<Measurement>
  ) {}

  async createFactorType(factorType: FactorType) {
    factorType.createdAt = new Date();
    factorType.updatedAt = new Date();
    this.factorType.persistAndFlush(factorType);
  }

  async getFactorTypes(): Promise<FactorType[]> {
    return this.factorType.findAll();
  }

  async getFactorTypeById(id: number): Promise<FactorType> {
    return this.factorType.findOne({ id: id });
  }

  async getTimeVaryingFactorByMeasure(
    measureId: number
  ): Promise<TimeVaryingFactorDetailsDTO | NotFoundException> {
    const measurement = await this.measurement.findOne({ id: measureId });
    if (!measurement) {
      return new NotFoundException("Measurement not found");
    }
    const timeVariangFactor = await this.timeVaryingFactor.findOne({
      measurement: measureId,
    });
    if (!timeVariangFactor) {
      return new NotFoundException("Time varying factor not found");
    }
    const factorSchedule = await this.factorSchedule.find({
      factor: timeVariangFactor.id,
    });
    if (!factorSchedule || factorSchedule.length === 0) {
      return new NotFoundException("Factor schedule not found");
    }
    const factorTimeOfDayValues: {
      effectiveDate: string;
      factorTimeOfDayValue: FactorTimeOfDayValue[];
    }[] = [];
    for (const schedule of factorSchedule) {
      const factorTimeOfDayValue = await this.factorTimeOfDayValue.find({
        factorSchedule: schedule.id,
      });
      factorTimeOfDayValues.push({
        effectiveDate: schedule.effectiveDate,
        factorTimeOfDayValue,
      });
    }

    return {
      id: timeVariangFactor.id,
      seasonal: timeVariangFactor.seasonal,
      createdAt: timeVariangFactor.createdAt,
      // createdBy: timeVariangFactor.createdBy,
      updatedAt: timeVariangFactor.updatedAt,
      // updatedBy: timeVariangFactor.updatedBy,
      measurement: measurement.id,
      factorType: timeVariangFactor.factorType.id,
      factorSchedule,
      factorTimeOfDayValue: factorTimeOfDayValues,
    };
  }

  public async updateTimeVaryingFactor(
    id: number,
    authUser: User,
    factoryTimeVaring: TimeVaryingFactorCreateDTO
  ) {
    const timeVaryingFactor = await this.timeVaryingFactor.findOne({
      id: id,
    });
    if (!timeVaryingFactor) {
      throw new NotFoundException("Time varying factor not found");
    }
    const factorType = await this.factorType.findOne({
      id: factoryTimeVaring.timeVaryingFactor.factorType,
    });
    if (!factorType) {
      throw new NotFoundException("Factor type not found");
    }
    timeVaryingFactor.factorType = factorType;
    timeVaryingFactor.seasonal = factoryTimeVaring.timeVaryingFactor.seasonal;
    timeVaryingFactor.updatedAt = new Date();
    timeVaryingFactor.updatedBy = authUser;
    await this.timeVaryingFactor.persistAndFlush(timeVaryingFactor);
    const factorSchedule = await this.factorSchedule.find({
      factor: timeVaryingFactor.id,
    });
    const schedules = await Promise.all(
      factorSchedule.map(async (schedule) => {
        return schedule.id;
      })
    );
    // delete all factor time of day value
    await this.factorTimeOfDayValue.nativeDelete({
      factorSchedule: { $in: schedules },
    });
    // delete all factor schedule
    await this.factorSchedule.nativeDelete({
      id: { $in: schedules },
    });
    // return { schedules, factorSchedule };
    if (timeVaryingFactor.id) {
      const factorSchedules = await Promise.all(
        factoryTimeVaring.factorSchedule.map(async (schedules) => {
          const factorSchedule = new FactorSchedule();
          factorSchedule.effectiveDate = schedules.effectiveDate;
          factorSchedule.createdAt = new Date();
          factorSchedule.updatedAt = new Date();
          // if (authUser) {
          //   factorSchedule.createdBy = authUser;
          //   factorSchedule.updatedBy = authUser;
          // }
          factorSchedule.factor = timeVaryingFactor;
          await this.factorSchedule.persistAndFlush(factorSchedule);
          return {
            id: factorSchedule.id,
            effectiveDate: factorSchedule.effectiveDate,
            schedules: factorSchedule,
          };
        })
      );
      const factorTimeOfDayValueToCreate: {
        [key: string]: FactorTimeOfDayValueDTO[];
      } = factoryTimeVaring.factorTimeOfDayValue.reduce(
        (acc, factorTimeOfDay) => {
          const { effectiveDate } = factorTimeOfDay;
          if (!acc[effectiveDate]) {
            acc[effectiveDate] = [];
          }
          acc[effectiveDate].push(factorTimeOfDay);
          return acc;
        },
        {}
      );
      factorSchedules.map((schedules) => {
        if (factorTimeOfDayValueToCreate[schedules.effectiveDate]) {
          factorTimeOfDayValueToCreate[schedules.effectiveDate].map(
            async (factorTimeOfDayValue) => {
              const time = new FactorTimeOfDayValue();
              time.createdAt = new Date();
              time.weekday = factorTimeOfDayValue.weekday;
              time.factorSchedule = schedules.schedules;
              time.updatedAt = new Date();
              // time.createdBy = authUser;
              // time.updatedBy = authUser;
              time.value = factorTimeOfDayValue.value;
              time.timeOfDay = factorTimeOfDayValue.timeOfDay;
              await this.timeVaryingFactor.persistAndFlush(time);
            }
          );
        }
      });
    }
    // return { factorSchedule };
  }
}
