import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryKey,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { Customer } from '../../customers/domain/customer.entity';
import { User } from '../../users/domain/user.entity';

export enum Role {
  ADMIN = 0,
  USER = 1,
  POWER_USER = 2,
}

export type RoleKey = keyof typeof Role;

export type CustomerUserRoleId = number;

@Entity({ tableName: 'customer_user_role' })
@Unique({ properties: ['customer', 'user'] })
export class CustomerUserRole {
  @PrimaryKey()
  id!: CustomerUserRoleId;

  @ManyToOne()
  customer!: Ref<Customer>;

  @ManyToOne()
  user!: Ref<User>;

  @Enum()
  role!: Role;
}
