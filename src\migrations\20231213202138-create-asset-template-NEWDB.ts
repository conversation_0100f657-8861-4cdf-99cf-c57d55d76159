import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20231213202138 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset_template" ("id" serial primary key, "manufacturer" varchar(50) not null, "model_no" varchar(50) not null, "asset_type" int not null);',
    );

    this.addSql(
      'alter table "asset_template" add constraint "asset_type_fk" foreign key ("asset_type") references "asset_type" ("id") on update cascade;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "asset_template" cascade;');
  }
}
