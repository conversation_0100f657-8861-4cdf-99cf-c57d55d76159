import { Injectable } from "@nestjs/common";
import { AlertStatsRepository } from "./repository/alert-stats.repository";

@Injectable()
export class AlertStatsService {
  constructor(private readonly alertStatsRepository: AlertStatsRepository) {}

  async getAlertStatsById({ alert_id }: { alert_id: number }) {
    return await this.alertStatsRepository.getStatByAlert({ id: alert_id });
  }

  async getAllExcursionStats() {
    const excursionStats =
      await this.alertStatsRepository.getAllExcursionStats();
    return excursionStats.map((excursion) => {
      const measureTag = excursion.alert?.measurement?._tag ?? null;
      return {
        ...excursion,
        measureTag,
      };
    });
  }

  async getGroupedStats(
    interval: "daily" | "weekly" | "monthly",
    assetId?: number,
    measureId?: number,
    date?: string
  ) {
    return await this.alertStatsRepository.getGroupedStats(
      interval,
      assetId,
      measureId,
      date
    );
  }
}
