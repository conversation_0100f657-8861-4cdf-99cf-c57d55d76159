import { AssetType } from '../domain/asset-type.entity';
import { Asset, AssetFactory } from '../domain/asset.entity';
import { Metric } from '../domain/metric.entity';

export const assetFactory = {
  createWaterPump: (
    assetId: number,
    customerId = 9,
    assetTypeId = 43,
  ): Asset => {
    const pump = AssetFactory.create({
      tag: 'Water Pump',
      assetTypeId,
      customerId,
      timeZone: 'ART',
    });
    pump.id = assetId;
    return pump;
  },

  createWaterPumpMotor: (assetId: number): Asset => {
    const pump = AssetFactory.create({
      tag: 'Water Pump Motor',
      assetTypeId: 43,
      customerId: 9,
    });
    pump.id = assetId;
    return pump;
  },
};

export const metricFactory = {
  createPumpPressureMetric: (
    metricId: number,
    pumpAssetTypeId: number,
  ): Metric => {
    const pumpAssetType = new AssetType({ name: 'Pump' });
    pumpAssetType.id = pumpAssetTypeId;

    const pumpPressure = new Metric({
      name: 'Pump/Pressure',
      assetType: pumpAssetType,
    });
    pumpPressure.id = metricId;

    pumpPressure.assetType = pumpAssetType;

    return pumpPressure;
  },

  createEngineRpmMetric: (
    metricId: number,
    engineAssetTypeId: number,
  ): Metric => {
    const engineAssetType = new AssetType({ name: 'Engine' });
    engineAssetType.id = engineAssetTypeId;

    const engineRpm = new Metric({
      name: 'Engine/RPM',
      assetType: engineAssetType,
    });
    engineRpm.id = metricId;

    return engineRpm;
  },
};
