import { Modu<PERSON> } from '@nestjs/common';
import { TimeseriesApiController } from './timeseries.controller';
import { AuthModule } from 'src/authentication/auth.module';
import { HttpModule } from '@nestjs/axios';
import { TIMESERIES_CONFIG, TimeSeriesConfig } from './timeseries.config';
import { ConfigService } from '@nestjs/config';

@Module({
  imports: [AuthModule, HttpModule],
  providers: [
    {
      provide: TIMESERIES_CONFIG,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.get<TimeSeriesConfig>(TIMESERIES_CONFIG),
    },
  ],
  controllers: [TimeseriesApiController],
  exports: [],
})
export class TimeseriesModule { }
