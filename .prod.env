# #frontend
# FRONTEND_URL=https://test.brompton.ai

# #server
# SERVER_PORT=8080

# #db
# DB_NAME=dataloggeraws
# DB_HOST=dataloggeraws.clclbj3j3ehf.us-east-1.rds.amazonaws.com
# DB_PORT=5432
# DB_USER=postgres
# DB_PASSWORD=Br0mpt0n!0T
# DB_SSL=true

# #redis
# REDIS_HOST=bromptonenergy.io
# REDIS_PORT=6379

# #auth
# AUTH_JWT_SECRET="this is a very secret secret"
# AUTH_SESSION_DURATION_MIN=43200

# #security
# SECURITY_CORS_ORIGIN_URL=https://brompton.ai
# AUTH_COOKIE_DOMAIN=brompton.ai

# #timeseries-api
# TS_API_HOST=bromptonenergy.io
# TS_API_PORT=28666
# TS_API_SSL=false
# NODE_TLS_REJECT_UNAUTHORIZED = 0

# #Twillio_API
# SEND_NOTIFICATIONS=true
# TWILLIO_ACCOUNT_SID=**********************************
# TWILLIO_AUTH_TOKEN=46910f888ea461bcde388a682d069592
# SMS_FROM_NO=+***********
# TWILLIO_EMAIL_API_TOKEN=*********************************************************************
# EMAIL_FROM=<EMAIL>