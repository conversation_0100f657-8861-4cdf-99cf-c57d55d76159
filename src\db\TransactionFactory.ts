import { EntityManager } from '@mikro-orm/core';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TransactionFactory {
  constructor(private readonly em: EntityManager) {}

  async run<T>(transactionBlock: (em: EntityManager) => Promise<T>) {
    let result: T;
    try {
      result = await this.em.transactional(transactionBlock);
    } catch (err) {
      throw err;
    }

    return result;
  }
}
