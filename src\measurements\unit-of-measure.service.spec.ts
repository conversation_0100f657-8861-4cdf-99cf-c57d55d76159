import { Test } from '@nestjs/testing';
import { UnitOfMeasureService } from './unit-of-measure.service';
import { MeasurementsBackofficeService } from './measurements-backoffice.service';
import { UnitOfMeasure } from './domain/unit-of-measure.entity';
import { getRepositoryToken } from '@mikro-orm/nestjs';

describe('Unit of measure service', () => {
  test('querying by measurement type should use id as filter', async () => {
    const { unitOfMeasureService } = await createUnitOfMeasureService();

    const result = await unitOfMeasureService.getAllByMeasurementType(43);

    expect(result.length).toBe(1);
  });
});

async function createUnitOfMeasureService() {
  const unitOfMeasureRepository = {
    find: (where) =>
      where.measurementType === 43 ? [{ id: 34, name: 'C' }] : [],
  };

  const moduleRef = await Test.createTestingModule({
    providers: [
      { provide: MeasurementsBackofficeService, useValue: jest.fn() },
      {
        provide: getRepositoryToken(UnitOfMeasure),
        useValue: unitOfMeasureRepository,
      },
      UnitOfMeasureService,
    ],
  }).compile();
  const unitOfMeasureService = moduleRef.get(UnitOfMeasureService);
  return { unitOfMeasureService };
}
