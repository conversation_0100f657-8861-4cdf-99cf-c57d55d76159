import { Migration } from '@mikro-orm/migrations';

export class Migration20250509071237_calc_metric_asset_template_update extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "calculation_metric_input" drop constraint "calculation_metric_input_calculation_instance_foreign";');

    this.addSql('alter table "calculation_metric_input" rename column "calculation_instance" to "calculation_metric_instance";');
    this.addSql('alter table "calculation_metric_input" add constraint "calculation_metric_input_calculation_metric_instance_foreign" foreign key ("calculation_metric_instance") references "calculation_metric_instance" ("id") on update cascade;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_metric_input" drop constraint "calculation_metric_input_calculation_metric_instance_foreign";');

    this.addSql('alter table "calculation_metric_input" rename column "calculation_metric_instance" to "calculation_instance";');
    this.addSql('alter table "calculation_metric_input" add constraint "calculation_metric_input_calculation_instance_foreign" foreign key ("calculation_instance") references "calculation_metric_instance" ("id") on update cascade;');
  }

}
