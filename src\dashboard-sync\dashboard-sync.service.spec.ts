import { Test, TestingModule } from '@nestjs/testing';
import { DashboardSyncService } from './dashboard-sync.service';

describe('DashboardSyncService', () => {
  let service: DashboardSyncService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DashboardSyncService],
    }).compile();

    service = module.get<DashboardSyncService>(DashboardSyncService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
