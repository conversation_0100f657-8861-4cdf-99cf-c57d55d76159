import { InjectRepository } from "@mikro-orm/nestjs";
import { Alerts } from "../domain/alert.entity";
import { EntityRepository } from "@mikro-orm/postgresql";
import { AlertCondition } from "../domain/alertCondition.entity";
import { Aggregates } from "../domain/aggregates.entity";
import { AlertThresholdType } from "../domain/alertThresholdType.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { Asset } from "src/assets/domain/asset.entity";
import { Periods } from "../domain/periods.entity";
import { AlertDTO } from "../dto/alert.dto";
import { NotFoundError } from "@mikro-orm/core";

export class AlertRepository {
  constructor(
    @InjectRepository(Alerts)
    private readonly alert: EntityRepository<Alerts>,
    @InjectRepository(AlertCondition)
    private readonly alertCondition: EntityRepository<AlertCondition>,
    @InjectRepository(Aggregates)
    private readonly aggregates: EntityRepository<Aggregates>,
    @InjectRepository(AlertThresholdType)
    private readonly alertThresholdType: EntityRepository<AlertThresholdType>,
    @InjectRepository(Measurement)
    private readonly measurement: EntityRepository<Measurement>,
    @InjectRepository(Asset)
    private readonly asset: EntityRepository<Asset>,
    @InjectRepository(Periods)
    private readonly periods: EntityRepository<Periods>
  ) {}

  async createAlert(alert: Alerts): Promise<Alerts> {
    this.alert.persistAndFlush(alert);
    return alert;
  }

  async validateAlert(alert: AlertDTO) {
    const agg = await this.aggregates.findOne(alert.agg);
    if (!agg) throw new NotFoundError("Aggregates not found");
    const period = await this.aggregates.findOne({
      id: alert.agg,
    });
    if (!period) throw new NotFoundError("Period not found");
    const thresholdType = await this.aggregates.findOne({
      id: alert.thresholdType,
    });
    if (!thresholdType) throw new NotFoundError("Threshold type not found");
    const condition = await this.aggregates.findOne({
      id: alert.condition,
    });
    if (!condition) throw new NotFoundError("Condition not found");
    const measurement = await this.measurement.findOne({
      id: alert.measurement,
    });
    if (!measurement) throw new NotFoundError("Measurement not found");
    const asset = await this.asset.findOne({
      id: alert.asset,
    });
    if (!asset) throw new NotFoundError("Asset not found");
    return {
      agg,
      period,
      thresholdType,
      condition,
      measurement,
      asset,
    };
  }
}
