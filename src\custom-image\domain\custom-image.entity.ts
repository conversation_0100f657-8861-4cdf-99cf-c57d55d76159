import { <PERSON><PERSON>ty, ManyTo<PERSON>ne, PrimaryKey, Property, Ref } from "@mikro-orm/core";
import { Customer } from "src/customers/domain/customer.entity";
import { User } from "src/users/domain/user.entity";

@Entity()
export class CustomImage {
  @PrimaryKey()
  id: number;

  @ManyToOne({
    entity: () => Customer,
    fieldName: "customer",
  })
  customer: Customer;

  @Property({ type: "text", nullable: true })
  logo?: string;

  @Property({ length: 6, hidden: true })
  createdAt = new Date();

  @ManyToOne({ hidden: true, nullable: true, fieldName: "created_by" })
  createdBy?: Ref<User>;

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;
}
