import { Test, TestingModule } from "@nestjs/testing";
import { SessionsApiController } from "./sessions.controller";
import { AuthService } from "./auth.service";
import { INestApplication } from "@nestjs/common";
import request from "supertest";
import authConfiguration from "./auth.config";
import { ConfigType } from "@nestjs/config";

// ✅ Mock guard to bypass LocalAuthGuard
class MockLocalAuthGuard {
  canActivate(context) {
    const req = context.switchToHttp().getRequest();
    req.user = { id: 1, email: "<EMAIL>" }; // simulate a logged-in user
    return true;
  }
}

describe("SessionsApiController", () => {
  let app: INestApplication;
  let authServiceMock: Partial<AuthService>;

  const authConfigMock: ConfigType<typeof authConfiguration> = {
    accessTokenHeader: "access-token",
    csrfTokenHeader: "x-csrf-token",
    sessionDurationMinutes: 15,
    cookieDomain: "localhost",
    jwtSecret: "mock-secret",
    activeCustomerKeyId: "mock-key-id",
  };

  beforeEach(async () => {
    authServiceMock = {
      login: jest.fn().mockResolvedValue({
        accessToken: "mockAccessToken",
        csrfToken: "mockCsrfToken",
      }),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [SessionsApiController],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        {
          provide: authConfiguration.KEY,
          useValue: authConfigMock,
        },
      ],
    })
      .overrideGuard(require("./infra/local-auth.guard").LocalAuthGuard)
      .useClass(MockLocalAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it("should login and set cookies", async () => {
    const response = await request(app.getHttpServer())
      .post("/sessions")
      .send();

    expect(response.status).toBe(201);
    expect(authServiceMock.login).toHaveBeenCalled();
    expect(response.body).toEqual({
      access_token: "mockAccessToken",
      csrf_token: "mockCsrfToken",
    });

    const cookies = response.headers["set-cookie"];
    expect(cookies).toEqual(
      expect.arrayContaining([
        expect.stringContaining(`${authConfigMock.accessTokenHeader}=`),
        expect.stringContaining(`${authConfigMock.csrfTokenHeader}=`),
      ])
    );
  });

  it("should clear cookies on logout", async () => {
    const response = await request(app.getHttpServer())
      .post("/sessions/invalidate")
      .send();

    expect(response.status).toBe(201);
    expect(response.body).toEqual({
      message: "Session invalidated successfully",
    });

    const cookies = response.headers["set-cookie"];
    expect(cookies).toEqual(
      expect.arrayContaining([
        expect.stringContaining(`${authConfigMock.accessTokenHeader}=;`),
        expect.stringContaining(`${authConfigMock.csrfTokenHeader}=;`),
      ])
    );
  });
});
