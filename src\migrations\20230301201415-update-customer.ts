import { Migration } from '@mikro-orm/migrations';

export class Migration20230301201415 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "customer" add column "name_id" varchar(50);');

    this.addSql(
      'alter table "customer" alter column "enabled" type boolean using ("enabled"::boolean);',
    );

    this.addSql(
      'alter table "customer" alter column "enabled" set default true;',
    );

    this.addSql(
      'alter table "customer" add column "created_at" timestamptz(6) null, \
      add column "updated_at" timestamptz(6) null, add column "created_by" int null, \
      add column "updated_by" int null;',
    );

    this.addSql(
      'alter table "customer" add constraint "customer_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "customer" add constraint "customer_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );

    this.addSql('alter table "customer" drop column "created";');
    this.addSql('alter table "customer" drop column "updated";');
    this.addSql('alter table "customer" drop column "createdby";');
    this.addSql('alter table "customer" drop column "updatedby";');

    this.addSql('alter table "customer" drop constraint "customer_name_key";');

    this.addSql(
      'update "customer" set "name_id" = regexp_replace(lower("name"), \'[^a-z0-9_-]+\', \'_\', \'g\');',
    );

    this.addSql('alter table "customer" alter column "name_id" set not null;');

    this.addSql(
      'alter table "customer" add constraint "customer_name_id_unique" unique ("name_id");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "customer" drop constraint "customer_created_by_foreign";',
    );
    this.addSql(
      'alter table "customer" drop constraint "customer_updated_by_foreign";',
    );

    this.addSql(
      'alter table "customer" add column "created" timestamptz null default null, add column "updated" timestamptz null default null, add column "createdby" int4 null default null, add column "updatedby" int4 null default null;',
    );
    this.addSql('alter table "customer" drop column "created_at";');
    this.addSql('alter table "customer" drop column "updated_at";');
    this.addSql('alter table "asset_type" drop column "created_by";');
    this.addSql('alter table "asset_type" drop column "updated_by";');

    this.addSql(
      'alter table "customer" drop constraint "customer_name_id_unique";',
    );
    this.addSql('alter table "customer" drop column "name_id";');
    this.addSql(
      'alter table "customer" add constraint "customer_name_key" unique ("name");',
    );
  }
}
