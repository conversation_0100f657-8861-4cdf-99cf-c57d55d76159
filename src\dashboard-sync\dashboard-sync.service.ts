// import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";
// import { Queue } from "bullmq";
import { CustomerService } from "src/customers/customer.service";
import { DashboardRepository } from "src/dashboards/repository/dashboard.repository";
import { DashboardSyncRepository } from "./repository/dashboard-sync.repository";

@Injectable()
export class DashboardSyncService {
  constructor(
    // @InjectQueue("dashboard-sync") private readonly queue: Queue,
    private readonly jobRepo: DashboardSyncRepository,
    private readonly dashboardRepo: DashboardRepository,
    private readonly customerService: CustomerService
  ) {}

  // Periodically fetch customers and dashboards and sync them
  async scheduleAutoSync() {
    const customers = await this.customerService.getAll({
      is_logo: "false",
    }); // You need a method to fetch customers

    for (const customer of customers) {
      const dashboards = await this.dashboardRepo.findAllByCustomerId(
        customer.id,
        2
      );

      for (const dashboard of dashboards) {
        const job = await this.jobRepo.createJob(customer.id, dashboard.id);
        // await this.queue.add("sync-dashboard", {
        //   jobId: job.id,
        //   customerId: customer.id,
        //   dashboardId: dashboard.id,
        // });
      }
    }
  }

  async findOne(jobId: string) {
    return await this.jobRepo.findOne(jobId);
  }
}
