import { EntityRepository } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { DashboardSyncJob } from "../domain/dashboard-sync.entity";

export class DashboardSyncRepository {
  constructor(
    @InjectRepository(DashboardSyncJob)
    private dashboardSync: EntityRepository<DashboardSyncJob>
  ) {}
  async createJob(
    customerId: number,
    dashboardId: number
  ): Promise<DashboardSyncJob> {
    const job = this.dashboardSync.create({ customerId, dashboardId });
    await this.dashboardSync.persistAndFlush(job);
    return job;
  }

  async updateJobProgress(id: string, progress: number) {
    await this.dashboardSync.nativeUpdate(
      { id },
      { progress, updatedAt: new Date() }
    );
  }

  async completeJob(id: string, result: Record<string, any>) {
    await this.dashboardSync.nativeUpdate(
      { id },
      {
        status: "completed",
        progress: 100,
        result,
        updatedAt: new Date(),
      }
    );
  }

  async failJob(id: string, error: string) {
    await this.dashboardSync.nativeUpdate(
      { id },
      {
        status: "failed",
        result: { error },
        updatedAt: new Date(),
      }
    );
  }

  async findOne(id: string): Promise<DashboardSyncJob | null> {
    return this.dashboardSync.findOne({ id });
  }
}
