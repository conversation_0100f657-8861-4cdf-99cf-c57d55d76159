import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230315145931 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset_hier" ("id" serial primary key, "child" int not null, \
     "parent" int null, "is_default" boolean null, "created" timestamptz(6) null, \
     "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "asset_hier" add constraint "pa_chi_unique" unique ("child", "parent");',
    );

    this.addSql(
      'alter table "asset_hier" add constraint "child_fk" foreign key ("child") references "asset" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_hier" add constraint "parent_fk" foreign key ("parent") references "asset" ("id") on update cascade on delete set null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "asset_hier" cascade;');
  }
}
