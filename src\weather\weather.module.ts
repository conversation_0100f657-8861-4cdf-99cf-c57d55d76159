import { Module, forwardRef } from '@nestjs/common';
import { WeatherController } from './weather.controller';
import { WeatherService } from './weather.service';
import { SecurityModule } from 'src/security/security.module';
import { AuthModule } from 'src/authentication/auth.module';

@Module({
  imports: [
    SecurityModule,
    forwardRef(() => AuthModule),
  ],
  controllers: [WeatherController],
  providers: [WeatherService],
})
export class WeatherModule { }
