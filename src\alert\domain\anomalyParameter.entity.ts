import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON>, Property } from "@mikro-orm/core";
import { Alerts } from "./alert.entity";

@Entity({ tableName: "anomaly_parameter" })
export class AnomalyParameter {
  @PrimaryKey()
  alert!: number;

  @OneToOne(() => Alerts, (alert) => alert.anomalyParameter, {
    nullable: false,
    owner: true,
    unique: true,
    fieldName: "alert",
    mappedBy: "anomalyParameter",
  })
  alertRelation!: Alerts;

  @Property({ fieldName: "learning_period", type: "interval", nullable: false })
  learningPeriod!: string;

  @Property({ fieldName: "include_velocity", type: "boolean", nullable: false })
  includeVelocity!: boolean;

  @Property({ fieldName: "include_momentum", type: "boolean", nullable: false })
  includeMomentum!: boolean;

  @Property({
    fieldName: "created_at",
    type: "timestamptz",
    nullable: true,
    defaultRaw: "now()",
  })
  createdAt?: Date = new Date();

  @Property({
    fieldName: "updated_at",
    type: "timestamptz",
    nullable: true,
    defaultRaw: "now()",
  })
  updatedAt?: Date = new Date();

  @Property({ fieldName: "created_by", type: "integer", nullable: true })
  createdBy?: number | null;

  @Property({ fieldName: "updated_by", type: "integer", nullable: true })
  updatedBy?: number | null;
}
