import { Migration } from '@mikro-orm/migrations';

export class Migration20230712190829 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "datasource" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "datasource" add constraint "datasource_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "datasource" add constraint "datasource_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "datasource" drop column "created";');
    this.addSql('alter table "datasource" drop column "updated";');
    this.addSql('alter table "datasource" drop column "createdby";');
    this.addSql('alter table "datasource" drop column "updatedby";');
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "datasource" drop constraint "datasource_created_by_foreign";',
    );
    this.addSql(
      'alter table "datasource" drop constraint "datasource_updated_by_foreign";',
    );

    this.addSql(
      'alter table "datasource" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "datasource" drop column "created_at";');
    this.addSql('alter table "datasource" drop column "updated_at";');
    this.addSql('alter table "datasource" drop column "created_by";');
    this.addSql('alter table "datasource" drop column "updated_by";');
  }
}
