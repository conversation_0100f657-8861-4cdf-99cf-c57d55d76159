import { Migration } from '@mikro-orm/migrations';

export class Migration20231213203059 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template" drop constraint "asset_type_fk";',
    );

    this.addSql(
      'alter table "asset_template" add constraint "asset_template_asset_type_foreign" foreign key ("asset_type") references "asset_type" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template" drop constraint "asset_template_asset_type_foreign";',
    );

    this.addSql(
      'alter table "asset_template" add constraint "asset_type_fk" foreign key ("asset_type") references "asset_type" ("id") on update cascade;',
    );
  }
}
