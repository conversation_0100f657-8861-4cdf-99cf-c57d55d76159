import { MikroOrmModule } from "@mikro-orm/nestjs";
import { forwardRef, Logger, Module } from "@nestjs/common";
import { DashboardTemplateController } from "./dashboard-template.controller";
import { DashboardTemplateService } from "./dashboard-template.service";
import { DashboardTemplate } from "./domain/dashboard-template.entity";
import { HttpModule } from "@nestjs/axios";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { AuthModule } from "src/authentication/auth.module";
import { Customer } from "src/customers/domain/customer.entity";
import { SecurityModule } from "../security/security.module";
import { DashboardTemplateRepository } from "./repository/dashboard-template.repository";

@Module({
  controllers: [DashboardTemplateController],
  providers: [
    DashboardTemplateRepository,
    DashboardTemplateService,
    {
      provide: Logger,
      useValue: new Logger(DashboardTemplateService.name),
    },
  ],
  imports: [
    MikroOrmModule.forFeature([DashboardTemplate, Customer, AssetTemplate]),
    SecurityModule,
    HttpModule,
    forwardRef(() => AuthModule),
  ],
  exports: [DashboardTemplateService],
})
export class DashboardTemplateModule {}
