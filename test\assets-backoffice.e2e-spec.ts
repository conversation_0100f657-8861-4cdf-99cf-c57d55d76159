import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { loginUser, setupApp } from './test-utils';
import request from 'supertest';
import {
  deleteAssetType,
  createAssetType,
  assetFixtureFactory,
} from './fixtures/asset.fixture';
import { createSuperUser, deleteUser } from './fixtures/user.fixture';

describe('/assets-backoffice', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let httpClient: request.SuperAgentTest;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    await deleteUser(testingModule, 'testuser');
    await createSuperUser(testingModule);

    httpClient = await loginUser(
      app.getHttpServer(),
      'testuser',
      'testpassword',
    );
  });

  afterAll(async () => {
    await deleteUser(testingModule, 'testuser');
    await app.close();
  });

  describe('GET .../asset-types', () => {
    describe('two asset types', () => {
      let response: request.Response;
      let bikeEngineTypeId: number;
      let carEngineTypeId: number;

      beforeAll(async () => {
        await deleteAssetType(testingModule, 'engine');

        bikeEngineTypeId = (await createAssetType(testingModule, 'Bike Engine'))
          .id;
        carEngineTypeId = (await createAssetType(testingModule, 'Car Engine'))
          .id;

        response = await httpClient.get('/v0/assets-backoffice/asset-types');
      });

      afterAll(async () => {
        await deleteAssetType(testingModule, 'bike engine');
        await deleteAssetType(testingModule, 'car engine');
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with two elements', () => {
        expect(response.body.total).toBe(2);
        expect(response.body.items.length).toBe(2);
      });

      it('should expose only visible asset type fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          new Set(['id', 'parent_type_id', 'name']),
        );
      });

      it('should return asset types with all fields', () => {
        const bikeEngineAsset = response.body.items[0];
        const carEngineAsset = response.body.items[1];
        expect(bikeEngineAsset.id).toBe(bikeEngineTypeId);
        expect(bikeEngineAsset.name).toBe('Bike Engine');
        expect(bikeEngineAsset.parent_type_id).toBeNull();
        expect(carEngineAsset.id).toBe(carEngineTypeId);
        expect(carEngineAsset.parent_type_id).toBeNull();
        expect(carEngineAsset.name).toBe('Car Engine');
      });
    });
  });
  describe('GET .../asset-types/{assetTypeId}/metrics', () => {
    let enginePowerMetricFixture: Awaited<
      ReturnType<typeof assetFixtureFactory.createEnginePowerMetric>
    >;
    let response: request.Response;

    beforeAll(async () => {
      enginePowerMetricFixture =
        await assetFixtureFactory.createEnginePowerMetric(testingModule);

      response = await httpClient.get(
        `/v0/assets-backoffice/asset-types/${enginePowerMetricFixture.engineAssetTypeId}/metrics`,
      );
    });

    afterAll(async () => {
      await enginePowerMetricFixture.cleanUp();
    });

    it('should return a 200', () => {
      expect(response.statusCode).toBe(200);
    });

    it('should return a collection with one element', () => {
      expect(response.body.total).toBe(1);
      expect(response.body.items.length).toBe(1);
    });

    it('should expose only visible metric fields', () => {
      expect(new Set(Object.keys(response.body.items[0]))).toEqual(
        new Set(['id', 'name']),
      );
    });

    it('should return asset types with all fields', () => {
      const powerMetric = response.body.items[0];
      expect(powerMetric.id).toBe(enginePowerMetricFixture.powerMetricId);
      expect(powerMetric.name).toBe('Power');
    });
  });
});
