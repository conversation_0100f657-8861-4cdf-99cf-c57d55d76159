import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Inject,
  Injectable,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { Request } from "express";
import authConfiguration from "src/authentication/auth.config";
import { AuthService } from "../auth.service";
import { ExceptionMessages } from "src/errors/exceptions.contants";

@Injectable()
export class CsrfGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();

    if (request && request.cookies) {
      const accessToken = request.cookies[this.authConfig.accessTokenHeader];
      const csrfCookieToken = request.cookies[this.authConfig.csrfTokenHeader];
      const csrfHeaderToken = request.header(
        this.authConfig.csrfTokenHeader.toLowerCase()
      );

      if (
        csrfHeaderToken === undefined ||
        csrfHeaderToken !== csrfCookieToken ||
        !this.authService.isValidCsrfToken(accessToken, csrfHeaderToken)
      ) {
        // throw new HttpException("Failed CSRF validation", 401);
        throw new HttpException(
          ExceptionMessages.UNAUTHORIZED.message,
          ExceptionMessages.UNAUTHORIZED.statusCode
        );
      }
    }

    return true;
  }
}
