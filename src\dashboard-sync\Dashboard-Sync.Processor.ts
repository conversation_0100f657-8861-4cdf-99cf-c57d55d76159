import { InjectRepository } from "@mikro-orm/nestjs";
// import { Process, Processor } from "@nestjs/bull";
// import { Job } from "bullmq";
import { DashboardRepository } from "src/dashboards/repository/dashboard.repository";
import { DashboardSyncRepository } from "./repository/dashboard-sync.repository";

// @Processor("dashboard-sync")
export class DashboardSyncProcessor {
  constructor(
    private readonly jobRepo: DashboardSyncRepository,
    @InjectRepository(DashboardRepository)
    private dashboardRepo: DashboardRepository
  ) {}

  // @Process("sync-dashboard")
  // async handleJob(job: Job) {
  //   const { jobId, customerId, dashboardId } = job.data;
  //   console.log(`Processing dashboard sync job ${jobId}...`);

  //   try {
  //     await this.jobRepo.updateJobProgress(jobId, 10);

  //     const result = await this.dashboardRepo.autoSyncDashboard(
  //       customerId,
  //       dashboardId
  //     );

  //     await this.jobRepo.updateJobProgress(jobId, 50);
  //     await this.jobRepo.completeJob(jobId, result);

  //     console.log(`Job ${jobId} completed`);
  //   } catch (error: any) {
  //     await this.jobRepo.failJob(jobId, error.message);
  //     console.error(`Error processing job ${jobId}:`, error);
  //   }
  // }
}
