import { Inject, Injectable } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { Request } from "express";
import { Strategy } from "passport-jwt";
import authConfiguration from "src/authentication/auth.config";
import { User } from "src/users/domain/user.entity";
import { UserService } from "src/users/user.service";

export type JwtPayload = {
  username: string;
  sub: number;
  roles: {
    ADMIN: number[];
    USER: number[];
    POWER_USER: number[];
  };
};

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly userService: UserService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {
    super({
      jwtFromRequest: JwtStrategy.cookieExtractor(authConfig.accessTokenHeader),
      ignoreExpiration: false,
      secretOrKey: authConfig.jwtSecret,
    });
  }

  async validate(payload: JwtPayload): Promise<User | null> {
    const user = await this.userService.findByUsername(payload.username);

    return user !== null && user.enabled ? user : null;
  }

  static cookieExtractor(accessTokenHeader: string) {
    return (req: Request) => {
      let token = null;

      if (req && req.cookies) {
        token = req.cookies[accessTokenHeader];
      }

      return token;
    };
  }
}
