import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { TimeSeriesAggregationType } from "redis";
import { catchError, firstValueFrom } from "rxjs";
import { AssetService } from "src/assets/asset.service";
import { CustomerService } from "src/customers/customer.service";
import { InvalidInputException } from "src/errors/exceptions";
import { AssetMeasurement } from "./domain/asset-measurement.entity";
import { DataType } from "./domain/data-type.entity";
@Injectable()
export class TimeSeriesService {
  private fast_api = "";
  constructor(
    private readonly assetService: AssetService,
    private readonly customerService: CustomerService,
    private readonly httpService: HttpService,
    private readonly logger: Logger,
    private configService: ConfigService
  ) {
    this.fast_api = this.configService.get("fastApi");
  }

  private readonly supportedAggregations = [
    TimeSeriesAggregationType.AVG,
    TimeSeriesAggregationType.MAX,
    TimeSeriesAggregationType.MIN,
    TimeSeriesAggregationType.VAR_P,
    TimeSeriesAggregationType.TWA,
  ];

  async create(
    assetMeasurement: AssetMeasurement,
    customerId: number,
    headers?: Request["headers"],
    calculation?: boolean,
    writeback?: boolean
  ) {
    // if measurement has a datasource, don't create time series
    if (
      !calculation &&
      (assetMeasurement.measurement.datasource ||
        assetMeasurement.measurement.datasource?.name === "Calculation")
    ) {
      return;
    }

    const measurement = assetMeasurement.measurement;
    const measurementType = measurement.measurementType;
    const dataType = measurement.dataType;
    const valueType = measurement.valueType;
    const unitName = measurement.unitOfMeasure?.name ?? "-";

    const asset = await this.assetService.findById(assetMeasurement.assetId);
    if (asset == null) {
      throw new InvalidInputException("Asset not found");
    }

    const customer = await this.customerService.findById(customerId);
    if (customer == null) {
      throw new InvalidInputException("Customer not found");
    }

    const measurementTypeLabel =
      valueType?.name.toLowerCase() !== "nominal"
        ? `${measurementType.name} ${valueType.name}`
        : measurementType.name;

    const measurementKey = assetMeasurement.measurementId.toString();

    const timeSeriesLabels = {
      tag: assetMeasurement.tag,
      meas_type: measurementTypeLabel,
      equipment: asset.tag,
      data_type: dataType.name,
      cust_id: customer.id.toString(),
      customer: customer.name,
      meter_factor: assetMeasurement.meterFactor?.toString() ?? "nan",
      timezone: asset.timeZone ?? "UTC",
      units: unitName,
    };
    try {
      const { data_type, meter_factor, ...streamLabels } = timeSeriesLabels;
      const data = await this.httpProxy(
        `${this.fast_api}/api/v1_0/timeseries/measurement/${customer.id}/${measurementKey}?writeback=${writeback}`,
        timeSeriesLabels,
        {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        }
      );
    } catch (error) {
      this.logger.error("Error creating Measurement", error);
      throw new InvalidInputException("Error creating Measurement");
    }
  }

  private async httpProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .post(url, body, {
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            this.logger.error(
              `Error in HTTP request:
              URL: ${url}
              Request Body: ${JSON.stringify(body, null, 2)}
              Error Response: ${JSON.stringify(
                error.response?.data || error.message,
                null,
                2
              )}
              Status: ${error.response?.status}`,
              error
            );
            throw new InvalidInputException("Error creating Measurement");
          })
        )
    );
    this.logger.log("Data", data);
    return data;
  }

  private async httpUpdateMeasureProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .put(url, body, {
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            this.logger.error(
              `Error in HTTP update request:
              URL: ${url}
              Request Body: ${JSON.stringify(body, null, 2)}
              Error Response: ${JSON.stringify(
                error.response?.data || error.message,
                null,
                2
              )}
              Status: ${error.response?.status}`,
              error
            );
            throw new InvalidInputException("Error updating Measurement");
          })
        )
    );
    this.logger.log("Url ", url);
    this.logger.log("Data ", data);
    return data;
  }

  private isStreamDataType(dataType: DataType): boolean {
    const streamDataTypes = ["string", "boolean"];
    return streamDataTypes.includes(dataType.name.toLowerCase());
  }

  async update(
    assetMeasurement: AssetMeasurement,
    customerId: number,
    headers?: Request["headers"],
    calculation?: boolean
  ) {
    if (
      !calculation &&
      (assetMeasurement.measurement.datasource ||
        assetMeasurement.measurement.datasource?.name === "Calculation")
    ) {
      return;
    }
    if (this.isStreamDataType(assetMeasurement.measurement.dataType)) {
      return;
    }

    const asset = await this.assetService.findById(assetMeasurement.assetId);
    if (asset == null) {
      throw new InvalidInputException("Asset not found");
    }

    const customer = await this.customerService.findById(customerId);
    const measurementKey = assetMeasurement.measurement.id.toString();

    const measurement = assetMeasurement.measurement;
    const measurementType = measurement.measurementType;
    const valueType = measurement.valueType;
    const measurementTypeLabel =
      valueType?.name.toLowerCase() !== "nominal"
        ? `${measurementType.name} ${valueType.name}`
        : measurementType.name;
    const unitName = measurement.unitOfMeasure?.name ?? "-";

    const labels: { [key: string]: string } = {
      tag: assetMeasurement.tag,
      meas_type: measurementTypeLabel,
      equipment: asset.tag,
      meter_factor: assetMeasurement.meterFactor?.toString() ?? "nan",
      units: unitName,
      timezone: asset.timeZone ?? "UTC",
    };

    if (asset.timeZone) {
      labels.timezone = asset.timeZone ?? "UTC";
    }
    await this.httpUpdateMeasureProxy(
      `${this.fast_api}/api/v1_0/timeseries/measurement/${customer.id}/${measurementKey}?writeback=${assetMeasurement.writeback}`,
      labels,
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );
  }
}
