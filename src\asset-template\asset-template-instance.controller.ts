import { Body, Controller, Param, Post, UseGuards } from "@nestjs/common";
import { ApiCreatedResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "src/users/domain/user.entity";
import { AssetTemplateInstanceService } from "./asset-template-instance.service";
import {
  AssetTemplateInstanceCreationDto,
  AssetTemplateInstanceDto,
} from "./dto/asset-template-instance.dto";
import { AssetTemplateInstanceMapper } from "./dto/asset-template-instance.mapper";

@Controller({
  version: "0",
  path: "assets-backoffice/asset-types/:assetTypeId/asset-templates/:assetTemplateId/instances",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetTemplateInstanceApiController {
  constructor(
    private readonly assetTemplateInstanceService: AssetTemplateInstanceService,
    private readonly assetTemplateInstanceMapper: AssetTemplateInstanceMapper
  ) {}

  @Post()
  @ApiCreatedResponse({ type: AssetTemplateInstanceDto })
  @HasRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"],
    @Param("assetTypeId") assetTypeId: string,
    @Param("assetTemplateId") assetTemplateId: string,
    @Body() newInstance: AssetTemplateInstanceCreationDto
  ) {
    const templateInstance = await this.assetTemplateInstanceService.create(
      Number(assetTemplateId),
      {
        ...newInstance.asset,
        assetTypeId: Number(assetTypeId),
        customerId: newInstance.asset.customer_id,
        parentIds: newInstance.asset.parent_ids,
        timeZone: newInstance.asset.time_zone,
      },
      newInstance.measurements
        .map(this.assetTemplateInstanceMapper.measurementOverridesToDomain)
        .reduce((measurmentOverrides, measurementOverride) => {
          measurmentOverrides.set(
            measurementOverride.metricId,
            measurementOverride
          );

          return measurmentOverrides;
        }, new Map()),
      newInstance.units_group_id,
      authUser.id,
      headers
    );

    return this.assetTemplateInstanceMapper.toDto(templateInstance);
  }
}
