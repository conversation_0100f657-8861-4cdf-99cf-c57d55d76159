import { Migration } from '@mikro-orm/migrations';

export class Migration20230421185616 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_measurement" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );

    this.addSql('alter table "asset_measurement" drop constraint "meas_fk";');
    this.addSql('alter table "asset_measurement" drop constraint "asset_fk";');
    this.addSql(
      'alter table "asset_measurement" drop constraint "location_fk";',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_asset_foreign" foreign key ("asset") references "asset" ("id") on update cascade on delete cascade;',
    );
    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_measurement_foreign" foreign key ("measurement") references "measurement" ("id") on update cascade on delete cascade;',
    );
    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_location_foreign" foreign key ("location") references "location" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "asset_measurement" drop column "created";');
    this.addSql('alter table "asset_measurement" drop column "updated";');
    this.addSql('alter table "asset_measurement" drop column "createdby";');
    this.addSql('alter table "asset_measurement" drop column "updatedby";');
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_measurement" add constraint "meas_fk" foreign key (measurement) references "measurement" ("id");',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "asset_fk" foreign key (measurement) references "asset" ("id");',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "location_fk" foreign key (measurement) references "location" ("id");',
    );

    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_measurement_foreign";',
    );
    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_asset_foreign";',
    );
    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_location_foreign";',
    );

    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_created_by_foreign";',
    );
    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_updated_by_foreign";',
    );

    this.addSql(
      'alter table "asset_measurement" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "asset_measurement" drop column "created_at";');
    this.addSql('alter table "asset_measurement" drop column "updated_at";');
    this.addSql('alter table "asset_measurement" drop column "created_by";');
    this.addSql('alter table "asset_measurement" drop column "updated_by";');
  }
}
