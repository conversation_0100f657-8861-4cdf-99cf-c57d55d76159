import {
  <PERSON><PERSON>ty,
  <PERSON>To<PERSON>ne,
  Primary<PERSON>ey,
  Property,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { User } from 'src/users/domain/user.entity';

@Entity()
export class Location {
  @PrimaryKey()
  id!: number;

  @Unique({ name: 'location_name_key' })
  @Property({ length: 50 })
  name!: string;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;
}
