import { Injectable, NotFoundException } from "@nestjs/common";
import { InvalidInputException } from "src/errors/exceptions";
import { UserId } from "src/users/domain/user.entity";
import {
  AssetType,
  AssetTypeWithTemplateCount,
} from "./domain/asset-type.entity";
import { AssetTypeRepository } from "./repository/asset-type.repository";
import { Reference } from "@mikro-orm/core";

export type AssetTypeCreationData = {
  name: string;
  parentTypeId?: number;
};

export type AssetTypeUpdationData = {
  id: number;
  name: string;
  parentTypeId?: number;
};

@Injectable()
export class AssetTypeService {
  constructor(private readonly assetTypeRepository: AssetTypeRepository) {}

  async findById(id: number): Promise<AssetType | null> {
    return await this.assetTypeRepository.findById(id);
  }

  async getAll(customerId?: number): Promise<AssetTypeWithTemplateCount[]> {
    return await this.assetTypeRepository.getAll(customerId);
  }

  async create(newAssetType: AssetTypeCreationData, runById: UserId) {
    if (
      newAssetType.parentTypeId &&
      (await this.assetTypeRepository.findById(newAssetType.parentTypeId)) ===
        null
    ) {
      throw new InvalidInputException("Parent asset type does not exist");
    }

    const assetType = new AssetType(newAssetType);

    await this.assetTypeRepository.add(assetType, runById);

    return assetType;
  }
  async update(updateAssetType: AssetTypeUpdationData, runById: UserId) {
    if (
      updateAssetType.parentTypeId &&
      (await this.assetTypeRepository.findById(
        updateAssetType.parentTypeId
      )) === null
    ) {
      throw new InvalidInputException("Parent asset type does not exist");
    }
    if (updateAssetType.parentTypeId === updateAssetType.id) {
      throw new InvalidInputException(
        "Parent Asset Type cannot set to current asset type."
      );
    }
    const assetType = await this.findById(updateAssetType.id);
    if (!assetType) {
      throw new NotFoundException("Asset Type not found");
    }
    if (
      (await this.assetTypeRepository.getAll()).find(
        (name) =>
          name.lowerCaseName === updateAssetType.name.toLowerCase() &&
          name.id !== updateAssetType.id
      )
    ) {
      throw new InvalidInputException("Duplicate name found");
    }
    assetType.name = updateAssetType.name;
    if (updateAssetType.parentTypeId) {
      assetType.parentType = Reference.createFromPK(
        AssetType,
        updateAssetType.parentTypeId
      );
    }
    await this.assetTypeRepository.update(assetType, runById);
    return assetType;
  }

  async delete(id: number) {
    const assetType = await this.assetTypeRepository.findById(id);
    if (!assetType) {
      throw new NotFoundException("Asset type not found");
    }
    return await this.assetTypeRepository.delete(id);
  }
}
