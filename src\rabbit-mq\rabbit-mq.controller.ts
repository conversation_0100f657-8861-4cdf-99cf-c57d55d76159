import { Controller, Get } from "@nestjs/common";
import { RabbitMqService } from "./rabbit-mq.service";

@Controller({ path: "rabbit-mq", version: "0" })
export class RabbitMqController {
  constructor(private readonly rabbitMqService: RabbitMqService) {}

  @Get()
  async sendMessage() {
    await this.rabbitMqService.sendMessage("alert.exceeded", {
      alert_id: 586,
      input_value: 99,
      state: "EXCEEDED",
      timestamp: 1710000000000,
      comparator: ">",
      event_id: 56122,
    });
    return { msg: "HI" };
  }
}
