import { Entity, Index, PrimaryKey, Property, Unique } from '@mikro-orm/core';

export type UnitsGroupId = number;
@Entity()
@Index({
  name: 'units_group_base_id',
  expression:
    'create unique index "units_group_base_id" on "units_group"("is_base") where "is_base" is true',
})
export class UnitsGroup {
  @PrimaryKey()
  id!: UnitsGroupId;

  @Property({ length: 50 })
  name!: string;

  @Property({ fieldName: 'is_base', default: false })
  @Unique({ name: 'units_group_base_id' })
  isBase!: boolean;
}
