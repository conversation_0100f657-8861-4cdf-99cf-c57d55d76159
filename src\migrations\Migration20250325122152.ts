// migration/20250325122152_UpdateUserPreferencesCheckConstraint.ts
import { Migration } from "@mikro-orm/migrations";

export class Migration20250325122152 extends Migration {
  async up(): Promise<void> {
    this.addSql(`
      ALTER TABLE "user_preferences"
      DROP CONSTRAINT IF EXISTS "user_preferences_prefer_key_check";
    `);

    this.addSql(`
      ALTER TABLE "user_preferences"
      ADD CONSTRAINT "user_preferences_prefer_key_check"
      CHECK (
        prefer_key = ANY (
          ARRAY['DATE_FORMAT', 'CURRENCY', 'DEFAULT_CUSTOMER', 'THOUSAND_SEPARATOR']::text[]
        )
      );
    `);
  }
}
