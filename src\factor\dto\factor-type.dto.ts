import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsString } from "class-validator";
import { FactorType } from "../domain/factor-type.entity";
import { User } from "../../users/domain/user.entity";

export class FactorTypeDto
  implements
    Pick<
      FactorType,
      "id" | "name" | "createdAt" | "updatedAt" | "createdBy" | "updatedBy"
    >
{
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  @IsNumber()
  createdBy: User; // Update the type to User

  @ApiProperty()
  updatedBy: User; // Update the type to User
}
