import { Migration } from '@mikro-orm/migrations';

export class Migration20231212192449 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "asset" drop constraint "asset_a_type_foreign";');
    this.addSql(
      'alter table "asset" drop constraint "asset_customer_foreign";',
    );

    this.addSql(
      'alter table "asset" add constraint "asset_a_type_foreign" foreign key ("a_type") references "asset_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql('alter table "asset" drop constraint "asset_a_type_foreign";');
    this.addSql(
      'alter table "asset" drop constraint "asset_customer_foreign";',
    );

    this.addSql(
      'alter table "asset" add constraint "asset_a_type_foreign" foreign key ("a_type") references "asset_type" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade on delete set null;',
    );
  }
}
