import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { MeasurementId } from "src/measurements/domain/measurement.entity";

export class CalcInputs {
  @ApiProperty({ type: "number", required: false, example: 23245 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  inputId: number;

  @ApiProperty({ type: "string", example: "$D" })
  @IsString()
  @IsNotEmpty()
  inputLabel: string;

  @ApiProperty({ type: "number", example: 22658 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  measurementId?: MeasurementId;

  @ApiProperty({ type: "string", example: "5" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  constantValue?: string;

  @ApiProperty({
    type: "string",
    enum: ["number", "string"],
    example: "number",
  })
  @IsString()
  @IsEnum(["number", "string"])
  @IsNotEmpty()
  @IsOptional()
  constantType?: string;

  @ApiProperty({ type: "string", required: false, example: "voltage in" })
  @IsString()
  @IsOptional()
  comment?: string;
}

export class CalcInstanceDto {
  @ApiProperty({ required: true, type: "number", example: 5 })
  @IsNumber()
  @IsNotEmpty()
  templateId: number;

  @ApiProperty({ required: true, type: "number", example: 5 })
  @IsNumber()
  @IsNotEmpty()
  customerId: number;

  @ApiProperty({ required: true, type: "number", example: 22658 })
  @IsNumber()
  @IsNotEmpty()
  outputMesurementId: number;

  @ApiProperty({ required: true, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  ispersisted: boolean;

  @ApiProperty({ required: false, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  iswriteback: boolean;

  @ApiProperty({ required: false, type: "number", example: 1 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  pollPeriod: number;

  @ApiProperty({
    example: [
      {
        inputLabel: "$A",
        mesurementId: 22539,
        comment: "With mesurement Id",
      },
      {
        inputLabel: "$B",
        constantValue: "5",
        constantType: "number",
        comment: "With constant",
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  @Type(() => CalcInputs)
  inputs: CalcInputs[];
}

export class CalcGetAllInstanceDto {
  @ApiProperty({ required: true, type: "string", example: "5" })
  @IsString()
  @IsNotEmpty()
  data_type: string;
}

export class OutputMeasurementDto {
  @ApiProperty({ required: true, type: "number", example: 24012 })
  id: number;

  @ApiProperty({ required: true, type: "string", example: "xyzABC" })
  tag: string;

  @ApiProperty({ required: true, type: "number", example: 3 })
  data_type: number;

  @ApiProperty({ required: false, type: "string", example: null })
  unit_of_measure: string | null;
}

export class CalculationInputDto {
  @ApiProperty({ required: true, type: "number", example: 241 })
  id: number;

  @ApiProperty({ required: true, type: "string", example: "$A" })
  input_label: string;

  @ApiProperty({
    required: true,
    type: "string",
    format: "date-time",
    example: "2024-05-15T09:19:42.832Z",
  })
  created: Date;

  @ApiProperty({
    required: false,
    type: "string",
    format: "date-time",
    example: null,
  })
  updated: Date | null;

  @ApiProperty({ required: true, type: "number", example: 2 })
  createdby: number;

  @ApiProperty({ required: false, type: "number", example: null })
  updatedby: number | null;

  @ApiProperty({ required: true, type: "number", example: 115 })
  calculation_instance: number;

  @ApiProperty({ required: false, type: OutputMeasurementDto, example: null })
  measurement: OutputMeasurementDto | null;

  @ApiProperty({ required: false, type: "number", example: null })
  constant_number: number | null;

  @ApiProperty({ required: false, type: "string", example: "asa" })
  constant_string: string | null;

  @ApiProperty({ required: false, type: "string", example: "" })
  comment: string;
}

export class CalcInstanceDTO {
  @ApiProperty({ required: true, type: "number", example: 115 })
  id: number;

  @ApiProperty({
    required: true,
    type: "string",
    format: "date-time",
    example: "2024-05-15T09:19:39.560Z",
  })
  created: Date;

  @ApiProperty({
    required: true,
    type: "string",
    format: "date-time",
    example: "2024-05-15T13:05:47.841Z",
  })
  updated: Date;

  @ApiProperty({ required: true, type: "number", example: 2 })
  createdby: number;

  @ApiProperty({ required: true, type: "number", example: 2 })
  updatedby: number;

  @ApiProperty({ required: true, type: "number", example: 3 })
  calculation: number;

  @ApiProperty({ required: true, type: OutputMeasurementDto, example: null })
  output_measurement: OutputMeasurementDto;

  @ApiProperty({ required: true, type: "boolean", example: false })
  ispersisted: boolean;

  @ApiProperty({ required: false, type: "number", example: null })
  poll_period: number | null;

  @ApiProperty({ required: true, type: [CalculationInputDto], example: [] })
  calculation_inputs: CalculationInputDto[];
}

export class GetInstanceResponseDto {
  @ApiProperty({
    required: true,
    type: [CalcInstanceDTO],
    example: [
      {
        id: 115,
        created: "2024-05-15T09:19:39.560Z",
        updated: "2024-05-15T13:05:47.841Z",
        createdby: 2,
        updatedby: 2,
        calculation: 3,
        output_measurement: {
          id: 24012,
          tag: "xyzABC",
          data_type: 3,
          unit_of_measure: null,
        },
        ispersisted: false,
        poll_period: null,
        calculation_inputs: [
          {
            id: 241,
            input_label: "$A",
            created: "2024-05-15T09:19:42.832+00:00",
            updated: null,
            createdby: 2,
            updatedby: null,
            calculation_instance: 115,
            measurement: null,
            constant_number: null,
            constant_string: "asa",
            comment: "",
          },
        ],
      },
    ],
  })
  items: CalcInstanceDTO[];

  @ApiProperty({ required: true, type: "number", example: 1 })
  total: number;
}
