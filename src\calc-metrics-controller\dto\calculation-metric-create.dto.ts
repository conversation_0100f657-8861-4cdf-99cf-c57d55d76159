import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { MetricId } from "src/assets/domain/metric.entity";

export class CalcMetricInputs {
  @ApiProperty({ type: "number", required: false, example: 23245 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  inputId: number;

  @ApiProperty({ type: "string", example: "$D" })
  @IsString()
  @IsNotEmpty()
  inputLabel: string;

  @ApiProperty({ type: "number", example: 22658 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  metric?: MetricId;

  @ApiProperty({ type: "string", example: "5" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  constantValue?: string;

  @ApiProperty({
    type: "string",
    enum: ["number", "string"],
    example: "number",
  })
  @IsString()
  @IsEnum(["number", "string"])
  @IsNotEmpty()
  @IsOptional()
  constantType?: string;

  @ApiProperty({ type: "string", required: false, example: "voltage in" })
  @IsString()
  @IsOptional()
  comment?: string;
}
export class CalcMetricDTO {
  @ApiProperty({ required: true, type: "number", example: 5 })
  @IsNumber()
  @IsNotEmpty()
  templateId: number;

  @ApiProperty({ required: true, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  ispersisted: boolean;

  @ApiProperty({ required: false, type: "number", example: 1 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  pollPeriod: number;

  @ApiProperty({ required: false, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  iswriteback: boolean;

  @ApiProperty({ required: true, type: "number", example: 444 })
  @IsNumber()
  @IsNotEmpty()
  metricId?: MetricId;

  @ApiProperty({
    example: [
      {
        inputLabel: "$A",
        mesurementId: 402,
        comment: "With mesurement Id",
      },
      {
        inputLabel: "$B",
        constantValue: "5",
        constantType: "number",
        comment: "With constant",
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  @Type(() => CalcMetricInputs)
  inputs: CalcMetricInputs[];
}

export class CalcMetricDTOArray {
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CalcMetricDTO)
  data: CalcMetricDTO[];
}
