import { HttpStatus } from "@nestjs/common";

export const ExceptionMessages = {
  // Client Errors
  BAD_REQUEST: {
    message: "Bad request.",
    statusCode: HttpStatus.BAD_REQUEST, // 400
  },
  UNAUTHORIZED: {
    message: "Unauthorized access.",
    statusCode: HttpStatus.UNAUTHORIZED, // 401
  },
  PAYMENT_REQUIRED: {
    message: "Payment required.",
    statusCode: HttpStatus.PAYMENT_REQUIRED, // 402
  },
  FORBIDDEN: {
    message: "Access is forbidden.",
    statusCode: HttpStatus.FORBIDDEN, // 403
  },
  NOT_FOUND: {
    message: "Resource not found.",
    statusCode: HttpStatus.NOT_FOUND, // 404
  },
  METHOD_NOT_ALLOWED: {
    message: "HTTP method not allowed.",
    statusCode: HttpStatus.METHOD_NOT_ALLOWED, // 405
  },
  NOT_ACCEPTABLE: {
    message: "Not acceptable.",
    statusCode: HttpStatus.NOT_ACCEPTABLE, // 406
  },
  PROXY_AUTHENTICATION_REQUIRED: {
    message: "Proxy authentication required.",
    statusCode: HttpStatus.PROXY_AUTHENTICATION_REQUIRED, // 407
  },
  REQUEST_TIMEOUT: {
    message: "Request timed out.",
    statusCode: HttpStatus.REQUEST_TIMEOUT, // 408
  },
  CONFLICT: {
    message: "Conflict occurred.",
    statusCode: HttpStatus.CONFLICT, // 409
  },
  GONE: {
    message: "Resource is no longer available.",
    statusCode: HttpStatus.GONE, // 410
  },
  LENGTH_REQUIRED: {
    message: "Content length is required.",
    statusCode: HttpStatus.LENGTH_REQUIRED, // 411
  },
  PRECONDITION_FAILED: {
    message: "Precondition failed.",
    statusCode: HttpStatus.PRECONDITION_FAILED, // 412
  },
  PAYLOAD_TOO_LARGE: {
    message: "Payload is too large.",
    statusCode: HttpStatus.PAYLOAD_TOO_LARGE, // 413
  },
  URI_TOO_LONG: {
    message: "URI is too long.",
    statusCode: HttpStatus.URI_TOO_LONG, // 414
  },
  UNSUPPORTED_MEDIA_TYPE: {
    message: "Unsupported media type.",
    statusCode: HttpStatus.UNSUPPORTED_MEDIA_TYPE, // 415
  },
  RANGE_NOT_SATISFIABLE: {
    message: "Requested range not satisfiable.",
    statusCode: HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE, // 416
  },
  EXPECTATION_FAILED: {
    message: "Expectation failed.",
    statusCode: HttpStatus.EXPECTATION_FAILED, // 417
  },
  I_AM_A_TEAPOT: {
    message: "I am a teapot.",
    statusCode: HttpStatus.I_AM_A_TEAPOT, // 418
  },
  UNPROCESSABLE_ENTITY: {
    message: "Unprocessable entity.",
    statusCode: HttpStatus.UNPROCESSABLE_ENTITY, // 422
  },
  PRECONDITION_REQUIRED: {
    message: "Precondition required.",
    statusCode: HttpStatus.PRECONDITION_REQUIRED, // 428
  },
  TOO_MANY_REQUESTS: {
    message: "Too many requests.",
    statusCode: HttpStatus.TOO_MANY_REQUESTS, // 429
  },
  REQUEST_HEADER_FIELDS_TOO_LARGE: {
    message: "Request header fields are too large.",
    statusCode: HttpStatus.PAYLOAD_TOO_LARGE, // 431
  },

  // Server Errors
  INTERNAL_SERVER_ERROR: {
    message: "Internal server error.",
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR, // 500
  },
  NOT_IMPLEMENTED: {
    message: "Not implemented.",
    statusCode: HttpStatus.NOT_IMPLEMENTED, // 501
  },
  BAD_GATEWAY: {
    message: "Bad gateway.",
    statusCode: HttpStatus.BAD_GATEWAY, // 502
  },
  SERVICE_UNAVAILABLE: {
    message: "Service unavailable.",
    statusCode: HttpStatus.SERVICE_UNAVAILABLE, // 503
  },
  GATEWAY_TIMEOUT: {
    message: "Gateway timeout.",
    statusCode: HttpStatus.GATEWAY_TIMEOUT, // 504
  },
  HTTP_VERSION_NOT_SUPPORTED: {
    message: "HTTP version not supported.",
    statusCode: HttpStatus.HTTP_VERSION_NOT_SUPPORTED, // 505
  },
};
