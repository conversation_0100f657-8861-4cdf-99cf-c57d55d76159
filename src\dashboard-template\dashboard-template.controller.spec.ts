import { Test, TestingModule } from '@nestjs/testing';
import { DashboardTemplateController } from './dashboard-template.controller';

describe('DashboardTemplateController', () => {
  let controller: DashboardTemplateController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardTemplateController],
    }).compile();

    controller = module.get<DashboardTemplateController>(DashboardTemplateController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
