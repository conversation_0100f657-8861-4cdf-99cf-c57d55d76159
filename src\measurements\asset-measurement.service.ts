import { <PERSON>tity<PERSON>anager } from "@mikro-orm/core";
import { Injectable } from "@nestjs/common";
import { AssetService } from "src/assets/asset.service";
import { AssetId } from "src/assets/domain/asset.entity";
import { Metric } from "src/assets/domain/metric.entity";
import { MetricService } from "src/assets/metric.service";
import { CustomerService } from "src/customers/customer.service";
import { CustomerId } from "src/customers/domain/customer.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { InvalidInputException } from "src/errors/exceptions";
import { findOrThrowError } from "src/errors/utils";
import { UserId } from "src/users/domain/user.entity";
import { DatasourceService } from "./datasource.service";
import {
  AssetMeasurement,
  AssetMeasurementId,
} from "./domain/asset-measurement.entity";
import { Datasource } from "./domain/datasource.entity";
import { Location } from "./domain/location.entity";
import { AssetMeasurementCreationData } from "./domain/types";
import { UnitOfMeasure } from "./domain/unit-of-measure.entity";
import { MeasurementsBackofficeService } from "./measurements-backoffice.service";
import { AssetMeasurementRepository } from "./repository/asset-measurement.repository";
import { TimeSeriesService } from "./time-series.service";
import { UnitOfMeasureService } from "./unit-of-measure.service";

export type AssetMeasurementUpdataData = Partial<
  Omit<
    AssetMeasurementCreationData,
    "customerId" | "assetId" | "datasourceId" | "dataTypeId"
  >
> & {
  dataTypeId?: number;
};

@Injectable()
export class AssetMeasurementService {
  constructor(
    private readonly customerService: CustomerService,
    private readonly assetService: AssetService,
    private readonly metricService: MetricService,
    private readonly unitOfMeasureService: UnitOfMeasureService,
    private readonly assetMeasurementRepository: AssetMeasurementRepository,
    private readonly measurementsBackofficeService: MeasurementsBackofficeService,
    private readonly datasourceService: DatasourceService,
    private readonly timeSeriesService: TimeSeriesService,
    private readonly transactionFactory: TransactionFactory
  ) {}

  async create(
    params: AssetMeasurementCreationData,
    customerId: number,
    createdById: number,
    headers?: Request["headers"],
    emManager?: EntityManager
  ): Promise<AssetMeasurement> {
    return await this.transactionFactory.run(async (emLocal) => {
      const em = emManager ?? emLocal;
      await this.checkCustomerExists(customerId);
      const asset = await findOrThrowError(
        params.assetId,
        (id) => this.assetService.findById(id, customerId),
        "Asset does not exist"
      );

      let metric: Metric | undefined;
      if (params.metricId) {
        const possiblyMetric = await this.metricService.findById(
          params.metricId
        );

        if (possiblyMetric === null) {
          throw new InvalidInputException("Metric does not exist");
        } else if (asset.assetType.id !== possiblyMetric.assetType.id) {
          throw new InvalidInputException(
            "Metric does not belong to asset type"
          );
        } else if (
          (await this.assetMeasurementRepository.countByAssetId(asset.id, {
            metricId: possiblyMetric.id,
          })) > 0
        ) {
          throw new InvalidInputException(
            "Metric already present for given asset"
          );
        } else {
          metric = possiblyMetric;
        }
      }

      const measurementType =
        await this.measurementsBackofficeService.getMeasurementTypeById(
          params.typeId
        );
      if (measurementType === null) {
        throw new InvalidInputException("Measurement type does not exist");
      }

      const dataType = await this.measurementsBackofficeService.getDataTypeById(
        params.dataTypeId
      );
      if (dataType === null) {
        throw new InvalidInputException("Data type does not exist");
      }

      const valueType =
        await this.measurementsBackofficeService.getValueTypeById(
          params.valueTypeId
        );
      if (valueType === null) {
        throw new InvalidInputException("Value type does not exist");
      }

      let unitOfMeasure: UnitOfMeasure | undefined;
      if (params.unitOfMeasureId) {
        const possiblyUnitOfMeasure = await this.unitOfMeasureService.getById(
          params.unitOfMeasureId
        );
        if (possiblyUnitOfMeasure === null) {
          throw new InvalidInputException("Unit of measure does not exist");
        } else {
          unitOfMeasure = possiblyUnitOfMeasure;
        }
      }

      let location: Location | undefined;
      if (params.locationId) {
        const possiblyLocation =
          await this.measurementsBackofficeService.getLocationById(
            params.locationId
          );

        if (possiblyLocation === null) {
          throw new InvalidInputException("Location does not exist");
        } else {
          location = possiblyLocation;
        }
      }

      let datasource: Datasource | undefined;
      if (params.datasourceId) {
        const possiblyDatasource = await this.datasourceService.getById(
          params.datasourceId
        );

        if (possiblyDatasource === null) {
          throw new InvalidInputException("Datasource does not exist");
        } else {
          datasource = possiblyDatasource;
        }
      }

      const newAssetMeasurement = new AssetMeasurement({
        ...params,
        location,
        measurementType,
        dataType,
        valueType,
        unitOfMeasure,
        datasource,
        metric,
      });
      await this.assetMeasurementRepository.add(
        newAssetMeasurement,
        createdById
      );
      await this.timeSeriesService.create(
        newAssetMeasurement,
        customerId,
        headers,
        newAssetMeasurement.measurement.datasource?.name !== "Calculation",
        params.writeback
      );
      return newAssetMeasurement;
    });
  }

  private async checkCustomerExists(customerId: number) {
    if ((await this.customerService.findById(customerId)) === null) {
      throw new InvalidInputException("Customer does not exist");
    }
  }

  async getAll(
    customerId: number,
    assetId: number
  ): Promise<AssetMeasurement[]> {
    await this.checkCustomerExists(customerId);
    await findOrThrowError(
      assetId,
      (id) => this.assetService.findById(id, customerId),
      "Asset does not exist"
    );

    return await this.assetMeasurementRepository.getAllByAssetId(assetId);
  }

  async getAllByAssetIdsMeasureIds({
    customerId,
    data,
  }: {
    customerId: number;
    data: {
      assetId: number;
      measurementId: number[];
    }[];
  }) {
    await this.checkCustomerExists(customerId);
    const assetMeasurements =
      await this.assetMeasurementRepository.getAllByAssetIdsMeasureIds({
        customerId,
        data,
      });
    return assetMeasurements;
  }
  async getById(
    customerId: number,
    assetId: number,
    assetMeasurementId: number
  ): Promise<AssetMeasurement | null> {
    return await this.assetMeasurementRepository.findById(assetMeasurementId, {
      assetId,
      customerId,
    });
  }

  async removeById(
    customerId: number,
    assetId: number,
    assetMeasurementId: number,
    deletedById: number,
    headers: Request["headers"],
    emManager?: EntityManager
  ) {
    return this.transactionFactory.run(async (emLocal) => {
      const em = (emManager ?? emLocal) as EntityManager;
      const assetMeasurement = await this.assetMeasurementRepository.findById(
        assetMeasurementId,
        { assetId, customerId }
      );

      if (assetMeasurement === null) {
        throw new InvalidInputException("Asset measurement not found");
      }

      await this.assetMeasurementRepository.remove(
        assetMeasurement,
        headers,
        deletedById,
        em
      );
    });
  }

  async batchRemoveByAssetId(
    assetIds: number[],
    occuredAt: Date,
    runById: UserId
  ) {
    await this.assetMeasurementRepository.batchRemoveByAssetId(
      assetIds,
      occuredAt,
      runById
    );
  }

  async handleAssetUpdate(
    assetId: AssetId,
    customerId: CustomerId,
    headers: Request["headers"],
    runBy: UserId,
    emManager?: EntityManager
  ) {
    const asset = await this.assetService.findById(assetId);

    if (asset === null) {
      throw new InvalidInputException("Asset does not exist");
    }

    const assetMeasurements =
      await this.assetMeasurementRepository.getAllByAssetId(asset.id);
    await Promise.all(
      assetMeasurements.map(async (assetMeasurement) => {
        const tagParts = assetMeasurement.measurement.tag?.split("\\");
        if (tagParts && tagParts.length >= 2) {
          // Replace second-to-last part with asset.tag
          tagParts[tagParts.length - 2] = asset.tag;
          // Update tag
          assetMeasurement.measurement.tag = tagParts.join("\\");
        }
        await this.update(
          customerId,
          assetId,
          assetMeasurement.id,
          { tag: assetMeasurement.tag },
          runBy,
          headers,
          emManager
        );
      })
    );
  }

  async update(
    customerId: CustomerId,
    assetId: AssetId,
    assetMeasurementId: AssetMeasurementId,
    update: AssetMeasurementUpdataData,
    runBy: UserId,
    headers?: Request["headers"],
    emManager?: EntityManager
  ) {
    return await this.transactionFactory.run(async (emLocal) => {
      const em = emManager ?? emLocal;
      const asset = await this.assetService.findById(assetId, customerId);

      if (asset === null) {
        throw new InvalidInputException("Asset does not exist");
      }

      const assetMeasurement = await this.assetMeasurementRepository.findById(
        assetMeasurementId,
        { assetId, customerId }
      );

      if (assetMeasurement === null) {
        throw new InvalidInputException(
          "Asset measurement not found ID:" +
            " customerId:" +
            customerId +
            " assetId:" +
            assetId +
            "assetMeasurementId:" +
            assetMeasurementId
        );
      }

      const {
        description,
        tag,
        meterFactor,
        locationId,
        typeId,
        dataTypeId,
        unitOfMeasureId,
        valueTypeId,
        metricId,
        writeback,
      } = update;

      if (metricId) {
        const possiblyMetric = await this.metricService.findById(metricId);

        if (possiblyMetric === null) {
          throw new InvalidInputException("Metric does not exist");
        } else if (asset.assetType.id !== possiblyMetric.assetType.id) {
          throw new InvalidInputException(
            "Metric does not belong to asset type"
          );
        }
        // else if (
        //   (await this.assetMeasurementRepository.countByAssetId(asset.id, {
        //     metricId: possiblyMetric.id,
        //   })) > 0
        // ) {
        //   throw new InvalidInputException(
        //     "Metric already present for given asset"
        //   );
        // }
        else {
          assetMeasurement.measurement.metric = possiblyMetric;
        }
      }
      if (metricId === null) {
        assetMeasurement.measurement.metric = null;
      }
      if (description) {
        assetMeasurement.measurement.description = description;
      }

      if (tag) {
        assetMeasurement.measurement.tag = tag;
      }

      if (meterFactor) {
        assetMeasurement.measurement.meterFactor = meterFactor;
      }

      if (locationId) {
        assetMeasurement.location = await findOrThrowError(
          locationId,
          (id) => this.measurementsBackofficeService.getLocationById(id),
          "Location does not exist"
        );
      }

      if (typeId) {
        assetMeasurement.measurement.measurementType = await findOrThrowError(
          typeId,
          (id) => this.measurementsBackofficeService.getMeasurementTypeById(id),
          "Measurement type does not exist"
        );
      }

      if (unitOfMeasureId) {
        assetMeasurement.measurement.unitOfMeasure = await findOrThrowError(
          unitOfMeasureId,
          (id) => this.unitOfMeasureService.getById(id),
          "Unit of measure does not exist"
        );
      }

      if (valueTypeId) {
        assetMeasurement.measurement.valueType = await findOrThrowError(
          valueTypeId,
          (id) => this.measurementsBackofficeService.getValueTypeById(id),
          "Value type does not exist"
        );
      }

      if (dataTypeId) {
        assetMeasurement.measurement.dataType = await findOrThrowError(
          dataTypeId,
          (id) => this.measurementsBackofficeService.getDataTypeById(id),
          "Data type does not exist"
        );
      }

      if (writeback) {
        assetMeasurement.measurement.writeback = writeback;
      } else {
        assetMeasurement.measurement.writeback = false;
      }
      let datasource: Datasource | undefined;
      if (assetMeasurement.datasourceId) {
        const possiblyDatasource = await this.datasourceService.getById(
          assetMeasurement.datasourceId
        );
        datasource = possiblyDatasource;
      }
      await this.assetMeasurementRepository.update(assetMeasurement, runBy);
      await this.timeSeriesService.update(
        assetMeasurement,
        customerId,
        headers,
        datasource?.name !== "Calculation"
      );
    });
  }
}
