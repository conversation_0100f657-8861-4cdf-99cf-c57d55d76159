import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { ApiNotFoundResponse, ApiOkResponse } from "@nestjs/swagger";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import {
  GetWeatherDto,
  GetWeatherNotFoundResDto,
  GetWeatherResDto,
} from "./dto/weather.dto";
import { WeatherService } from "./weather.service";

@Controller({ version: "0", path: "weather" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class WeatherController {
  constructor(private readonly weatherService: WeatherService) {}

  @Get("/station/:station_id")
  @HasRole(Role.USER)
  @ApiOkResponse({ type: GetWeatherResDto })
  @ApiNotFoundResponse({ type: GetWeatherNotFoundResDto })
  async getUserPreference(@Param() param: GetWeatherDto) {
    return await this.weatherService.getStationWeather(param.station_id);
  }
}
