import { <PERSON><PERSON>ty, ManyTo<PERSON>ne, <PERSON><PERSON>ey, Property } from "@mikro-orm/core";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";

@Entity()
export class Annotation {
  @PrimaryKey()
  id: number;

  @ManyToOne({ fieldName: "dashboard", entity: () => Dashboard })
  dashboard: Dashboard;

  @ManyToOne({ fieldName: "measurement_id", entity: () => Measurement })
  measurement_id: Measurement;

  @Property()
  description: string;

  @Property()
  widget_id: number;

  @Property({
    type: "bigint",
  })
  time_of_annotation: number;

  @Property({
    type: "double precision",
  })
  value: number;

  @Property({ length: 65536 })
  settings!: string;
}
