import {
  <PERSON><PERSON>ty,
  PrimaryKey,
  Property,
  ManyToOne,
  Ref,
  Unique,
  Reference,
} from '@mikro-orm/core';
import { User } from 'src/users/domain/user.entity';

type AssetTypeCreationParams = {
  name: string;
  parentTypeId?: AssetTypeId;
};

export type AssetTypeId = number;
@Entity()
@Unique({ properties: ['parentType', 'lowerCaseName'] })
export class AssetType {
  constructor(params: AssetTypeCreationParams) {
    const { name, parentTypeId } = params;

    this.name = name;
    if (parentTypeId) {
      this.parentType = Reference.createFromPK(AssetType, parentTypeId);
    }
  }

  @PrimaryKey()
  id!: number;

  @ManyToOne({
    fieldName: 'parent_type',
    nullable: true,
  })
  parentType?: Ref<AssetType>;

  @Property({
    length: 50,
    columnType: 'VARCHAR GENERATED ALWAYS AS (LOWER("name")) STORED',
    ignoreSchemaChanges: ['type', 'extra'],
    // Workaround to avoid non-nullable validation when creating
    onCreate: (e: AssetType) => e.lowerCaseName,
    hidden: true,
  })
  lowerCaseName!: string;

  @Property({ length: 50 })
  name!: string;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;
}

export class AssetTypeWithTemplateCount extends AssetType {
  @Property()
  asset_template_count?: number
}