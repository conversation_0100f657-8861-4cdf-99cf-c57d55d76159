import { Test, TestingModule } from "@nestjs/testing";
import { RabbitMqController } from "./rabbit-mq.controller";
import { RabbitMqService } from "./rabbit-mq.service";

describe("RabbitMqController", () => {
  let controller: RabbitMqController;
  let service: RabbitMqService;

  const mockRabbitMqService = {
    sendMessage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RabbitMqController],
      providers: [
        {
          provide: RabbitMqService,
          useValue: mockRabbitMqService,
        },
      ],
    }).compile();

    controller = module.get<RabbitMqController>(RabbitMqController);
    service = module.get<RabbitMqService>(RabbitMqService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  it("should call sendMessage with correct payload and return message", async () => {
    mockRabbitMqService.sendMessage.mockResolvedValue(undefined); // assuming it returns nothing

    const result = await controller.sendMessage();

    expect(mockRabbitMqService.sendMessage).toHaveBeenCalledWith(
      "alert.exceeded",
      {
        alert_id: 586,
        input_value: 99,
        state: "EXCEEDED",
        timestamp: 1710000000000,
        comparator: ">",
        event_id: 56122,
      }
    );

    expect(result).toEqual({ msg: "HI" });
  });
});
