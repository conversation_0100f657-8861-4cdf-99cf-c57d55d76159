import { <PERSON>tity, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property, Ref } from '@mikro-orm/core';
import { User, UserId } from '../../users/domain/user.entity';

@Entity()
export class Datasource {
  @PrimaryKey()
  id!: number;

  @Property({ length: 50 })
  name!: string;

  @Property({ length: 100, nullable: true })
  description?: string;

  @Property({ length: 256 })
  uri!: string;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: 'created_by',
  })
  private readonly createdBy?: Ref<User>;

  @Property({ fieldName: 'created_by' })
  createdById?: UserId;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: 'updated_by',
  })
  private readonly updatedBy?: Ref<User>;

  @Property({ fieldName: 'updated_by' })
  updatedById?: UserId;
}
