import { Migration } from '@mikro-orm/migrations';

export class Migration20240424052659 extends Migration {

  async up(): Promise<void> {
    this.addSql('update "dashboard" as t1 set title = concat(title, id) where exists ( select 1 from "dashboard" AS t2 where t1.id <> t2.id and t1.title = t2.title)');
    this.addSql('alter table "dashboard" add constraint "dashboard_unique" unique ("title");');

    this.addSql('alter table "user_preferences" alter column "prefer_key" type text using ("prefer_key"::text);');
    this.addSql('alter table "user_preferences" add constraint "user_preferences_prefer_key_check" check ("prefer_key" in (\'DATE_FORMAT\', \'CURRENCY\', \'DEFAULT_CUSTOMER\'));');
  }

  async down(): Promise<void> {
    this.addSql('alter table "user_preferences" drop constraint if exists "user_preferences_prefer_key_check";');

    this.addSql('alter table "dashboard" drop constraint "dashboard_unique";');

    this.addSql('alter table "user_preferences" alter column "prefer_key" type varchar(50) using ("prefer_key"::varchar(50));');
  }

}
