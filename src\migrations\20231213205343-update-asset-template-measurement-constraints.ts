import { Migration } from '@mikro-orm/migrations';

export class Migration20231213205343 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "meter_template_fk";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "meas_type_fk";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "location_fk";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "value_type_fk";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "units_fk";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_foreign" foreign key ("meter_template") references "asset_template" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_data_type_foreign" foreign key ("data_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_location_foreign" foreign key ("location") references "location" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_value_type_foreign" foreign key ("value_type") references "value_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_default_units_foreign" foreign key ("default_units") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_data_type_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_location_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_value_type_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_default_units_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "meter_template_fk" foreign key ("meter_template") references "asset_template" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "meas_type_fk" foreign key ("data_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "location_fk" foreign key ("location") references "location" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "value_type_fk" foreign key ("value_type") references "value_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "units_fk" foreign key ("default_units") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
  }
}
