import { InjectRepository } from "@mikro-orm/nestjs";
import { Diagram } from "./domain/diagram.entity";
import { EntityRepository } from "@mikro-orm/postgresql";
import { ConflictException } from "@nestjs/common";

export class DiagramRepository {
  constructor(
    @InjectRepository(Diagram)
    private readonly diagramTemplates: EntityRepository<Diagram>
  ) {}

  async add(diagramData: Diagram) {
    if (await this.findUniqeName(diagramData.name)) {
      throw new ConflictException(
        `Diagram already exist with "${diagramData.name}" name.`
      );
    }
    diagramData.createdAt = new Date();
    await this.diagramTemplates.persistAndFlush(diagramData);
    delete diagramData.created_by;
    return diagramData;
  }

  async update(diagramData: Diagram) {
    diagramData.updatedAt = new Date();
    await this.diagramTemplates.persistAndFlush(diagramData);
    delete diagramData.updated_by;
    delete diagramData.created_by;
    return diagramData;
  }

  async findAll() {
    return await this.diagramTemplates.findAll({
      fields: ["id", "name", "createdAt", "updatedAt"],
    });
  }

  async findUniqeName(name: string) {
    return await this.diagramTemplates.findOne({
      name: name,
    });
  }

  async findById(id: number) {
    return await this.diagramTemplates.findOne(
      {
        id,
      }
      //   {
      //     fields: [
      //       "id",
      //       "name",
      //       "data",
      //       "created_by",
      //       "updated_by",
      //       "createdAt",
      //       "updatedAt",
      //     ],
      //   }
    );
  }
}
