import { <PERSON><PERSON>ty, <PERSON>um, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { User } from './user.entity';
import { PreferenceKeys } from '../dto/user_perference.dto';

@Entity()
@Unique({ name: 'user_preferences_unique', properties: ['preferKey', 'user'] })
export class UserPreferences {

  @PrimaryKey()
  id!: number;

  @ManyToOne({ entity: () => User, onDelete: 'cascade' })
  user!: User;

  @Enum()
  preferKey!: PreferenceKeys;

  @Property({ length: 255 })
  preferValue!: string;

  @Property({ length: 6 })
  createdAt!: Date;

  @Property({ length: 6 })
  updatedAt!: Date;
}