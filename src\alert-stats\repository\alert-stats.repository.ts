import { EntityManager } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { Injectable } from "@nestjs/common";
import { ExcursionStats } from "../domain/excursion-stats.entity";

@Injectable()
export class AlertStatsRepository {
  constructor(
    @InjectRepository(ExcursionStats)
    private readonly alertStats: EntityRepository<ExcursionStats>,
    private readonly em: EntityManager
  ) {}

  async getStatByAlert({ id }: { id: number }) {
    const alertStats = await this.alertStats.find({
      alert: {
        id: id,
      },
    });
    return alertStats.map((alertStat) => {
      const { formattedTimeDuration, ...rest } = alertStat;
      return {
        ...rest,
        formattedTimeDuration,
      };
    });
  }

  async getGroupedStats(
    interval: "daily" | "weekly" | "monthly",
    assetId?: number,
    measureId?: number,
    date?: string
  ) {
    const defaultDurations = {
      daily: 30, // 30 days
      weekly: 12, // 12 weeks
      monthly: 12, // 12 months (1 year)
    };

    if (!["daily", "weekly", "monthly"].includes(interval)) {
      throw new Error(
        `Invalid interval. Must be one of: daily, weekly, monthly`
      );
    }
    const currentDate = new Date();
    const pastDate = new Date(currentDate);
    if (interval === "daily") {
      pastDate.setDate(currentDate.getDate() - defaultDurations.daily);
    } else if (interval === "weekly") {
      pastDate.setDate(currentDate.getDate() - 7 * defaultDurations.weekly);
    } else if (interval === "monthly") {
      pastDate.setMonth(currentDate.getMonth() - defaultDurations.monthly);
    }

    const filters: string[] = [];
    const params: any[] = []; // Use an array for PostgreSQL positional parameters

    if (measureId) {
      filters.push(`AND alerts.measurement_id = ?`);
      params.push(measureId);
    }
    if (assetId) {
      filters.push(`AND alerts.asset_id = ?`);
      params.push(assetId);
    }

    if (date) {
      filters.push(
        `AND DATE_TRUNC(?, excursion_stats.start_time) = DATE_TRUNC(?, TIMESTAMP ?)`
      );
      params.push(
        interval === "daily" ? "day" : interval === "weekly" ? "week" : "month"
      );
      params.push(
        interval === "daily" ? "day" : interval === "weekly" ? "week" : "month"
      );
      params.push(date);
    } else {
      filters.push(`AND excursion_stats.start_time >= ?`);
      params.push(pastDate.toISOString());
    }

    const intervalTrunc =
      interval === "daily" ? "day" : interval === "weekly" ? "week" : "month";

    const query = `
      SELECT
        excursion_stats.alert_id,
        DATE_TRUNC('${intervalTrunc}', excursion_stats.start_time) AS period_start,
        COUNT(*) AS excursion_count,
        SUM(EXTRACT(EPOCH FROM excursion_stats.time_duration)) AS total_duration_seconds,
        AVG(excursion_stats.avg_value) AS avg_value,
        MIN(excursion_stats.min_value) AS min_value,
        MAX(excursion_stats.max_value) AS max_value,
  
        -- Required fields from Alerts
        alerts.threshold_type,  
        alerts.agg,             
        alerts.period,          
        alerts.state,           
        alerts.enabled,         
  
        -- Asset and Measurement types
        asset.a_type,      
        measurement.m_type, 
        measurement.metric
  
      FROM
        excursion_stats
      JOIN
        alerts ON excursion_stats.alert_id = alerts.id
      JOIN
        asset ON alerts.asset_id = asset.id  
      JOIN
        measurement ON alerts.measurement_id = measurement.id  
      WHERE
        1=1
        ${filters.join(" ")}
      GROUP BY
        excursion_stats.alert_id, DATE_TRUNC('${intervalTrunc}', excursion_stats.start_time),
        alerts.threshold_type, alerts.agg, alerts.period, alerts.state, alerts.enabled,
        asset.a_type, measurement.m_type, measurement.metric
      ORDER BY
        excursion_stats.alert_id, period_start;
    `;

    console.log(query, params);
    return this.em.getConnection().execute(query, params, "all");
  }

  async getAllExcursionStats() {
    return await this.alertStats.findAll({
      populate: ["alert.measurement"], // Populate the alert and measurement relationships
    });
  }
}
