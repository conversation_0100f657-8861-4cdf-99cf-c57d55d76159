import { <PERSON><PERSON>ty, PrimaryKey, Property } from "@mikro-orm/core";

@Entity()
export class DashboardSyncJob {
  @PrimaryKey()
  id: string;

  @Property({})
  customerId: number;

  @Property()
  dashboardId: number;

  @Property({ default: "pending" }) // pending, processing, completed, failed
  status: string;

  @Property({ type: "int", default: 0 })
  progress: number;

  @Property({ type: "jsonb", nullable: true })
  result: Record<string, any>;

  @Property({ hidden: true, length: 6, nullable: true })
  createdAt: Date;

  @Property({ hidden: true, length: 6, nullable: true })
  updatedAt: Date;
}
