import { ApiProperty } from '@nestjs/swagger';

export class DashboardItem {
  @ApiProperty({ required: true, example: 1 })
  id: number;

  @ApiProperty({ required: true, example: "dashboard-123" })
  title: string;

  @ApiProperty({ required: true, example: true })
  default: boolean;

  @ApiProperty({ required: true, example: true })
  favorite: boolean;
}

export class GetAllDashboardSuccessResDto {
  @ApiProperty({ required: true, type: [DashboardItem] })
  items: Array<DashboardItem>;

  @ApiProperty({ required: true, example: 1 })
  total: number;
}

export class GetAllDashboardForbiddenResDto {
  @ApiProperty({ required: true, example: 403 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Forbidden' })
  error: string;
}