import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";

class RealTime {
  @ApiProperty({ type: "string", example: 22539 })
  @IsNumber()
  @IsNotEmpty()
  meas_id: string;

  @ApiProperty({ required: false, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  use_asset_tz?: boolean;

  @ApiProperty({ required: false, type: "string", example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  browser_tz?: string;
}

class AggregateDto {
  @ApiProperty({ example: 22539 })
  @IsNumber()
  @IsNotEmpty()
  meas_id: number;

  @ApiProperty({ type: "number", example: 1711469468822 })
  @IsNumber()
  @IsNotEmpty()
  start: number;

  @ApiProperty({ required: false, example: 1714061468822 })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  end?: number;

  @ApiProperty({ required: false, type: "string", example: "twa" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  agg?: string;

  @ApiProperty({ required: false, type: "string", example: "DAILY" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  agg_period?: string;

  @ApiProperty({ required: false, type: "boolean", example: true })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  use_asset_tz?: boolean;

  @ApiProperty({ required: false, type: "string", example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  browser_tz?: string;
}

export class PostAllTimeSeriesDto {
  @ApiProperty({
    example: [
      {
        meas_id: 22539,
        start: 1711469468822,
        end: 1714061468822,
        agg: "twa",
        agg_period: "DAILY",
        use_asset_tz: true,
        browser_tz: "Asia/Calcutta",
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  @Type(() => AggregateDto)
  agg: AggregateDto[];
}

class AggResData {
  @ApiProperty({ type: "number", example: 22539 })
  meas_id: number;

  @ApiProperty({ type: "array", example: [1713975068822, 31.45845838993349] })
  timeseries: [];

  @ApiProperty({
    type: "string",
    example: "up",
    examples: ["up", "down", "same"],
  })
  status: string;

  @ApiProperty({
    type: "array",
    example: {
      tag: 22539,
      agg: "avg",
      period: "DAILY",
      "ts,val": [[1714435242000, 23.33513605442179]],
    },
  })
  data: [];
}

class RealTimeResData {
  @ApiProperty({ type: "number", example: 22539 })
  meas_id: number;

  @ApiProperty({ type: "number", example: 12.7955 })
  realTimeValue: number;

  @ApiProperty({
    type: "array",
    example: {
      tag: 22539,
      agg: "avg",
      period: "DAILY",
      "ts,val": [[1714435242000, 23.33513605442179]],
    },
  })
  data: [];
}

export class PostAllTimeSeriesResDto {
  @ApiProperty({
    isArray: true,
    type: RealTimeResData,
    example: [
      {
        meas_id: 22539,
        realTimeValue: 12.7955,
        data: [
          {
            tag: 22539,
            ts: 1714479840000,
            val: 12.7955,
          },
        ],
      },
    ],
  })
  realtime: RealTimeResData[];

  @ApiProperty({
    isArray: true,
    type: AggResData,
    example: [
      {
        meas_id: 22539,
        timeseries: [1714435242000, 23.33513605442179],
        status: "same",
        data: [
          {
            tag: 22539,
            agg: "avg",
            period: "DAILY",
            "ts,val": [[1714435242000, 23.33513605442179]],
          },
        ],
      },
    ],
  })
  agg: AggResData[];
}

export class GetForecastDto {
  @ApiProperty({ type: "string", example: "22443" })
  @IsString()
  @IsNotEmpty()
  meas_id: string;

  @ApiProperty({ type: "string", enum: ["24hr", "eom"], example: "eom" })
  @IsEnum(["24hr", "eom"])
  @IsString()
  @IsNotEmpty()
  forecast: string;

  @ApiProperty({ required: false, type: "string", example: "UTC" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  use_asset_tz?: string;

  @ApiProperty({
    required: false,
    type: "string",
    enum: ["true", "false"],
    example: "false",
  })
  @IsString()
  @IsOptional()
  browser_tz?: string;

  @ApiProperty({ required: false, type: "string", example: "twa" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  agg?: string;
}

class assetTz {
  @ApiProperty({ example: "US/Pacific" })
  asset_tz: string;

  @ApiProperty({ example: -25200000 })
  utc_offset: number;
}

class tagMeta {
  @ApiProperty({ example: "%RH" })
  uom: string;
}

export class ForecastResponseDto {
  @ApiProperty({ example: 22443 })
  tag: number;

  @ApiProperty({ example: "eom" })
  forecast: string;

  @ApiProperty({
    example: {
      asset_tz: "US/Pacific",
      utc_offset: -25200000,
    },
  })
  asset_tz: assetTz;

  @ApiProperty({
    example: [
      [1715929200000, 84.8571428571444],
      [1717200000000, 93.93428571428876],
    ],
  })
  "ts,val": Array<[number, number]>;

  @ApiProperty({ example: 0.039086247 })
  proc_time_sec: number;

  @ApiProperty({
    example: {
      uom: "%RH",
    },
  })
  tag_meta: tagMeta;
}

export class GetAggregationDto {
  @ApiProperty({ required: true, example: "22539" })
  @IsString()
  @IsNotEmpty()
  meas_id: string;

  @ApiProperty({ required: true, example: "1711469468822" })
  @IsString()
  @IsNotEmpty()
  start: string;

  @ApiProperty({ required: false, example: "1714061468822" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  end?: string;

  @ApiProperty({ required: false, example: "twa" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  agg?: string;

  @ApiProperty({ required: false, example: "DAILY" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  agg_period?: string;

  @ApiProperty({ required: false, example: "true" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  use_asset_tz?: string;

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsArray()
  @IsNotEmpty()
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  groupby?: string | string[];

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  "x-usepersistedcalcs"?: string;

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  browser_tz?: string;
}

export class GetCurrentTimeSeriesDto {
  @ApiProperty({ required: true, example: "22539" })
  @IsString()
  @IsNotEmpty()
  meas_id: string;

  @ApiProperty({ required: false, example: "true" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  "x-usepersistedcalcs"?: string;

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  "browser_tz"?: string;
}

export class GetHistoryTimeSeriesDto {
  @ApiProperty({ required: true, example: "22539" })
  @IsString()
  @IsNotEmpty()
  meas_id: string;

  @ApiProperty({ required: true, example: "1711469468822" })
  @IsString()
  @IsNotEmpty()
  start: string;

  @ApiProperty({ required: false, example: "1714061468822" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  end?: string;

  @ApiProperty({ required: false, example: "twa" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  use_asset_tz?: string;

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  "x-usepersistedcalcs"?: string;

  @ApiProperty({ required: false, example: "Asia/Calcutta" })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  browser_tz?: string;
}

export class GetLastReadingsDto {
  @ApiProperty({ required: true, example: "22539" })
  @IsString()
  @IsNotEmpty()
  meas_id: string;
}
