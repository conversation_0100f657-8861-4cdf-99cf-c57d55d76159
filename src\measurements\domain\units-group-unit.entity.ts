import {
  Entity,
  ManyToOne,
  <PERSON>Key,
  Property,
  Ref
} from '@mikro-orm/core';
import { User } from 'src/users/domain/user.entity';
import { UnitOfMeasure } from './unit-of-measure.entity';
import { UnitsGroup } from './units-group.entity';

@Entity()
export class UnitsGroupUnit {
  @PrimaryKey()
  id!: number;

  @ManyToOne({
    entity: () => UnitOfMeasure,
    fieldName: 'unit_of_measure',
  })
  unitOfMeasure!: Ref<UnitOfMeasure>;

  @ManyToOne({
    entity: () => UnitsGroup,
    fieldName: 'units_group',
    onDelete: 'cascade',
  })
  unitsGroup!: Ref<UnitsGroup>;

  // @ManyToMany({
  //   entity: () => UnitsGroup,
  //   pivotEntity: () => UnitsGroupDefaultUnit,
  //   eager: true,
  // })
  // unitsGroupDefaultUnit = new Collection<UnitsGroup>(this);

  // isDefaultUnit(unitsGroupId: UnitsGroupId) {
  //   return (
  //     this.unitsGroupDefaultUnit
  //       .getItems()
  //       .find((unitsGroup) => unitsGroup.id === unitsGroupId) !== undefined
  //   );
  // }

  @Property()
  is_default!: boolean;

  @Property({ length: 6, nullable: true })
  createdAt?: Date;

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;
}
