import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230421184932 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "location" ("id" serial primary key, "name" varchar(50) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "location" add constraint "location_name_key" unique ("name");',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "location" cascade;');
  }
}
