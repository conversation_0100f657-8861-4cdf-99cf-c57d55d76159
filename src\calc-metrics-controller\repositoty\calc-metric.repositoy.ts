import { EntityRepository } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { ConflictException, NotFoundException } from "@nestjs/common";
import { Metric } from "src/assets/domain/metric.entity";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { User } from "src/users/domain/user.entity";
import { CalculationMetricInput } from "../domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "../domain/calculation-metric-instance.entity";
import { CalcMetricDTO } from "../dto/calculation-metric-create.dto";

export class CalcMetricRepository {
  constructor(
    @InjectRepository(CalculationTemplate)
    private calculationTemplateRepo: EntityRepository<CalculationTemplate>,
    @InjectRepository(CalculationMetricInput)
    private calcMetricsInputRepository: EntityRepository<CalculationMetricInput>,
    @InjectRepository(CalculationMetricInstance)
    private calcMetricInstanceRepository: EntityRepository<CalculationMetricInstance>,
    private readonly transactionFactory: TransactionFactory,
    @InjectRepository(Metric)
    private metricRepository: EntityRepository<Metric>
  ) {}

  async createCalcMetrics(calcMetricList: CalcMetricDTO[], user: User) {
    return await this.transactionFactory.run(async () => {
      const allCreatedInputs = [];

      // === Step 1: Perform All Validations Upfront ===

      // 1. Validate all templates exist
      const templateIds = Array.from(
        new Set(calcMetricList.map((cm) => cm.templateId))
      );
      const templates = await this.calculationTemplateRepo.find({
        id: { $in: templateIds },
      });
      const foundTemplateIds = templates.map((t) => t.id);
      const missingTemplates = templateIds.filter(
        (id) => !foundTemplateIds.includes(id)
      );
      if (missingTemplates.length > 0) {
        throw new NotFoundException(
          `Templates not found: ${missingTemplates.join(", ")}`
        );
      }

      // 2. Validate all have at least one measurement input
      for (const cm of calcMetricList) {
        const hasMeasurement = cm.inputs.some(
          (input) => input?.metric !== undefined
        );
        if (!hasMeasurement) {
          throw new ConflictException(
            `Calculation metric with outputMetric ${cm.metricId} has no measurement input`
          );
        }
      }

      // 3. Check if any outputMetric already exists
      const outputMetricIds = calcMetricList.map((cm) => cm.metricId);
      const existingInstances = await this.calcMetricInstanceRepository.find({
        outputMetric: { $in: outputMetricIds },
      });

      if (existingInstances.length > 0) {
        const existingIds = existingInstances
          .map((e) => e.outputMetric.id)
          .join(", ");
        throw new ConflictException(
          `Output metrics already exist for IDs: ${existingIds}`
        );
      }

      for (const calcMetric of calcMetricList) {
        const instance = this.calcMetricInstanceRepository.create({
          calculation: calcMetric.templateId,
          ispersisted: calcMetric.ispersisted,
          outputMetric: calcMetric.metricId,
          pollPeriod: calcMetric.pollPeriod,
          created: new Date(),
          createdby: user.id,
        });
        await this.calcMetricInstanceRepository.persistAndFlush(instance);

        const inputsForThisInstance = [];

        for (const input of calcMetric.inputs) {
          const calcInput = this.calcMetricsInputRepository.create({
            created: new Date(),
            createdby: user.id,
            calculationMetricInstance: instance.id,
            inputLabel: input.inputLabel,
            comment: input.comment ?? "",
          });

          if (input.metric) {
            const metric = await this.metricRepository.findOne({
              id: input.metric,
            });
            if (!metric) {
              throw new ConflictException(
                `Measurement with ID ${input.metric} not found`
              );
            }
            calcInput.metric = metric;
          } else {
            calcInput.constantNumber =
              input.constantType === "number" ? input.constantValue : null;
            calcInput.constantString =
              input.constantType === "string" ? input.constantValue : null;
          }

          await this.calcMetricsInputRepository.persistAndFlush(calcInput);
          inputsForThisInstance.push(calcInput);
        }

        allCreatedInputs.push({
          instanceId: instance.id,
          outputMetricId: calcMetric.metricId,
          templateId: calcMetric.templateId,
          inputs: inputsForThisInstance,
        });
      }
      return allCreatedInputs;
    });
  }
}
