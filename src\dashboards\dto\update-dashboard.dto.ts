import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateDashboardDto {
  @ApiProperty({ example: "dashboard-123" })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ example: "{apple:ioioiio}" })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  data: string;
}

export class UpdateDashboardConflictResDto {
  @ApiProperty({ required: true, example: 409 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Dashboard already exists' })
  message: string;

  @ApiProperty({ required: true, example: 'Conflict' })
  error: string;
}