{"name": "brompton-admin-api", "version": "0.2.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": "16.15"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=production node main", "deploy:dev": "NODE_ENV=dev node main", "deploy:test": "NODE_ENV=test node main", "deploy:prod": "NODE_ENV=prod node main", "deploy:production": "NODE_ENV=production node main", "start:dev": "npx cross-env NODE_ENV=dev nest start --watch", "start:development": "npx cross-env NODE_ENV=development nest start --watch", "start:mqtt:dev": "npx cross-env NODE_ENV=development nest start --watch", "start:test": "NODE_ENV=test nest start --watch", "start:debug": "NODE_ENV=development nest start --debug --watch", "start:test:debug": "NODE_ENV=test nest start --debug --watch", "start:prod": "NODE_ENV=prod node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=integration-test jest -i --config ./test/jest-e2e.json", "test:e2e:debug": "NODE_ENV=integration-test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest -i --config ./test/jest-e2e.json --runInBand --testTimeout=100000000", "test:migration": "NODE_ENV=integration-test jest -i --config ./test/jest-migration.json", "test:migration:debug": "NODE_ENV=integration-test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest -i --config ./test/jest-migration.json", "migrations:up:dev": "NEW_DB=true MIKRO_ORM_ENV=.development.env mikro-orm migration:up", "migrations:reset:dev": "NEW_DB=true MIKRO_ORM_ENV=.development.env mikro-orm migration:fresh", "migrations:up:test": "MIKRO_ORM_ENV=.test.env mikro-orm migration:up", "migrations:up:prod": ".prod.env mikro-orm migration:up", "clear-cache": "mikro-orm cache:clear"}, "dependencies": {"@aws-sdk/client-ses": "^3.651.1", "@aws-sdk/client-sns": "^3.651.1", "@grpc/proto-loader": "^0.7.13", "@mikro-orm/cli": "^5.9.0", "@mikro-orm/core": "^5.9.0", "@mikro-orm/entity-generator": "^5.9.0", "@mikro-orm/migrations": "^5.9.0", "@mikro-orm/nestjs": "^5.1.6", "@mikro-orm/postgresql": "^5.9.0", "@mikro-orm/reflection": "^5.9.0", "@nestjs/axios": "^3.0.1", "@nestjs/cli": "^9.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.0", "@nestjs/core": "^9.3.2", "@nestjs/event-emitter": "^2.0.2", "@nestjs/jwt": "^10.0.1", "@nestjs/passport": "^9.0.1", "@nestjs/platform-express": "^9.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^6.2.1", "@sendgrid/mail": "^8.1.3", "amqplib": "^0.10.8", "argon2": "^0.30.3", "aws-sdk": "^2.1691.0", "axios": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "grpc": "^1.24.11", "kafkajs": "^2.2.4", "nest-winston": "^1.9.7", "nestjs-twilio": "^4.4.0", "node-gyp": "^9.3.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "protobufjs": "^7.4.0", "redis": "^4.6.6", "rxjs": "^7.8.1", "uuid": "^10.0.0", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.3.2", "@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/node": "18.11.18", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.3.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "modulePaths": ["node_modules", "<rootDir>"], "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "mikro-orm": {"useTsNode": true, "configPaths": ["./src/mikro-orm.config.ts", "./dist/mikro-orm.config.js"]}}