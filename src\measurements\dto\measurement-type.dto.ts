import { ApiProperty } from "@nestjs/swagger";
import {
  IsNotEmpty,
  IsString,
  MinLength
} from "class-validator";

export class CreateMeasurementTypeDto {
  @ApiProperty({
    example: "Voltage",
    description: "Name of the measurement type",
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  name: string;
}

export class UpdateMeasurementTypeDto {
  @ApiProperty({
    example: "Updated Voltage",
    description: "Updated name of the measurement type",
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  name: string;
}
