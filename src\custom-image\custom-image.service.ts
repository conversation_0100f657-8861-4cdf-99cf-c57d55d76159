import { Injectable } from "@nestjs/common";
import { CustomerService } from "src/customers/customer.service";
import { User } from "src/users/domain/user.entity";
import { CustomImageRepository } from "./repository/custom-image.repository";
import { CustomImage } from "./domain/custom-image.entity";
import { Reference } from "@mikro-orm/core";

@Injectable()
export class CustomImageService {
  constructor(
    private readonly customImageRepository: CustomImageRepository,
    private readonly customerService: CustomerService
  ) {}

  async create({
    customerId,
    logo,
    user,
  }: {
    customerId: number;
    logo: string;
    user: User;
  }) {
    const findCustomer = await this.customerService.findById(customerId);
    if (!findCustomer) {
      throw new Error("Customer not found");
    }
    const customImage = new CustomImage();
    customImage.createdAt = new Date();
    customImage.createdBy = Reference.createFromPK(User, user.id);
    customImage.logo = logo;
    customImage.customer = findCustomer;
    return await this.customImageRepository.createCustomImage(customImage);
  }

  async getCustomerImage(customerId: number) {
    return await this.customImageRepository.getCustomImage(customerId);
  }

  async getImage(imageId: number) {
    return await this.customImageRepository.getImage(imageId);
  }
}
