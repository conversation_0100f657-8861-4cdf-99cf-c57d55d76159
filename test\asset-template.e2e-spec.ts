import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { deleteRedisKey, loginUser, setupApp } from './test-utils';
import { UserFixture, userFixtureFactory } from './fixtures/user.fixture';
import request from 'supertest';
import {
  assetFixtureFactory,
  deleteAssetByTag,
} from './fixtures/asset.fixture';
import { customerFixtureFactory } from './fixtures/customer.fixture';
import {
  AssetTemplateFixtureFactory,
  deleteAssetTemplateByManufacturer,
} from './fixtures/asset-template.fixture';
import {
  deleteMeasurementByTag,
  measurementFixtureFactory,
} from './fixtures/measurement.fixture';
import { ASSET_VISIBLE_FIELDS } from './asset.e2e-spec';
import { ASSET_MEASUREMENT_VISIBLE_FIELDS } from './measurement.e2e-spec';
import {
  UnitsGroupEuropeFixture,
  UnitsGroupFixtureFactory,
} from './fixtures/units-group.fixture';
import { AwaitedReturn } from './fixtures/type-utilities';

const ASSET_MEASUREMENT_INSTANCE_VISIBLE_FIELDS = new Set(
  [...ASSET_MEASUREMENT_VISIBLE_FIELDS].filter(
    (key) => !['asset_id'].includes(key),
  ),
);

const ASSET_TEMPLATE_MEASUREMENT_VISIBLE_FIELDS = new Set(
  [...ASSET_MEASUREMENT_INSTANCE_VISIBLE_FIELDS].filter(
    (key) =>
      !['id', 'measurement_id', 'tag', 'unit_of_measure_id'].includes(key),
  ),
);

const ASSET_INSTANCE_VISIBLE_FIELDS = new Set([
  ...ASSET_VISIBLE_FIELDS,
  'latitude',
  'longitude',
]);

describe('/assets-backoffice/asset-types/{assetTypeId}/asset-templates', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let userFixture: UserFixture;
  let kawasakiCustomerFixture;
  let httpClient: request.SuperAgentTest;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    userFixture = await userFixtureFactory.createSuperUser(testingModule);
    kawasakiCustomerFixture = await customerFixtureFactory.createKawasaki(
      testingModule,
      userFixture.superUserId,
    );

    httpClient = await loginUser(
      app.getHttpServer(),
      userFixture.username,
      userFixture.password,
    );
  });

  afterAll(async () => {
    await userFixture.cleanUp();
    await kawasakiCustomerFixture.cleanUp();
    await app.close();
  });

  describe('POST', () => {
    let response: request.Response;
    let measurementFixture: AwaitedReturn<
      typeof measurementFixtureFactory.createMetadata
    >;
    let enginePowerMetricFixture: AwaitedReturn<
      typeof assetFixtureFactory.createEnginePowerMetric
    >;

    beforeAll(async () => {
      measurementFixture = await measurementFixtureFactory.createMetadata(
        testingModule,
      );

      enginePowerMetricFixture =
        await assetFixtureFactory.createEnginePowerMetric(testingModule);

      response = await httpClient
        .post(
          `/v0/assets-backoffice/asset-types/${enginePowerMetricFixture.engineAssetTypeId}\
          /asset-templates`,
        )
        .send({
          manufacturer: 'Toyota',
          model_number: 'Hilux',
          measurements: [
            {
              metric_id: enginePowerMetricFixture.powerMetricId,
              type_id: measurementFixture.powerTypeId,
              data_type_id: measurementFixture.realDataTypeId,
              value_type_id: measurementFixture.nominalValueTypeId,
            },
          ],
        });
    });

    afterAll(async () => {
      await deleteAssetTemplateByManufacturer(testingModule, 'Toyota');
      await measurementFixture.cleanUp();
      await enginePowerMetricFixture.cleanUp();
    });

    test('should return a 201', async () => {
      expect(response.statusCode).toBe(201);
    });

    it('should return all visible fields for created asset template', () => {
      expect(new Set(Object.keys(response.body))).toEqual(
        new Set([
          'id',
          'manufacturer',
          'model_number',
          'asset_type_id',
          'measurements',
        ]),
      );
      expect(new Set(Object.keys(response.body.measurements[0]))).toEqual(
        ASSET_TEMPLATE_MEASUREMENT_VISIBLE_FIELDS,
      );
    });

    it('should return correct new asset template data', () => {
      expect(response.body.manufacturer).toBe('Toyota');
      expect(response.body.model_number).toBe('Hilux');
      expect(response.body.asset_type_id).toBe(
        enginePowerMetricFixture.engineAssetTypeId,
      );
      const measurementTemplate = response.body.measurements[0];
      expect(measurementTemplate.metric_id).toBe(
        enginePowerMetricFixture.powerMetricId,
      );
      expect(measurementTemplate.type_id).toBe(measurementFixture.powerTypeId);
      expect(measurementTemplate.data_type_id).toBe(
        measurementFixture.realDataTypeId,
      );
      expect(measurementTemplate.value_type_id).toBe(
        measurementFixture.nominalValueTypeId,
      );
      expect(measurementTemplate.location_id).toBeNull();
      expect(measurementTemplate.description).toBeNull();
      expect(measurementTemplate.datasource_id).toBeNull();
      expect(measurementTemplate.meter_factor).toBeNull();
    });
  });

  describe('GET', () => {
    let response: request.Response;
    let bikeEngineTemplateFixture: Awaited<
      ReturnType<AssetTemplateFixtureFactory['createBikeEngineTemplate']>
    >;

    beforeAll(async () => {
      bikeEngineTemplateFixture = await new AssetTemplateFixtureFactory(
        testingModule,
      ).createBikeEngineTemplate(userFixture.superUserId);

      const engineAssetTypeId = bikeEngineTemplateFixture.engineAssetTypeId;

      response = await httpClient.get(
        `/v0/assets-backoffice/asset-types/${engineAssetTypeId}\
          /asset-templates`,
      );
    });

    afterAll(async () => {
      await bikeEngineTemplateFixture.cleanUp();
    });

    it('should return a 200', () => {
      expect(response.statusCode).toBe(200);
    });

    it('should return a list with one element', () => {
      expect(response.body.total).toBe(1);
      expect(response.body.items.length).toBe(1);
    });

    it('should return all available asset templates for asset type', () => {
      expect(response.body.items[0].id).toBe(
        bikeEngineTemplateFixture.bikeEngineTemplateId,
      );
    });

    it('should return all visible fields for given asset template', () => {
      expect(new Set(Object.keys(response.body.items[0]))).toEqual(
        new Set([
          'id',
          'manufacturer',
          'model_number',
          'asset_type_id',
          'measurements',
        ]),
      );
      expect(
        new Set(Object.keys(response.body.items[0].measurements[0])),
      ).toEqual(ASSET_TEMPLATE_MEASUREMENT_VISIBLE_FIELDS);
    });

    it('should return correct measurement template data', () => {
      const measurementTemplate = response.body.items[0].measurements[0];
      expect(measurementTemplate.metric_id).toBe(
        bikeEngineTemplateFixture.powerMetricId,
      );
      expect(measurementTemplate.type_id).toBe(
        bikeEngineTemplateFixture.powerTypeId,
      );
      expect(measurementTemplate.data_type_id).toBe(
        bikeEngineTemplateFixture.realDataTypeId,
      );
      expect(measurementTemplate.value_type_id).toBe(
        bikeEngineTemplateFixture.nominalValueDataTypeId,
      );
      expect(measurementTemplate.location_id).toBe(
        bikeEngineTemplateFixture.outputLocationId,
      );
      expect(measurementTemplate.description).toBeNull();
      expect(measurementTemplate.datasource_id).toBeNull();
      expect(measurementTemplate.meter_factor).toBeNull();
    });
  });

  describe('POST .../{assetTemplateId}/instances', () => {
    let response: request.Response;
    let officeSiteFixture: AwaitedReturn<
      typeof assetFixtureFactory.createOfficeSite
    >;
    let bikeEngineTemplateFixture: AwaitedReturn<
      AssetTemplateFixtureFactory['createBikeEngineTemplate']
    >;
    let europeUnitsGroupFixture: UnitsGroupEuropeFixture;

    describe('create template with no overrides', () => {
      beforeAll(async () => {
        officeSiteFixture = await assetFixtureFactory.createOfficeSite(
          testingModule,
          userFixture.superUserId,
          kawasakiCustomerFixture.customerId,
        );

        bikeEngineTemplateFixture = await new AssetTemplateFixtureFactory(
          testingModule,
        ).createBikeEngineTemplate(userFixture.superUserId);

        europeUnitsGroupFixture = await new UnitsGroupFixtureFactory(
          testingModule,
        ).createEuropeUnitsGroup(bikeEngineTemplateFixture.powerTypeId);

        const engineAssetTypeId = bikeEngineTemplateFixture.engineAssetTypeId;
        const bikeEngineTemplateId =
          bikeEngineTemplateFixture.bikeEngineTemplateId;

        response = await httpClient
          .post(
            `/v0/assets-backoffice/asset-types/${engineAssetTypeId}\
          /asset-templates/${bikeEngineTemplateId}/instances`,
          )
          .send({
            units_group_id: europeUnitsGroupFixture.europeUnitsGroupId,
            asset: {
              tag: 'Office Test Bike Engine',
              description: 'Bike engine used for testing',
              latitude: 34.4,
              longitude: 44.3,
              parent_ids: [officeSiteFixture.officeAssetId],
              time_zone: 'America/Argentina/Buenos_Aires',
              customer_id: kawasakiCustomerFixture.customerId,
            },
            measurements: [],
          });
      });

      afterAll(async () => {
        await deleteAssetByTag(testingModule, 'Office Test Bike Engine');
        await deleteMeasurementByTag(testingModule, 'Power');
        await europeUnitsGroupFixture.cleanUp();
        await bikeEngineTemplateFixture.cleanUp();
        await officeSiteFixture.cleanUp();

        await deleteRedisKey(
          testingModule,
          response.body.measurements[0].measurement_id,
        );
      });

      test('should return a 201', async () => {
        expect(response.statusCode).toBe(201);
      });

      it('should expose only visible unit of asset measurement fields', () => {
        expect(new Set(Object.keys(response.body))).toEqual(
          new Set(['units_group_id', 'asset', 'measurements']),
        );
        expect(new Set(Object.keys(response.body.asset))).toEqual(
          ASSET_INSTANCE_VISIBLE_FIELDS,
        );
        expect(new Set(Object.keys(response.body.measurements[0]))).toEqual(
          ASSET_MEASUREMENT_INSTANCE_VISIBLE_FIELDS,
        );
      });

      it('should return new asset with an id', () => {
        expect(response.body.asset.id).not.toBeUndefined();
        expect(response.body.asset.id).not.toBeNull();
      });

      it('should return new asset data', () => {
        expect(response.body.asset.tag).toBe('Office Test Bike Engine');
        expect(response.body.asset.description).toBe(
          'Bike engine used for testing',
        );
        expect(response.body.asset.latitude).toBe(34.4);
        expect(response.body.asset.longitude).toBe(44.3);
        expect(response.body.asset.parent_ids).toStrictEqual([
          officeSiteFixture.officeAssetId,
        ]);
        expect(response.body.asset.children_ids).toStrictEqual([]);
        expect(response.body.asset.time_zone).toBe(
          'America/Argentina/Buenos_Aires',
        );
        expect(response.body.asset.customer_id).toBe(
          kawasakiCustomerFixture.customerId,
        );
      });

      it('should return units group id', () => {
        expect(response.body.units_group_id).not.toBeUndefined();
        expect(response.body.units_group_id).not.toBeNull();
      });

      it('should return new measurement with generated ids', () => {
        expect(response.body.measurements[0].id).not.toBeUndefined();
        expect(response.body.measurements[0].id).not.toBeNull();
        expect(
          response.body.measurements[0].measurement_id,
        ).not.toBeUndefined();
        expect(response.body.measurements[0].measurement_id).not.toBeNull();
      });

      it('should return new measurement data', () => {
        const newMeasurement = response.body.measurements[0];
        expect(newMeasurement.metric_id).toBe(
          bikeEngineTemplateFixture.powerMetricId,
        );
        expect(newMeasurement.tag).toBe('Power');
        expect(newMeasurement.type_id).toBe(
          bikeEngineTemplateFixture.powerTypeId,
        );
        expect(newMeasurement.data_type_id).toBe(
          bikeEngineTemplateFixture.realDataTypeId,
        );
        expect(newMeasurement.value_type_id).toBe(
          bikeEngineTemplateFixture.nominalValueDataTypeId,
        );
        expect(newMeasurement.location_id).toBe(
          bikeEngineTemplateFixture.outputLocationId,
        );
        expect(newMeasurement.description).toBeNull();
        expect(newMeasurement.datasource_id).toBeNull();
        expect(newMeasurement.meter_factor).toBeNull();
        expect(newMeasurement.unit_of_measure_id).toBe(
          europeUnitsGroupFixture.wattUnitOfMeasureId,
        );
      });
    });
    describe('create template with overrides', () => {
      beforeAll(async () => {
        officeSiteFixture = await assetFixtureFactory.createOfficeSite(
          testingModule,
          userFixture.superUserId,
          kawasakiCustomerFixture.customerId,
        );

        bikeEngineTemplateFixture = await new AssetTemplateFixtureFactory(
          testingModule,
        ).createBikeEngineTemplate(userFixture.superUserId);

        europeUnitsGroupFixture = await new UnitsGroupFixtureFactory(
          testingModule,
        ).createEuropeUnitsGroup(bikeEngineTemplateFixture.powerTypeId);

        const engineAssetTypeId = bikeEngineTemplateFixture.engineAssetTypeId;
        const bikeEngineTemplateId =
          bikeEngineTemplateFixture.bikeEngineTemplateId;
        response = await httpClient
          .post(
            `/v0/assets-backoffice/asset-types/${engineAssetTypeId}\
          /asset-templates/${bikeEngineTemplateId}/instances`,
          )
          .send({
            units_group_id: europeUnitsGroupFixture.europeUnitsGroupId,
            asset: {
              tag: 'Office Test Bike Engine',
              description: 'Bike engine used for testing',
              latitude: 34.4,
              longitude: 44.3,
              parent_ids: [officeSiteFixture.officeAssetId],
              time_zone: 'America/Argentina/Buenos_Aires',
              customer_id: kawasakiCustomerFixture.customerId,
            },
            measurements: [
              {
                metric_id: bikeEngineTemplateFixture.powerMetricId,
                tag: 'Test-Bike-Engine/Power',
                description: 'Power consumption',
                unit_of_measure_id:
                  europeUnitsGroupFixture.jouleUnitOfMeasureId,
                meter_factor: 3.3,
              },
            ],
          });
      });

      afterAll(async () => {
        await deleteAssetByTag(testingModule, 'Office Test Bike Engine');
        await deleteMeasurementByTag(testingModule, 'Test-Bike-Engine/Power');
        await europeUnitsGroupFixture.cleanUp();
        await bikeEngineTemplateFixture.cleanUp();
        await officeSiteFixture.cleanUp();

        await deleteRedisKey(
          testingModule,
          response.body.measurements[0].measurement_id,
        );
      });

      test('should return a 201', async () => {
        expect(response.statusCode).toBe(201);
      });

      it('should return new measurement data', () => {
        const newMeasurement = response.body.measurements[0];
        expect(newMeasurement.metric_id).toBe(
          bikeEngineTemplateFixture.powerMetricId,
        );
        expect(newMeasurement.tag).toBe('Test-Bike-Engine/Power');
        expect(newMeasurement.type_id).toBe(
          bikeEngineTemplateFixture.powerTypeId,
        );
        expect(newMeasurement.data_type_id).toBe(
          bikeEngineTemplateFixture.realDataTypeId,
        );
        expect(newMeasurement.value_type_id).toBe(
          bikeEngineTemplateFixture.nominalValueDataTypeId,
        );
        expect(newMeasurement.location_id).toBe(
          bikeEngineTemplateFixture.outputLocationId,
        );
        expect(newMeasurement.description).toBe('Power consumption');
        expect(newMeasurement.datasource_id).toBeNull();
        expect(newMeasurement.meter_factor).toBe(3.3);
        expect(newMeasurement.unit_of_measure_id).toBe(
          europeUnitsGroupFixture.jouleUnitOfMeasureId,
        );
      });
    });
  });
});
