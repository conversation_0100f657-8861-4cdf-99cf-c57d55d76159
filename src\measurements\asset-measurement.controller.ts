import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Patch,
  Post,
  UseGuards,
} from "@nestjs/common";
import { ApiCreatedResponse, ApiOkResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "src/users/domain/user.entity";
import { AssetMeasurementService } from "./asset-measurement.service";
import {
  AssetMeasurementCreationDto,
  AssetMeasurementDto,
  AssetMeasurementUpdateDto,
} from "./dto/asset-measurement.dto";
import { CookieToken } from "src/authentication/infra/cookieToken";

@Controller({
  version: "0",
  path: "customers/:customerId/assets/:assetId/measurements",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetMeasurementApiController {
  constructor(
    private readonly assetMeasurementService: AssetMeasurementService
  ) {}

  @Post()
  @ApiCreatedResponse({ type: AssetMeasurementDto })
  @HasCustomerRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("assetId") assetId: string,
    @Body() newMeasurement: AssetMeasurementCreationDto,
    @CookieToken() headers: Request["headers"]
  ) {
    return await this.assetMeasurementService.create(
      {
        ...newMeasurement,
        typeId: newMeasurement.type_id,
        dataTypeId: newMeasurement.data_type_id,
        unitOfMeasureId: newMeasurement.unit_of_measure_id,
        valueTypeId: newMeasurement.value_type_id,
        locationId: newMeasurement.location_id,
        datasourceId: newMeasurement.datasource_id,
        assetId: Number(assetId),
        meterFactor: newMeasurement.meter_factor,
      },
      Number(customerId),
      authUser.id,
      headers
    );
  }

  @Get()
  @HasCustomerRole(Role.USER)
  async getAll(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("assetId") assetId: string
  ) {
    const items = await this.assetMeasurementService.getAll(
      Number(customerId),
      Number(assetId)
    );

    return {
      items,
      total: items.length,
    };
  }

  @Get(":assetMeasurementId")
  @ApiOkResponse({ type: AssetMeasurementDto })
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @HasCustomerRole(Role.USER)
  async getById(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("assetId") assetId: string,
    @Param("assetMeasurementId") assetMeasurementId: string
  ) {
    const assetMeasurement = await this.assetMeasurementService.getById(
      Number(customerId),
      Number(assetId),
      Number(assetMeasurementId)
    );

    if (assetMeasurement === null) {
      throw new NotFoundException();
    }

    return assetMeasurement;
  }

  @Delete(":assetMeasurementId")
  @HttpCode(204)
  @HasCustomerRole(Role.POWER_USER)
  async removeById(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("assetId") assetId: string,
    @Param("assetMeasurementId") assetMeasurementId: string,
    @CookieToken() headers: Request["headers"]
  ) {
    await this.assetMeasurementService.removeById(
      Number(customerId),
      Number(assetId),
      Number(assetMeasurementId),
      authUser.id,
      headers
    );
  }

  @Patch(":assetMeasurementId")
  @HttpCode(204)
  @HasCustomerRole(Role.POWER_USER)
  async patchById(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("assetId") assetId: string,
    @Param("assetMeasurementId") assetMeasurementId: string,
    @Body() assetMeasurementUpdate: AssetMeasurementUpdateDto,
    @CookieToken() headers: Request["headers"]
  ) {
    await this.assetMeasurementService.update(
      Number(customerId),
      Number(assetId),
      Number(assetMeasurementId),
      {
        ...assetMeasurementUpdate,
        typeId: assetMeasurementUpdate.type_id,
        metricId: assetMeasurementUpdate.metric_id,
        valueTypeId: assetMeasurementUpdate.value_type_id,
        meterFactor: assetMeasurementUpdate.meter_factor,
        unitOfMeasureId: assetMeasurementUpdate.unit_of_measure_id,
        locationId: assetMeasurementUpdate.location_id,
        dataTypeId: assetMeasurementUpdate.data_type_id,
      },
      authUser.id,
      headers
    );
  }
}
