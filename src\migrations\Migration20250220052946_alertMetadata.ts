import { Migration } from '@mikro-orm/migrations';

export class Migration20250220052946_alertMetadata extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "alerts" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null;'
    );
  }

  async down(): Promise<void> {
    this.addSql('alter table "alerts" drop column "created_at";');
    this.addSql('alter table "alerts" drop column "updated_at";');
  }
}
