import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { AssetUpdateData } from 'src/assets/asset.service';
import { Asset } from '../domain/asset.entity';

export class AssetDto implements Pick<Asset, 'id' | 'tag'> {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  tag!: string;

  @ApiProperty()
  type_id!: number;

  @ApiProperty()
  parent_ids!: number[];

  @ApiProperty()
  children_ids!: number[];

  @ApiProperty({ nullable: true, type: String })
  time_zone!: string | null;

  @ApiProperty({ nullable: true, type: Number })
  latitude!: number | null;

  @ApiProperty({ nullable: true, type: Number })
  longitude!: number | null;

  @ApiProperty({ nullable: true, type: String })
  description!: string | null;
}

export class AssetCreationDto extends OmitType(AssetDto, [
  'id',
  'children_ids',
  'time_zone',
  'latitude',
  'longitude',
  'description',
]) {
  @ApiPropertyOptional()
  time_zone?: string;

  @ApiPropertyOptional()
  latitude?: number;

  @ApiPropertyOptional()
  longitude?: number;

  @ApiPropertyOptional()
  description?: string;
}

type AssetUpdateDtoType = Omit<
  AssetUpdateData,
  'assetTypeId' | 'parentIds' | 'timeZone'
> & {
  type_id?: number;
  parent_ids?: number[];
  time_zone?: string;
};

export class AssetUpdateDto implements AssetUpdateDtoType {
  tag?: string;
  type_id?: number;
  latitude?: number;
  longitude?: number;
  description?: string;
  parent_ids?: number[];
  time_zone?: string;
}
