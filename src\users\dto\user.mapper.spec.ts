import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { User, UserFactory } from '../domain/user.entity';
import { UserMapper } from './user.mapper';
import { UserCreationDto } from './user.dto';
import { Test } from '@nestjs/testing';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { Customer } from 'src/customers/domain/customer.entity';

describe('UserMapper', () => {
  const mapper = new UserMapper();

  const commonUserData = {
    username: 'test_user',
    password: 'somepass',
    email: '<EMAIL>',
    firstName: 'fredo',
    lastName: 'lastly',
  };

  beforeAll(async () => {
    await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer])],
    }).compile();
  });

  describe('user to dto', () => {
    test('global admin', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.ADMIN,
      });

      const mappedDto = mapper.toDto(user);

      expect(mappedDto).toStrictEqual({
        email: '<EMAIL>',
        enabled: true,
        first_name: 'fredo',
        global_role: 'ADMIN',
        scoped_roles: [],
        last_name: 'lastly',
        username: 'test_user',
      });
    });

    test('scoped user with multiple customers in different roles', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [
          { role: Role.USER, customerIds: [34] },
          { role: Role.ADMIN, customerIds: [43, 56] },
        ],
      });

      const mappedDto = mapper.toDto(user);

      expect(mappedDto).toStrictEqual({
        email: '<EMAIL>',
        enabled: true,
        first_name: 'fredo',
        global_role: null,
        scoped_roles: [
          {
            role: 'USER',
            customer_ids: [34],
          },
          {
            role: 'ADMIN',
            customer_ids: [43, 56],
          },
        ],
        last_name: 'lastly',
        username: 'test_user',
      });
    });
  });

  describe('user creation dto to domain params', () => {
    test('global power user', () => {
      const userCreation: UserCreationDto = {
        email: '<EMAIL>',
        first_name: 'Carlos',
        last_name: 'Bethesda',
        username: 'cbethesda',
        global_role: 'POWER_USER',
        password: 'carlosbethesda',
      };

      const mappedParams = mapper.creationDtoToParams(userCreation);

      expect(mappedParams).toStrictEqual({
        username: 'cbethesda',
        email: '<EMAIL>',
        password: 'carlosbethesda',
        firstName: 'Carlos',
        lastName: 'Bethesda',
        globalRole: Role.POWER_USER,
        scopedRoles: [],
      });
    });

    test('scoped user with multiple customers in different roles', () => {
      const userCreation: UserCreationDto = {
        email: '<EMAIL>',
        first_name: 'Carlos',
        last_name: 'Bethesda',
        username: 'cbethesda',
        password: 'carlosbethesda',
        scoped_roles: [
          {
            role: 'USER',
            customer_ids: [34],
          },
          {
            role: 'ADMIN',
            customer_ids: [43, 56],
          },
        ],
      };

      const mappedParams = mapper.creationDtoToParams(userCreation);

      expect(mappedParams).toStrictEqual({
        username: 'cbethesda',
        email: '<EMAIL>',
        password: 'carlosbethesda',
        firstName: 'Carlos',
        lastName: 'Bethesda',
        globalRole: undefined,
        scopedRoles: [
          {
            role: Role.USER,
            customerIds: [34],
          },
          {
            role: Role.ADMIN,
            customerIds: [43, 56],
          },
        ],
      });
    });
  });
});
