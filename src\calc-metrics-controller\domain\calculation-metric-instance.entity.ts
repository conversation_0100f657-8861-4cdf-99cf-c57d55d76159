import {
  <PERSON><PERSON>ty,
  <PERSON>ToOne,
  PrimaryKey,
  Property,
  Unique,
} from "@mikro-orm/core";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { Metric } from "src/assets/domain/metric.entity";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";

@Entity()
@Unique({ properties: ["assetTemplate", "outputMetric"] })
export class CalculationMetricInstance {
  @PrimaryKey()
  id!: number;

  @ManyToOne({
    entity: () => AssetTemplate,
    fieldName: "assetTemplate",
    nullable: false,
  })
  assetTemplate!: AssetTemplate;

  @ManyToOne({ entity: () => CalculationTemplate, fieldName: "calculation" })
  calculation!: CalculationTemplate;

  @ManyToOne({
    entity: () => Metric,
    fieldName: "output_metric",
  })
  outputMetric!: Metric;

  @Property()
  ispersisted!: boolean;

  @Property({ default: false })
  writeback!: boolean;

  @Property({ nullable: true })
  pollPeriod?: number;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;
}
