import { Migration } from '@mikro-orm/migrations';

export class Migration20240103223712 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "metric" drop constraint "metric_measurement_type_foreign";',
    );

    this.addSql(
      'alter table "metric" rename column "measurement_type" to "asset_type";',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_asset_type_foreign" foreign key ("asset_type") references "asset_type" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "metric" drop constraint "metric_asset_type_foreign";',
    );

    this.addSql(
      'alter table "metric" rename column "asset_type" to "measurement_type";',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_measurement_type_foreign" foreign key ("measurement_type") references "measurement_type" ("id") on update cascade;',
    );
  }
}
