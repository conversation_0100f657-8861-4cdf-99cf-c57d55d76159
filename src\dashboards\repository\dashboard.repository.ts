import { EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import {
  ConflictException,
  ForbiddenException,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { Asset } from "src/assets/domain/asset.entity";
import { AssetRepository } from "src/assets/repository/asset.repository";
import { DashboardTemplate } from "src/dashboard-template/domain/dashboard-template.entity";
import { AssetMeasurementService } from "src/measurements/asset-measurement.service";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { Customer } from "../../customers/domain/customer.entity";
import { InvalidInputException } from "../../errors/exceptions";
import { User } from "../../users/domain/user.entity";
import { DashboardCreationData } from "../dashboard.service";
import { Dashboard } from "../domain/dashboard.entity";
import {
  ChartMeasureSetting,
  elementVariable,
  ImageTextDetails,
  Widget,
} from "../domain/dashboard.types";
import { DashboardDefault } from "../domain/default-dashboard.entity";
import { DashboardFavorite } from "../domain/favorite-dashboard.entity";
import { DefaultDashboardType } from "../dto/dashboard.dto";
import { UpdateDashboardDto } from "../dto/update-dashboard.dto";
import { TransactionFactory } from "src/db/TransactionFactory";

export class DashboardRepository {
  constructor(
    private readonly logger: Logger,
    @InjectRepository(Dashboard)
    private repository: EntityRepository<Dashboard>,
    @InjectRepository(DashboardDefault)
    private defaultdashboardrepository: EntityRepository<DashboardDefault>,
    @InjectRepository(DashboardFavorite)
    private favoritedashboardrepository: EntityRepository<DashboardFavorite>,
    @InjectRepository(Asset)
    private asset: EntityRepository<Asset>,
    @InjectRepository(DashboardTemplate)
    private dashboardTemplate: EntityRepository<DashboardTemplate>,
    @InjectRepository(Measurement)
    private measurement: EntityRepository<Measurement>,
    @InjectRepository(AssetMeasurement)
    private assetMeasurement: EntityRepository<AssetMeasurement>,
    private readonly assetRepository: AssetRepository,
    private readonly assetMeasurementService: AssetMeasurementService,
    private readonly transactionFactory: TransactionFactory
  ) {}
  public async findAllByCustomerId(
    customerId: number,
    userId: number
  ): Promise<any[]> {
    const dashboards = await this.repository.find(
      { customer: customerId },
      {
        populate: [
          "createdBy",
          "asset",
          "dashboardTemplate.id",
          "dashboardTemplate.title",
        ],
        fields: [
          "id",
          "title",
          "asset",
          "dashboardTemplate.id",
          "dashboardTemplate.title",
          "createdBy",
          "createdAt",
          "updatedAt",
        ],
        orderBy: { id: "DESC" },
      }
    );
    const defaultDashboad = await this.defaultdashboardrepository.findOne({
      user: Reference.createFromPK(User, userId),
      customer: Reference.createFromPK(Customer, customerId),
    });

    const favoriteDashboad = await this.favoritedashboardrepository.find({
      user: Reference.createFromPK(User, userId),
      customer: Reference.createFromPK(Customer, customerId),
    });

    const userIds = dashboards
      .map((d) => d.createdBy?.id)
      .filter((id): id is number => typeof id === "number");

    const userRepo = this.repository.getEntityManager().getRepository(User);
    const users = await userRepo.find(
      { id: { $in: userIds } },
      { fields: ["id", "username"] }
    );
    const userIdToUsername = users.reduce((acc, user) => {
      acc[user.id] = user.username;
      return acc;
    }, {} as Record<number, string>);

    const result = dashboards.map((item) => {
      const dashboard_id = item.id;

      const isDefault =
        defaultDashboad && defaultDashboad.dashboard.id === dashboard_id;

      const isFavorite = favoriteDashboad.some(
        (dash: any) => dash.dashboard.id === dashboard_id
      );

      return {
        id: item.id,
        title: item.title,
        asset: item.asset ?? null,
        dashboardTemplate: item.dashboardTemplate ?? null,
        default: isDefault,
        favorite: isFavorite,
        createdBy: userIdToUsername[item.createdBy?.id] ?? "Unknown",
        updatedAt: item.updatedAt ?? item.createdAt ?? null,
      };
    });

    return result;
  }
  async update(
    dashboard: UpdateDashboardDto,
    userId: number,
    dashboardId: number,
    customerId: number
  ) {
    let d: Dashboard | null = await this.findById(dashboardId);
    if (d) {
      if (dashboard.title) {
        const checkDashboardIsExists = await this.repository.findOne({
          title: dashboard.title,
          id: { $ne: dashboardId },
          customer: Reference.createFromPK(Customer, customerId),
        });

        if (checkDashboardIsExists) {
          throw new ConflictException("Dashboard already exists");
        }

        d.title = dashboard.title;
      }
      dashboard.data ? (d.data = dashboard.data) : null;
      d.updatedBy = Reference.createFromPK(User, userId);
      d.asset = null;
      d.dashboardTemplate = null;
      d.updatedAt = new Date();
      this.repository.getEntityManager().persistAndFlush(d);
    } else {
      throw new InvalidInputException(
        `Dashboard with id "${dashboardId}" does not exists`
      );
    }
  }
  async findById(id: number): Promise<Dashboard | null> {
    return await this.repository.findOne({ id });
  }
  capitalizeFirstLetter(input: string): string {
    return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
  }
  formatMetricLabel(input: string): string {
    if (!input) return "";
    return input
      .split("\\") // Split the string by backslash
      .pop()! // Take the last segment
      .replace(/_/g, " ") // Replace underscores with spaces
      .split(" ") // Split the string by spaces
      .map(this.capitalizeFirstLetter) // Capitalize the first letter of each word
      .join(" "); // Join the words with spaces
  }
  async findByIdWithDefault(
    customerId: number,
    id: number,
    userId: number
  ): Promise<DefaultDashboardType> {
    const dashboardData = await this.repository.findOne({
      id,
      // customer: customerId,
    });
    const dashboard = JSON.parse(JSON.stringify(dashboardData));
    if (!dashboard) {
      return dashboard;
    }
    if (String(dashboardData.customer.id) !== String(customerId)) {
      throw new ForbiddenException("Access to this dashboard is restricted.");
    }
    const allDefaultDashboads = await this.defaultdashboardrepository.findOne({
      dashboard: id,
      customer: customerId,
      user: userId,
    });
    if (dashboardData.asset && dashboardData.dashboardTemplate) {
      const dashboardTemplateData = await this.dashboardTemplate.findOne(
        {
          id: dashboardData.dashboardTemplate.id,
        },
        {
          populate: ["asset_template"],
        }
      );
      if (dashboardTemplateData) {
        const assetMeausements = await this.assetMeasurementService.getAll(
          customerId,
          dashboard.asset
        );
        if (assetMeausements) {
          const assetMeasurementsHasMetrics = assetMeausements.filter(
            (measurement) =>
              measurement.metricId && measurement.metricId !== null
          );
          const assetMeasurementsListMap: Map<
            number,
            { id: number; tag: string }[]
          > = new Map();
          assetMeasurementsHasMetrics?.forEach((item) => {
            if (item.metricId) {
              if (assetMeasurementsListMap.has(item.metricId)) {
                assetMeasurementsListMap.set(item.metricId, [
                  ...(assetMeasurementsListMap.get(item.metricId) ?? []),
                  {
                    id: item.id,
                    tag: item.tag,
                  },
                ]);
              } else {
                assetMeasurementsListMap.set(item.metricId, [
                  {
                    id: item.id,
                    tag: item.tag,
                  },
                ]);
              }
            }
          });
          const dashboardTemplateJSONData = JSON.parse(
            dashboardTemplateData.data
          );
          const widgets = dashboardTemplateJSONData.widget.widgets as Widget[];
          try {
            const dashboardWidgetsAndAllData = JSON.parse(dashboardData.data);
            const updatedWidgets = await Promise.all(
              widgets.map((widget) => {
                const { type, settings } = widget;
                switch (type) {
                  case "stats":
                  case "kpi-percentage":
                  case "kpi-bar-chart":
                  case "kpi-sparkline":
                  case "kpi-value-indicator":
                  case "real-time":
                  case "image-stats":
                  case "kpi-color-box": {
                    const measures =
                      assetMeasurementsListMap.get(
                        Number(settings.selectedDbMeasureId)
                      ) !== undefined &&
                      assetMeasurementsListMap.get(
                        Number(settings.selectedDbMeasureId)
                      );
                    if (measures) {
                      settings.selectedDbMeasureId = measures[0].id.toString();
                      settings.assetMeasure = {
                        assetId: dashboard.asset.toString(),
                        measureId: measures.map((measure) =>
                          measure.id.toString()
                        ),
                      };
                    } else {
                      settings.selectedDbMeasureId = "";
                      settings.assetMeasure = {
                        assetId: dashboardData.asset?.id.toString(),
                        measureId: [],
                      };
                    }
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    settings.mode = "dashboard";
                    break;
                  }
                  case "dashboard-widget": {
                    settings.mode = "dashboard";
                    settings.assetOrAssetType = dashboardData.asset?.id ?? null;
                    settings.assetOption = {
                      id: dashboardData.asset?.id ?? null,
                      label: dashboardData.asset?.tag ?? "",
                    };
                  }
                  case "title": {
                    settings.mode = "dashboard";
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    break;
                  }
                  case "table":
                  case "kpi-table": {
                    const measures = settings.selectedTitles
                      .map((title) => {
                        const measurements: number[] = [];
                        if (
                          assetMeasurementsListMap.get(Number(title)) !==
                          undefined
                        ) {
                          const measureIds =
                            assetMeasurementsListMap
                              .get(Number(title))
                              ?.map((title) => title.id) ?? [];
                          measurements.push(...measureIds);
                        }
                        return measurements;
                      })
                      .flat();
                    settings.selectedTitles = measures.map((measure) =>
                      measure.toString()
                    );
                    settings.assetMeasure = [
                      {
                        assetId: dashboardData.asset?.id.toString(),
                        measureId: measures.map((measure) =>
                          measure.toString()
                        ),
                      },
                    ];
                    settings.mode = "dashboard";
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    break;
                  }
                  case "alert-widget": {
                    const measures = settings.selectedTitles
                      .map((title) => {
                        const measurements: number[] = [];
                        if (
                          assetMeasurementsListMap.get(Number(title)) !==
                          undefined
                        ) {
                          const measureIds =
                            assetMeasurementsListMap
                              .get(Number(title))
                              ?.map((title) => title.id) ?? [];
                          measurements.push(...measureIds);
                        }
                        return measurements;
                      })
                      .flat();
                    settings.selectedTitles = measures.map((measure) =>
                      measure.toString()
                    );
                    settings.assetMeasure = [
                      {
                        assetId: dashboardData.asset?.id.toString(),
                        measureId: measures.map((measure) =>
                          measure.toString()
                        ),
                      },
                    ];
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    settings.mode = "dashboard";
                    break;
                  }
                  case "image": {
                    const measures = settings.selectedTitles
                      .map((title) => {
                        const measurements: number[] = [];
                        if (
                          assetMeasurementsListMap.get(Number(title)) !==
                          undefined
                        ) {
                          const measureIds =
                            assetMeasurementsListMap
                              .get(Number(title))
                              ?.map((title) => title.id) ?? [];
                          measurements.push(...measureIds);
                        }
                        return measurements;
                      })
                      .flat();
                    settings.selectedTitles = measures.map((measure) =>
                      measure.toString()
                    );
                    settings.assetMeasure = [
                      {
                        assetId: dashboardData?.asset?.id.toString(),
                        measureId: measures.map((measure) =>
                          measure.toString()
                        ),
                      },
                    ];
                    const measureIdImageText: Record<string, ImageTextDetails> =
                      {};
                    Array.from(assetMeasurementsListMap.keys()).forEach(
                      (metric) => {
                        const imageToText =
                          settings.measureIdToImageTextDetails[
                            metric.toString()
                          ];
                        if (imageToText) {
                          const measurement = assetMeasurementsListMap
                            .get(metric)
                            ?.map((measures) => {
                              return measures;
                            });
                          measurement?.forEach((measure) => {
                            measureIdImageText[measure.id.toString()] = {
                              ...imageToText,
                              id: measure.id.toString(),
                              label: measure.tag,
                            };
                          });
                        }
                      }
                    );
                    const dbMeasureIdToName: Record<string, string> = {};
                    assetMeasurementsListMap.forEach((measures) => {
                      measures.forEach((measure) => {
                        dbMeasureIdToName[measure.id] = measure.tag;
                      });
                    });
                    widget.settings.dbMeasureIdToName = dbMeasureIdToName;
                    widget.settings.measureIdToImageTextDetails =
                      measureIdImageText;
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    settings.mode = "dashboard";
                    break;
                  }
                  case "map": {
                    settings.markers = settings.markers.map((marker) => {
                      if (marker.selectedTitles) {
                        const measures = marker.selectedTitles.flatMap(
                          (title) => {
                            const measureList = assetMeasurementsListMap.get(
                              Number(title)
                            );
                            return measureList
                              ? measureList.map((measure) => ({
                                  assetId: dashboardData?.asset?.id.toString(),
                                  measureId: measure.id.toString(),
                                }))
                              : [];
                          }
                        );
                        marker.assetMeasures =
                          measures.length > 0 ? measures : [];
                        marker.labelAndUnits = marker.selectedTitles.reduce(
                          (acc, title) => {
                            const measureList = assetMeasurementsListMap.get(
                              Number(title)
                            );
                            if (measureList) {
                              measureList.forEach((measure) => {
                                acc[measure.id.toString()] = {
                                  label: measure.tag,
                                  unit:
                                    marker.labelAndUnits[measure.id.toString()]
                                      ?.unit || "",
                                  value: "",
                                };
                              });
                            }
                            return acc;
                          },
                          {} as Record<
                            string,
                            { label: string; unit: string; value: string }
                          >
                        );
                        marker.selectedTitles = measures.map(
                          (measure) => measure.measureId
                        );
                      } else {
                        marker.assetMeasures = [];
                        marker.labelAndUnits = {};
                      }
                      return marker;
                    });
                    settings.mode = "dashboard";
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    break;
                  }
                  case "Diagram": {
                    Object.keys(settings.elementIdVariabels).map((ids) => {
                      const variables = settings.elementIdVariabels[ids];
                      return variables.map((vars) => {
                        const title = vars.measurementId;
                        return {
                          measurementId: vars.measurementId, // Returning the measurementId for each variable
                        };
                      });
                    });
                    settings.elementIdVariabels = Object.keys(
                      settings.elementIdVariabels
                    ).reduce(
                      (acc, ids) => {
                        const variables = settings.elementIdVariabels[ids];
                        // Assign measurementId to each variable and push to the accumulator
                        const updatedVariables = variables.map((vars) => {
                          const measures = assetMeasurementsListMap.get(
                            Number(vars.measurementId)
                          );
                          const measurementId = measures
                            ? measures[0].id.toString()
                            : ""; // Take the first measure's id
                          return {
                            ...vars, // Spread the original variable properties
                            assetId: dashboardData?.asset?.id.toString(),
                            measurementId: measurementId, // Add the measurementId to each variable
                          };
                        });

                        // Accumulate the updated variables under the corresponding ids
                        acc[ids] = updatedVariables;
                        return acc;
                      },
                      {} as Record<string, elementVariable[]> // Initialize the accumulator with the correct type
                    );
                    settings.elementVariable = settings.elementVariable.map(
                      (variable) => {
                        const measures = assetMeasurementsListMap.get(
                          Number(variable.measurementId)
                        );
                        const measurementId = measures
                          ? measures[0].id.toString()
                          : "";
                        return {
                          ...variable,
                          assetId: dashboardData?.asset?.id.toString(),
                          measurementId: measurementId,
                        };
                      }
                    );
                    settings.mode = "dashboard";
                    if (settings.dashboardOrTemplate === "template") {
                      settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    break;
                  }
                  case "chart": {
                    switch (settings.chartType) {
                      case "bar":
                      case "scatter": {
                        const measures = settings.settings.selectedTitles
                          .map((title) => {
                            const measurements: number[] = [];
                            if (
                              assetMeasurementsListMap.get(Number(title)) !==
                              undefined
                            ) {
                              const measureIds =
                                assetMeasurementsListMap
                                  .get(Number(title))
                                  ?.map((title) => title.id) ?? [];
                              measurements.push(...measureIds);
                            }
                            return measurements;
                          })
                          .flat();
                        settings.settings.selectedTitles = measures.map(
                          (measure) => measure.toString()
                        );
                        const titles: number[] = [];
                        settings.settings.assetMeasure = [
                          {
                            assetId: dashboardData.asset?.id.toString(),
                            measureId: measures.map((measure) =>
                              measure.toString()
                            ),
                          },
                        ];

                        const updatedDbMeasureIdToName: Record<string, string> =
                          {};
                        const updatedDbMeasureIdToSetting: Record<
                          string,
                          ChartMeasureSetting
                        > = {};
                        const updatedDbMeasureIdToAnnotation: Record<
                          string,
                          boolean
                        > = {};
                        if ("dbMeasureIdToName" in widget.settings.settings) {
                          for (const [oldId, tag] of Object.entries(
                            widget.settings.settings.dbMeasureIdToName
                          )) {
                            const mapKey = Number(oldId);
                            const measurements =
                              assetMeasurementsListMap.get(mapKey);

                            if (measurements && measurements.length > 0) {
                              const newId = measurements[0].id.toString();
                              updatedDbMeasureIdToName[newId] =
                                measurements[0].tag;
                            }
                          }

                          widget.settings.settings.dbMeasureIdToName =
                            updatedDbMeasureIdToName;
                        }
                        if (
                          "dbMeasureIdToSetting" in widget.settings.settings
                        ) {
                          for (const [oldId, setting] of Object.entries(
                            widget.settings.settings.dbMeasureIdToSetting
                          )) {
                            const mapKey = Number(oldId);
                            const measurements =
                              assetMeasurementsListMap.get(mapKey);

                            if (measurements && measurements.length > 0) {
                              const newId = measurements[0].id.toString();
                              updatedDbMeasureIdToSetting[newId] = setting;
                            }
                          }
                          widget.settings.settings.dbMeasureIdToSetting =
                            updatedDbMeasureIdToSetting;
                        }
                        if (
                          "dbMeasureIdToAnnotation" in widget.settings.settings
                        ) {
                          for (const [oldId, annotation] of Object.entries(
                            widget.settings.settings.dbMeasureIdToAnnotation
                          )) {
                            const mapKey = Number(oldId);
                            const measurements =
                              assetMeasurementsListMap.get(mapKey);
                            if (measurements && measurements.length > 0) {
                              const newId = measurements[0].id.toString();
                              updatedDbMeasureIdToAnnotation[newId] =
                                annotation;
                            }
                          }

                          widget.settings.settings.dbMeasureIdToAnnotation =
                            updatedDbMeasureIdToAnnotation;
                        }
                        // if (settings.settings.overrideGlobalBarColor) {
                        settings.settings.barColors =
                          settings.settings.barColors.map((bar) => {
                            const { color, measureId } = bar;
                            const measurement = assetMeasurementsListMap.get(
                              Number(measureId)
                            );
                            const measureToMap: { id: number | null } = {
                              id: null,
                            };
                            if (measurement && measurement.length > 0) {
                              for (const measure of measurement) {
                                if (
                                  measures.includes(measure.id) &&
                                  !titles.includes(measure.id)
                                ) {
                                  titles.push(measure.id);
                                  measureToMap.id = measure.id;
                                }
                              }
                            }
                            return {
                              color,
                              measureId: measureToMap.id?.toString() ?? "", // Use the valid id or fallback to an empty string
                            };
                          });
                        // }
                        if (
                          settings.chartType === "scatter" &&
                          settings.settings.showStacked &&
                          settings.settings.selectedSparkMeasure?.measureId &&
                          settings.settings.selectedSparkMeasure?.measureId !==
                            ""
                        ) {
                          if (
                            settings.settings.showStacked &&
                            settings.settings.selectedSparkMeasure?.measureId &&
                            settings.settings.selectedSparkMeasure
                              ?.measureId !== ""
                          ) {
                            const measures = assetMeasurementsListMap.get(
                              Number(
                                settings.settings.selectedSparkMeasure
                                  ?.measureId
                              )
                            );
                            settings.settings.selectedSparkMeasure.assetId =
                              dashboardData.asset?.id.toString();
                            settings.settings.selectedSparkMeasure.measureId =
                              measures?.[0].id.toString() ?? "";
                          } else {
                            settings.settings.showSparkLine = false;
                            settings.settings.selectedSparkMeasure.measureId =
                              "";
                            settings.settings.selectedSparkMeasure.assetId = "";
                          }
                        }
                        break;
                      }
                      case "bullet":
                      case "indicator":
                      case "heatmap": {
                        const measures =
                          assetMeasurementsListMap.get(
                            Number(settings.settings.selectedDbMeasureId)
                          ) !== undefined &&
                          assetMeasurementsListMap.get(
                            Number(settings.settings.selectedDbMeasureId)
                          );
                        if (measures) {
                          settings.settings.selectedDbMeasureId =
                            measures[0].id.toString();
                          settings.settings.assetMeasure = {
                            assetId: dashboardData.asset?.id.toString(),
                            measureId: measures.map((measure) =>
                              measure.id.toString()
                            ),
                          };
                        } else {
                          settings.settings.selectedDbMeasureId = "";
                          settings.settings.assetMeasure = {
                            assetId: dashboardData.asset?.id.toString(),
                            measureId: [],
                          };
                        }
                        break;
                      }
                      case "sankey": {
                        settings.settings.Label = settings.settings.Label.map(
                          (label) => {
                            if (label.sourceFrom === "Default") {
                              let measureToMap: {
                                id: number | null;
                                tag: string | null;
                              } = {
                                id: null,
                                tag: null,
                              };
                              const measure =
                                assetMeasurementsListMap.get(
                                  Number(label.sourceAssetMeasure?.measureId)
                                ) ??
                                assetMeasurementsListMap.get(
                                  Number(label.sourceName)
                                );

                              if (measure && measure.length > 0) {
                                const selectedMeasure = measure.at(0);
                                measureToMap = {
                                  id: selectedMeasure?.id ?? 0,
                                  tag: selectedMeasure?.tag.toString() ?? "",
                                };
                              }
                              return {
                                ...label,
                                sourceLabel: this.formatMetricLabel(
                                  measureToMap?.tag ?? ""
                                ),
                                sourceName: measureToMap?.id?.toString() ?? "",
                                sourceAssetMeasure: {
                                  ...label.sourceAssetMeasure,
                                  assetId: dashboardData.asset?.id.toString(),
                                  measureId: measureToMap?.id?.toString() ?? "",
                                },
                              };
                            }
                            return label;
                          }
                        );
                        break;
                      }
                    }
                    if (settings.settings.mode) {
                      settings.settings.mode = "dashboard";
                    }
                    if (settings.settings.dashboardOrTemplate === "template") {
                      settings.settings.assetOrAssetType =
                        dashboardData.asset?.id ?? null;
                    }
                    break;
                  }
                }
                return widget;
              })
            );
            dashboardWidgetsAndAllData.widget.widgets = updatedWidgets;
            dashboardWidgetsAndAllData.topPanel =
              dashboardTemplateJSONData.topPanel;
            dashboardWidgetsAndAllData.chart = dashboardTemplateJSONData.chart;
            dashboardWidgetsAndAllData.widget.widgetLayout =
              dashboardTemplateJSONData.widget.widgetLayout;
            dashboardData.data = JSON.stringify(dashboardWidgetsAndAllData);
            const dashboardTOJSON = JSON.parse(JSON.stringify(dashboardData));
            return {
              ...dashboardTOJSON,
              default: allDefaultDashboads ? true : false,
            };
          } catch (e) {
            console.error("Error updating dashboard template widgets:", e);
            this.logger.log(e);
            return {
              ...dashboard,
              default: allDefaultDashboads ? true : false,
            };
          }
        }
      }
    }
    return {
      ...dashboard,
      default: allDefaultDashboads ? true : false,
    };
  }

  async add(
    dashboard: DashboardCreationData,
    userId: number,
    customerId: number
  ) {
    return this.transactionFactory.run(async (em) => {
      const checkDashboardIsExists = await this.repository.findOne({
        title: dashboard.title,
        customer: Reference.createFromPK(Customer, customerId),
      });
      if (checkDashboardIsExists) {
        throw new ConflictException("Dashboard already exists");
      }
      if (dashboard.asset_id && dashboard.dashboard_template_id) {
        const asset = await this.asset.findOne({
          $and: [
            {
              customerId,
            },
            {
              id: dashboard.asset_id,
            },
          ],
        });
        if (!asset) {
          throw new NotFoundException("Asset not found");
        }
        dashboard.asset = asset;
        const dashboardTemplate = await this.dashboardTemplate.findOne({
          id: dashboard.dashboard_template_id,
        });
        if (!dashboardTemplate) {
          if (!asset) {
            throw new NotFoundException("Dashboard template not found");
          }
        }
        dashboard.dashboardTemplate = dashboardTemplate;
        dashboard.dashboard_template_id = undefined;
        dashboard.asset_id = undefined;
      }

      const newDashboard = this.repository.create({
        ...dashboard,
        createdBy: Reference.createFromPK(User, userId),
        customer: Reference.createFromPK(Customer, customerId),
      });
      await this.repository.persistAndFlush(newDashboard);
      if (dashboard.favourite) {
        await this.createFavoriteDashboard(newDashboard.id, userId, customerId);
      }
      delete dashboard.asset;
      delete dashboard.dashboardTemplate;
      return { ...dashboard, id: newDashboard.id };
    });
  }

  async delete(dashboardId: number, userId: number) {
    let d: Dashboard | null = await this.findById(dashboardId);
    if (d) {
      d.deletedAt = new Date();
      d.deletedBy = Reference.createFromPK(User, userId);
      this.repository.getEntityManager().flush();
    } else {
      throw new InvalidInputException(
        `Dashboard with id "${dashboardId}" does not exist`
      );
    }
  }

  async defaultDashboard(
    dashboardId: number,
    userId: number,
    customerId: number,
    status: boolean
  ): Promise<Number> {
    try {
      if (status) {
        const checkDefaultIsExists =
          await this.defaultdashboardrepository.findOne({
            user: Reference.createFromPK(User, userId),
            customer: Reference.createFromPK(Customer, customerId),
          });

        if (
          checkDefaultIsExists &&
          dashboardId === checkDefaultIsExists.dashboard.id
        ) {
          throw new InvalidInputException(
            `Dashboard id ${dashboardId} is already default for this user.`
          );
        }

        const defaultDashboardData = {
          dashboard: Reference.createFromPK(Dashboard, dashboardId),
          user: Reference.createFromPK(User, userId),
          customer: Reference.createFromPK(Customer, customerId),
          createdAt: new Date(),
          createdBy: Reference.createFromPK(User, userId),
        };

        if (checkDefaultIsExists) {
          await this.defaultdashboardrepository.nativeUpdate(
            {
              user: Reference.createFromPK(User, userId),
              customer: Reference.createFromPK(Customer, customerId),
            },
            defaultDashboardData
          );
        } else {
          const defaultDashboard = await this.defaultdashboardrepository.create(
            defaultDashboardData
          );
          await this.defaultdashboardrepository.persistAndFlush(
            defaultDashboard
          );
        }
        return 1;
      } else {
        const dashboard = this.defaultdashboardrepository.nativeDelete({
          dashboard: Reference.createFromPK(Dashboard, dashboardId),
          user: Reference.createFromPK(User, userId),
          customer: Reference.createFromPK(Customer, customerId),
        });
        return dashboard;
      }
    } catch (error: any) {
      throw new InvalidInputException(error.message);
    }
  }

  async findFavoriteDashboard(
    dashboardId: number,
    userId: number,
    customerId: number
  ): Promise<DashboardFavorite> {
    return this.favoritedashboardrepository.findOne({
      dashboard: Reference.createFromPK(Dashboard, dashboardId),
      user: Reference.createFromPK(User, userId),
      customer: Reference.createFromPK(Customer, customerId),
    });
  }

  async createFavoriteDashboard(
    dashboardId: number,
    userId: number,
    customerId: number
  ): Promise<Number> {
    const defaultDashboardData = {
      dashboard: Reference.createFromPK(Dashboard, dashboardId),
      user: Reference.createFromPK(User, userId),
      customer: Reference.createFromPK(Customer, customerId),
      createdAt: new Date(),
      createdBy: Reference.createFromPK(User, userId),
    };
    return this.favoritedashboardrepository.nativeInsert(defaultDashboardData);
  }

  async removeFavoriteDashboard(
    dashboardId: number,
    userId: number,
    customerId: number
  ): Promise<Number> {
    return this.favoritedashboardrepository.nativeDelete({
      dashboard: Reference.createFromPK(Dashboard, dashboardId),
      user: Reference.createFromPK(User, userId),
      customer: Reference.createFromPK(Customer, customerId),
    });
  }
  async findAssetByMeasurement(measurementId: number, customerId: number) {
    const assetMeasure = await this.assetMeasurement.findOne({
      id: measurementId,
    });
    return assetMeasure;
  }
  async migrateDashboard(customerId: number, dashboardId: number) {
    const dashboard = await this.repository.findOne(
      { id: dashboardId },
      { populate: ["data"] }
    );
    const before = JSON.parse(dashboard.data);
    const dashboardData = JSON.parse(dashboard.data); // Parse existing data
    const widgets = dashboardData.widget.widgets as Widget[];

    // Helper function for single measure update
    const updateSingleMeasure = async (
      measureId: number,
      settings: any,
      customerId: number
    ) => {
      // if (!settings.assetMeasure) {
      const measurementData = await this.findAssetByMeasurement(
        measureId,
        customerId
      );
      if (measurementData) {
        const { asset, measurement, id } = measurementData;
        settings.assetMeasure = {
          assetId: asset.id.toString(),
          measureId: [id.toString()],
        };

        // Populate dbMeasureIdToName
        settings.dbMeasureIdToName = {
          ...(settings.dbMeasureIdToName || {}),
          [id.toString()]: measurement.tag, // Add measurement ID and tag
        };
      } else {
        settings.assetMeasure = {
          assetId: "",
          measureId: [],
        };
        settings.dbMeasureIdToName = {};
      }
      // }
    };

    // Helper function for grouped measures update
    const updateGroupedMeasures = async (
      measureIds: number[],
      settings: any,
      customerId: number
    ) => {
      // if (!settings.assetMeasure) {
      const groupedAssetMeasureData = new Map();
      const dbMeasureIdToName: Record<string, string> = {};
      for (const measureId of measureIds) {
        const measurementData = await this.findAssetByMeasurement(
          measureId,
          customerId
        );
        if (measurementData) {
          const { asset, measurement, id } = measurementData;
          if (!groupedAssetMeasureData.has(asset.id)) {
            groupedAssetMeasureData.set(asset?.id, []);
          }
          groupedAssetMeasureData.get(asset.id).push(id.toString() ?? "");
          dbMeasureIdToName[id.toString()] = measurement.tag;
        } else {
          console.log("measurement not found");
        }
      }
      settings.assetMeasure = Array.from(groupedAssetMeasureData.entries()).map(
        ([assetId, measureId]) => ({
          assetId: assetId.toString(),
          measureId,
        })
      );
      settings.dbMeasureIdToName = {
        ...(settings.dbMeasureIdToName || {}),
        ...dbMeasureIdToName,
      };
      // }
    };
    let updatedWidgets = [];
    try {
      // Update widgets
      updatedWidgets = await Promise.all(
        (JSON.parse(JSON.stringify(widgets)) as Widget[]).map(
          async (widget) => {
            const { type, settings } = widget;

            switch (type) {
              case "stats":
              case "kpi-percentage":
              case "kpi-bar-chart":
              case "kpi-sparkline":
              case "kpi-value-indicator":
              case "real-time":
              case "image-stats":
              case "kpi-color-box":
                await updateSingleMeasure(
                  Number(settings.selectedDbMeasureId),
                  settings,
                  customerId
                );
                break;

              case "table":
              case "kpi-table":
                await updateGroupedMeasures(
                  settings.selectedTitles.map((title) => Number(title)),
                  settings,
                  customerId
                );
                break;

              case "chart":
                switch (settings.chartType) {
                  case "heatmap":
                  case "bullet":
                  case "indicator":
                    await updateSingleMeasure(
                      Number(settings.settings.selectedDbMeasureId),
                      settings.settings,
                      customerId
                    );
                    break;
                  case "scatter":
                  case "bar":
                    if ("selectedTitles" in settings.settings) {
                      await updateGroupedMeasures(
                        (
                          settings.settings
                            .selectedTitles as unknown as string[]
                        ).map((title: string) => Number(title)),
                        settings.settings,
                        customerId
                      );
                    }
                    break;
                  default:
                    console.warn("Unknown chart type:", settings.chartType);
                }
                break;
              case "image": {
              }
              default:
                console.warn("Unhandled widget type:", type);
                break;
            }

            return widget;
          }
        )
      );

      // Update only the widgets in the dashboard data
      dashboardData.widget.widgets = updatedWidgets;

      // Save the updated dashboard data to the database
      dashboard.data = JSON.stringify(dashboardData);
      await this.repository.persistAndFlush(dashboard);
    } catch (e) {
      console.log(e);
      return e;
    }
    return {
      status: "Migration and update successful for " + dashboardId,
      before: before.widget.widgets,
      // dashboardData,
      updatedWidgets,
      // dashboard,
    };
  }
  async singleMeasureAsset(
    customerId: number,
    assetId: string,
    measurements: string[]
  ) {
    const asset = await this.assetRepository.findById(Number(assetId), {
      customerId,
    });
    if (!asset) {
      return undefined;
    }
    const assetMeasurements = await this.assetMeasurementService.getAll(
      customerId,
      asset.id
    );
    const assetMeasureIds = assetMeasurements.map(({ id }) => id.toString());
    const updatedMeasurements = measurements.filter((measurement) =>
      assetMeasureIds.includes(measurement)
    );
    return {
      assetId: asset.id.toString(),
      measureId: updatedMeasurements,
    };
  }
  async singleMeasureAssetWithNames(
    customerId: number,
    assetId: string,
    measurements: string[]
  ) {
    const asset = await this.assetRepository.findById(Number(assetId), {
      customerId,
    });
    if (!asset) {
      return undefined;
    }
    const assetMeasurements = await this.assetMeasurementService.getAll(
      customerId,
      asset.id
    );
    const assetMeasureIds = assetMeasurements.map(({ id }) => id.toString());
    const metricsIdsNames = assetMeasurements.reduce(
      (acc: Record<string, string>, measure) => {
        if (measure?.id && measure.tag) {
          if (measure.tag.includes("\\")) {
            const tagParts = measure.tag.split("\\");
            acc[measure.id.toString()] = tagParts[tagParts.length - 1]; // Set last index value
          } else {
            acc[measure.id.toString()] = measure.tag;
          }
        }
        return acc;
      },
      {}
    );

    const updatedMeasurements = measurements.filter((measurement) =>
      assetMeasureIds.includes(measurement)
    );
    return {
      assetId: asset.id.toString(),
      measureId: updatedMeasurements,
      metricsIdsNames,
    };
  }
  async autoSyncDashboard(customerId: number, dashboardId: number) {
    const dashboard = await this.repository.findOne(
      { id: dashboardId, customer: customerId },
      { populate: ["data"] }
    );

    if (!dashboard) {
      throw new Error("Dashboard not found");
    }

    const dashboardData = JSON.parse(dashboard.data); // Parse existing data
    const widgets = dashboardData.widget.widgets as Widget[];

    try {
      const updatedWidgets = await Promise.all(
        widgets.map(async (widget) => {
          const { type, settings } = widget;
          switch (type) {
            case "stats":
            case "kpi-percentage":
            case "kpi-bar-chart":
            case "kpi-sparkline":
            case "kpi-value-indicator":
            case "real-time":
            case "image-stats":
            case "kpi-color-box": {
              if (
                settings.assetMeasure?.assetId &&
                settings.assetMeasure?.measureId
              ) {
                const updatedAssetMeasurements = await this.singleMeasureAsset(
                  customerId,
                  settings.assetMeasure.assetId,
                  settings.assetMeasure.measureId
                );
                if (updatedAssetMeasurements) {
                  settings.assetMeasure = updatedAssetMeasurements;
                  const measureIds: string[] = Array.isArray(
                    settings.assetMeasure.measureId
                  )
                    ? settings.assetMeasure.measureId.flat()
                    : [settings.assetMeasure.measureId];
                  // Filter dbMeasureIdToName based on measureIds
                  settings.dbMeasureIdToName = Object.fromEntries(
                    Object.entries(settings.dbMeasureIdToName || {}).filter(
                      ([key]) => measureIds.includes(key)
                    )
                  );
                }
              }
              break;
            }
            case "alert-widget": {
              settings.assetMeasure = await Promise.all(
                settings.assetMeasure.map(async (assetMeasure) => {
                  if (assetMeasure?.assetId && assetMeasure?.measureId) {
                    return (
                      (await this.singleMeasureAsset(
                        customerId,
                        assetMeasure.assetId,
                        assetMeasure.measureId
                      )) ?? assetMeasure
                    );
                  }
                  return assetMeasure;
                })
              );
              break;
            }
            case "kpi-table":
            case "table": {
              settings.assetMeasure = await Promise.all(
                settings.assetMeasure.map(async (assetMeasure) => {
                  if (assetMeasure?.assetId && assetMeasure?.measureId) {
                    return (
                      (await this.singleMeasureAsset(
                        customerId,
                        assetMeasure.assetId,
                        assetMeasure.measureId
                      )) ?? assetMeasure
                    );
                  }
                  return assetMeasure;
                })
              );
              const measureIds = settings.assetMeasure
                .map((assetMeasure) => assetMeasure.measureId)
                .flat()
                .filter((id) => typeof id === "string" && id.trim() !== "");
              settings.dbMeasureIdToName = Object.fromEntries(
                Object.entries(settings.dbMeasureIdToName).filter(([key]) =>
                  measureIds.includes(key)
                )
              );
              break;
            }
            case "chart": {
              switch (settings.chartType) {
                case "heatmap":
                case "bullet":
                case "indicator": {
                  if (
                    settings.settings?.assetMeasure?.assetId &&
                    settings.settings.assetMeasure?.measureId
                  ) {
                    const updatedAssetMeasurements =
                      await this.singleMeasureAsset(
                        customerId,
                        settings.settings.assetMeasure.assetId,
                        settings.settings.assetMeasure.measureId
                      );
                    if (updatedAssetMeasurements) {
                      settings.settings.assetMeasure = updatedAssetMeasurements;
                      const measureIds =
                        settings.settings.assetMeasure.measureId;
                      settings.settings.dbMeasureIdToName = Object.fromEntries(
                        Object.entries(
                          settings.settings.dbMeasureIdToName
                        ).filter(([key]) => measureIds.includes(key))
                      );
                    }
                  }
                  break;
                }
                case "sankey": {
                  settings.settings.Label = await Promise.all(
                    settings.settings.Label.map(async (label) => {
                      if (label.sourceFrom === "Calculated") return label;
                      if (
                        label.sourceAssetMeasure?.assetId &&
                        label.sourceAssetMeasure?.measureId
                      ) {
                        const updatedAssetMeasurements =
                          await this.singleMeasureAssetWithNames(
                            customerId,
                            label.sourceAssetMeasure.assetId,
                            [label.sourceAssetMeasure?.measureId]
                          );
                        if (updatedAssetMeasurements) {
                          label.sourceAssetMeasure = {
                            assetId: updatedAssetMeasurements.assetId,
                            measureId:
                              updatedAssetMeasurements.measureId.length > 0
                                ? updatedAssetMeasurements.measureId[0]
                                : "",
                          };
                          label.sourceName =
                            updatedAssetMeasurements.measureId.length > 0
                              ? updatedAssetMeasurements.measureId[0]
                              : "";
                          label.sourceLabel =
                            updatedAssetMeasurements.metricsIdsNames[
                              label.sourceName
                            ] ?? "";
                        }
                      }
                      return label;
                    })
                  );
                  break;
                }
                case "bar":
                case "scatter": {
                  settings.settings.assetMeasure = await Promise.all(
                    settings.settings.assetMeasure.map(async (assetMeasure) => {
                      if (assetMeasure?.assetId && assetMeasure?.measureId) {
                        return (
                          (await this.singleMeasureAsset(
                            customerId,
                            assetMeasure.assetId,
                            assetMeasure.measureId
                          )) ?? assetMeasure
                        );
                      }
                      return assetMeasure;
                    })
                  );
                  const measureIds = settings.settings.assetMeasure
                    .map((assetMeasure) => assetMeasure.measureId)
                    .flat()
                    .filter((id) => typeof id === "string" && id.trim() !== "");
                  settings.settings.dbMeasureIdToName = Object.fromEntries(
                    Object.entries(settings.settings.dbMeasureIdToName).filter(
                      ([key]) => measureIds.includes(key)
                    )
                  );
                  settings.settings.dbMeasureIdToSetting = Object.fromEntries(
                    Object.entries(
                      settings.settings.dbMeasureIdToSetting
                    ).filter(([key]) => measureIds.includes(key))
                  );
                  break;
                }
                default:
                  break;
              }
              break;
            }
            case "map": {
              settings.markers = await Promise.all(
                settings.markers.map(async (marker) => {
                  marker.assetMeasures = await Promise.all(
                    marker.assetMeasures.map(async (assetMeasure) => {
                      const updatedAssetMeasure =
                        await this.singleMeasureAssetWithNames(
                          customerId,
                          assetMeasure.assetId,
                          [assetMeasure.measureId]
                        );
                      return updatedAssetMeasure
                        ? {
                            ...assetMeasure,
                            measureId: Array.isArray(
                              updatedAssetMeasure.measureId
                            )
                              ? updatedAssetMeasure.measureId[0] ?? ""
                              : updatedAssetMeasure.measureId,
                          }
                        : assetMeasure;
                    })
                  );
                  marker.labelAndUnits = Object.keys(
                    marker.labelAndUnits || {}
                  ).reduce((filteredLabelAndUnits, key) => {
                    if (
                      marker.assetMeasures.some(
                        (m) => m?.measureId?.toString() === key
                      )
                    ) {
                      filteredLabelAndUnits[key] = {
                        unit: marker.labelAndUnits[key].unit,
                        value: marker.labelAndUnits[key].value,
                        label: marker.labelAndUnits[key].label,
                      };
                    }
                    return filteredLabelAndUnits;
                  }, {} as Record<string, { label: string; unit: string; value: string }>);
                  return marker;
                })
              );
              break;
            }
            case "image": {
              settings.assetMeasure = await Promise.all(
                settings.assetMeasure.map(async (assetMeasure) => {
                  if (assetMeasure?.assetId && assetMeasure?.measureId) {
                    return (
                      (await this.singleMeasureAsset(
                        customerId,
                        assetMeasure.assetId,
                        assetMeasure.measureId
                      )) ?? assetMeasure
                    );
                  }
                  return assetMeasure;
                })
              );
              const measureIds = settings.assetMeasure
                .map((assetMeasure) => assetMeasure.measureId)
                .flat()
                .filter((id) => typeof id === "string" && id.trim() !== "");
              settings.dbMeasureIdToName = Object.fromEntries(
                Object.entries(settings.dbMeasureIdToName).filter(([key]) =>
                  measureIds.includes(key)
                )
              );
              settings.labelAndUnits = Object.fromEntries(
                Object.entries(settings.labelAndUnits).filter(([key]) =>
                  measureIds.includes(key)
                )
              );
              settings.measureIdToImageTextDetails = Object.fromEntries(
                Object.entries(settings.measureIdToImageTextDetails).filter(
                  ([key]) => measureIds.includes(key)
                )
              );
              break;
            }
            default:
              break;
          }

          return widget; // Directly returning modified widget without creating a new object
        })
      );

      // Save the updated dashboard with modified widgets
      dashboard.data = JSON.stringify({
        ...dashboardData,
        widget: {
          ...dashboardData.widget,
          widgets: updatedWidgets,
        },
      });

      await this.repository.persistAndFlush(dashboard);

      return { success: true, updatedWidgets };
    } catch (e) {
      console.error("Error updating dashboard widgets:", e);
      throw e;
    }
  }
}
