import { <PERSON>ti<PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property } from "@mikro-orm/core";
import { Metric } from "src/assets/domain/metric.entity";
import { CalculationMetricInstance } from "./calculation-metric-instance.entity";

@Entity()
export class CalculationMetricInput {
  @PrimaryKey()
  id!: number;

  @Property({ fieldName: "input_label" })
  inputLabel!: string;

  @ManyToOne({
    entity: () => CalculationMetricInstance,
    fieldName: "calculation_metric_instance",
  })
  calculationMetricInstance!: CalculationMetricInstance;

  @ManyToOne({
    entity: () => Metric,
    fieldName: "metric",
    nullable: true,
  })
  metric?: Metric;

  @Property({ nullable: true })
  constantNumber?: string;

  @Property({ length: 30, nullable: true })
  constantString?: string;

  @Property({ length: 50, nullable: true })
  comment?: string;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;
}
