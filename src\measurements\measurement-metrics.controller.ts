import { Body, Controller, Post, UseGuards } from "@nestjs/common";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { MeasurementsBackofficeService } from "./measurements-backoffice.service";

@Controller({
  version: "0",
  path: "customers/:customerId/measurements-metrics",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class MeasurementsMetricsController {
  constructor(
    private readonly measurementBackService: MeasurementsBackofficeService
  ) {}

  @Post()
  @HasCustomerRole(Role.POWER_USER)
  async getMeasurementsMetrics(@Body() measurements: number[]) {
    const items = await this.measurementBackService.getMeasurementsByIds(
      measurements
    );
    return {
      total: items.length,
      items,
    };
  }
}
