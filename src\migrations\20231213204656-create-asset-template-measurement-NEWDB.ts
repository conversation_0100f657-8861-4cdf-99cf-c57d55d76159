import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20231213204656 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset_template_measurement" ("id" serial primary key, "tag" varchar(100) not null, "meter_template" int not null, "m_type" int not null, "data_type" int not null, "location" int null, "value_type" int not null, "default_units" int null, "default_description" varchar(150) null, "default_meter_factor" double precision null);',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "meter_template_fk" foreign key ("meter_template") references "asset_template" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "meas_type_fk" foreign key ("data_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "location_fk" foreign key ("location") references "location" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "value_type_fk" foreign key ("value_type") references "value_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "units_fk" foreign key ("default_units") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "asset_template_measurement" cascade;');
  }
}
