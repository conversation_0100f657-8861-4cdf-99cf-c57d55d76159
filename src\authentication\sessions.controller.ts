import {
  Controller,
  Inject,
  Post,
  Request,
  Res,
  UseGuards,
} from "@nestjs/common";
import { Response } from "express";
import { AuthService } from "./auth.service";
import authConfiguration from "./auth.config";
import { ConfigType } from "@nestjs/config";
import { LocalAuthGuard } from "./infra/local-auth.guard";

@Controller({ version: "0", path: "sessions" })
export class SessionsApiController {
  constructor(
    private readonly authService: AuthService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Post()
  @UseGuards(LocalAuthGuard)
  async create(@Request() req, @Res({ passthrough: true }) res: Response) {
    const { accessToken, csrfToken } = await this.authService.login(req.user);

    const expirationDate = new Date();
    expirationDate.setMinutes(
      expirationDate.getMinutes() + this.authConfig.sessionDurationMinutes
    );

    res.cookie(this.authConfig.accessTokenHeader, accessToken, {
      httpOnly: true,
      expires: expirationDate,
      domain: this.authConfig.cookieDomain,
      secure: true,
      sameSite: "strict",
      path: "/",
    });

    res.cookie(this.authConfig.csrfTokenHeader, csrfToken, {
      httpOnly: false,
      expires: expirationDate,
      domain: this.authConfig.cookieDomain,
      secure: true,
      sameSite: "strict",
      path: "/",
    });

    return {
      access_token: accessToken,
      csrf_token: csrfToken,
    };
  }

  // Inside the SessionsApiController class

  @Post("invalidate")
  async invalidate(@Res({ passthrough: true }) res: Response) {
    // Clear the access token cookie
    res.clearCookie(this.authConfig.accessTokenHeader, {
      path: "/",
      domain: this.authConfig.cookieDomain,
      httpOnly: true,
      secure: true,
      sameSite: "strict",
    });

    // Clear the CSRF token cookie
    res.clearCookie(this.authConfig.csrfTokenHeader, {
      path: "/",
      domain: this.authConfig.cookieDomain,
      httpOnly: true,
      secure: true,
      sameSite: "strict",
    });

    // Optionally, you can add more logic here, like logging the logout event or invalidating the token server-side

    return { message: "Session invalidated successfully" };
  }
}
