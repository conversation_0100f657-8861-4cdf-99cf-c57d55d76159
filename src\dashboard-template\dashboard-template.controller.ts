import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import authConfiguration from "src/authentication/auth.config";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { User } from "src/users/domain/user.entity";
import { DashboardTemplateService } from "./dashboard-template.service";

@Controller({
  path: "dashboard-templates",
  version: "0",
})
@UseGuards(JwtAuthGuard, CsrfGuard)
export class DashboardTemplateController {
  constructor(
    private readonly dashboardTemplateService: DashboardTemplateService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Get()
  findAll(
    @CookieToken() headers: Request["headers"],
    @Query("assetTypeId") assetTypeId?: string
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    console.log(customerId);
    return this.dashboardTemplateService.findAll(assetTypeId, customerId);
  }

  @Post()
  async create(
    @Body()
    dashboardTemplate: {
      asset_template: number;
      title: string;
      data: string;
      is_global?: boolean;
    },
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"]
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.dashboardTemplateService.create(
      dashboardTemplate,
      authUser,
      customerId
    );
  }

  @Patch(":id")
  async update(
    @Param("id") id: number,
    @Body()
    dashboardTemplate: {
      asset_template: number;
      title: string;
      data: string;
      is_global?: boolean;
    },
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"]
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.dashboardTemplateService.update(
      {
        id,
        ...dashboardTemplate,
      },
      authUser,
      customerId
    );
  }

  @Get(":id")
  findOne(@Param("id") id: number, @CookieToken() headers: Request["headers"]) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return this.dashboardTemplateService.findDashboardTemplateById(
      id,
      customerId
    );
  }

  @Delete(":id")
  delete(@Param("id") id: number) {
    return this.dashboardTemplateService.delete(id);
  }

  @Post("/create-from-template/:id")
  async createFromTemplate(
    @Param("id") id: number,
    @Body()
    dashboardTemplate: {
      asset_template: number;
      title: string;
      data: string;
      id: number;
    },
    @AuthUser() authUser: User
  ) {
    return "Not implemented";
  }

  @Post("/sync/:templateId")
  async createFromTemplateForCustomer(
    @Param("templateId") templateId: number,
    @CookieToken() headers: Request["headers"]
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.dashboardTemplateService.asyncDashboardTemplate(
      templateId,
      customerId
    );
  }
}
