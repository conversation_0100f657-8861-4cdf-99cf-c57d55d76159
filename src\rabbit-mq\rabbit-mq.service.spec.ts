import { Test, TestingModule } from "@nestjs/testing";
import { RabbitMqService } from "./rabbit-mq.service";
import { ConfigService } from "@nestjs/config";
import { AlertService } from "src/alert/alert.service";
import * as amqp from "amqplib";
import { NotificationDTO } from "src/notification/notification.dto";

jest.mock("amqplib");

describe("RabbitMqService", () => {
  let service: RabbitMqService;
  let mockChannel: any;
  let mockConnection: any;

  const mockConfig = {
    brokers: "localhost",
    topicAlert: "alert-topic",
    clientId: "client-id",
    topicNotification: "notification-topic",
    kafkaGroupId: "group-id",
    kafkaGroupIdInstance: "instance-id",
    frontendUrl: "https://example.com",
    username: "guest",
    password: "guest",
    alertQueueName: "test-queue",
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue(mockConfig),
  };

  const mockAlertService = {
    getAlertNew: jest.fn().mockResolvedValue({
      customerId: 123,
      thresholdValue: 70,
      resetDeadband: 5,
      thresholdType: { threshold: "STALE" },
      asset: { tag: "Asset123" },
      measurement: { tag: "Measurement456" },
      alertUsers: [],
    }),
    getAlertUsers: jest.fn().mockResolvedValue({
      id: 1,
      phone_no: "1234567890",
      email: "<EMAIL>",
      country_code: "+91",
    }),
  };

  beforeEach(async () => {
    mockChannel = {
      assertExchange: jest.fn(),
      assertQueue: jest.fn(),
      bindQueue: jest.fn(),
      consume: jest.fn(),
      publish: jest.fn(),
      ack: jest.fn(),
    };

    mockConnection = {
      createChannel: jest.fn().mockResolvedValue(mockChannel),
      close: jest.fn(),
    };

    (amqp.connect as jest.Mock).mockResolvedValue(mockConnection);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RabbitMqService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: AlertService, useValue: mockAlertService },
      ],
    }).compile();

    service = module.get<RabbitMqService>(RabbitMqService);
    await service.onModuleInit();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should publish message with correct routing key and payload", async () => {
    const testPayload = { test: "data" };
    await service.sendMessage("alert.test", testPayload);

    expect(mockChannel.publish).toHaveBeenCalledWith(
      "notifications_topic_exchange",
      "alert.test",
      Buffer.from(JSON.stringify(testPayload))
    );
  });

  it("should generate correct HTML email template for STALE alert", async () => {
    const alert = await mockAlertService.getAlertNew();
    const result = await service["htmlEmailTemplate"](
      alert,
      42.5,
      "STALE",
      *************,
      ">",
      1001,
      "STALE"
    );

    expect(result).toContain("Stale Alert Raised");
    expect(result).toContain("Asset123");
    expect(result).toContain("Measurement456");
    expect(result).toContain("No fresh data received for 70 minutes");
    expect(result).toContain("Visit Dashboard");
  });

  it("should generate correct HTML email template for DEAD alert", async () => {
    const alert = {
      ...(await mockAlertService.getAlertNew()),
      thresholdType: { threshold: "DEAD" },
    };
    const result = await service["htmlEmailTemplate"](
      alert,
      null,
      "DEAD",
      *************,
      "==",
      2002,
      "DEAD"
    );

    expect(result).toContain("Dead Measurement Alert Raised");
    expect(result).toContain("No data received for 70 minutes");
  });

  it("should generate correct SMS template for STALE alert", async () => {
    const alert = await mockAlertService.getAlertNew();
    const result = await service["smsTemplate"](
      alert,
      42.5,
      "STALE",
      *************,
      ">",
      1001,
      "STALE"
    );

    expect(result).toContain("Stale Alert Raised");
    expect(result).toContain("Asset: Asset123");
    expect(result).toContain("Measurement: Measurement456");
    expect(result).toContain("No fresh data received for 70 minutes");
    expect(result).toContain(
      "Link : https://example.com/customer/123/dashboard/0?event_id=1001"
    );
  });

  it("should generate correct SMS template for DEAD alert", async () => {
    const alert = {
      ...(await mockAlertService.getAlertNew()),
      thresholdType: { threshold: "DEAD" },
    };
    const result = await service["smsTemplate"](
      alert,
      null,
      "DEAD",
      *************,
      "==",
      2002,
      "DEAD"
    );

    expect(result).toContain("Dead Measurement Alert Raised");
    expect(result).toContain("No data received for 70 minutes");
  });
  it("should generate correct HTML email template for EXCEEDED alert without threshold type", async () => {
    const alert = {
      ...(await mockAlertService.getAlertNew()),
      thresholdType: undefined,
    };
    const result = await service["htmlEmailTemplate"](
      alert,
      99,
      "EXCEEDED",
      *************,
      ">",
      5678,
      undefined
    );

    expect(result).toContain("Limit Alert");
    expect(result).toContain("Asset123");
    expect(result).toContain("Measurement456");
    expect(result).toContain("> 70");
    expect(result).toContain("<p><strong>Return to Normal:</strong> > 5</p>");
  });
  it("should generate correct SMS template for RETURNED alert without threshold type", async () => {
    const alert = {
      ...(await mockAlertService.getAlertNew()),
      thresholdType: undefined,
    };
    const result = await service["smsTemplate"](
      alert,
      88,
      "RETURNED",
      *************,
      "<",
      9999,
      undefined
    );

    expect(result).toContain("Returned to Normal");
    expect(result).toContain("Asset: Asset123");
    expect(result).toContain("Measurement: Measurement456");
    expect(result).toContain("Threshold: < 70");
    expect(result).toContain("Return to Normal: < 5");
    expect(result).toContain(
      "Link : https://example.com/customer/123/dashboard/0?event_id=9999"
    );
  });
});
