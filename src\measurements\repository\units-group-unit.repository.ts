import { EntityRepository, FilterQuery, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import {
  MeasurementType,
  MeasurementTypeId,
} from "../../measurements/domain/measurement-type.entity";
import { UnitOfMeasure } from "../domain/unit-of-measure.entity";
import { UnitsGroupUnit } from "../domain/units-group-unit.entity";
import { UnitsGroup, UnitsGroupId } from "../domain/units-group.entity";

@Injectable()
export class UnitsGroupUnitRepository {
  constructor(
    @InjectRepository(UnitsGroupUnit)
    private readonly entityRepository: EntityRepository<UnitsGroupUnit>,
    @InjectRepository(UnitOfMeasure)
    private readonly unitOfMeasureRepository: EntityRepository<UnitOfMeasure>,
    @InjectRepository(UnitsGroup)
    private readonly unitsGroupRepository: EntityRepository<UnitsGroup>,
    @InjectRepository(MeasurementType)
    private readonly measurementTypeRepository: EntityRepository<MeasurementType>
  ) {}

  async findMeasurementTypeDefaultUnit(
    unitsGroupId: UnitsGroupId,
    measurementTypeId: MeasurementTypeId
  ): Promise<UnitsGroupUnit | null> {
    return await this.entityRepository.findOne({
      $or: [
        {
          unitsGroup: { id: unitsGroupId },
        },
        {
          unitsGroup: { isBase: true },
        },
      ],
      unitOfMeasure: {
        measurementType: { id: measurementTypeId },
      },
      // unitsGroupDefaultUnit: { id: unitsGroupId },
    });
  }

  async findUnitByIdAndMeasurementType(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId
  ): Promise<UnitsGroupUnit | null> {
    return await this.entityRepository.findOne({
      $or: [
        {
          unitsGroup: { id: unitsGroupId },
        },
        {
          unitsGroup: { isBase: true },
        },
      ],
      unitOfMeasure: {
        id: unitOfMeasureId,
        measurementType: { id: measurementTypeId },
      },
    });
  }

  async getAllByGroupId(
    unitsGroupId: UnitsGroupId,
    filters: { measurementTypeId?: MeasurementTypeId }
  ) {
    const whereFilter: FilterQuery<UnitsGroupUnit> = {
      $or: [
        {
          unitsGroup: { id: unitsGroupId },
        },
        {
          unitsGroup: { isBase: true },
        },
      ],
    };

    if (filters.measurementTypeId) {
      whereFilter.unitOfMeasure = {
        measurementType: { id: filters.measurementTypeId },
      };
    }

    return await this.entityRepository.find(whereFilter);
  }

  async getUnitGroupsUnits() {
    return await this.entityRepository.findAll({
      populate: [
        "unitsGroup",
        "unitOfMeasure",
        "unitOfMeasure.measurementType",
      ],
    });
  }

  async createUnitGroupUnit(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId,
    is_default: boolean
  ) {
    const findExistingUnit = await this.entityRepository.findOne({
      unitOfMeasure: {
        id: unitOfMeasureId,
        measurementType: { id: measurementTypeId },
      },
      unitsGroup: { id: unitsGroupId },
    });
    if (findExistingUnit) {
      throw new Error("Unit of group already exists");
    }
    const unitsGroupUnit = new UnitsGroupUnit();
    const unitOfMeasureExists = await this.unitOfMeasureRepository.findOne({
      id: unitOfMeasureId,
    });
    if (!unitOfMeasureExists) {
      throw new Error("Unit of measure not found");
    }
    const unitsGroupExists = await this.unitsGroupRepository.findOne({
      id: unitsGroupId,
    });
    if (!unitsGroupExists) {
      throw new Error("Units group not found");
    }
    const measurementTypeExists = await this.measurementTypeRepository.findOne({
      id: measurementTypeId,
    });
    if (!measurementTypeExists) {
      throw new Error("Measurement type not found");
    }
    unitsGroupUnit.unitsGroup = Reference.create(unitsGroupExists);
    unitsGroupUnit.unitOfMeasure = Reference.create(unitOfMeasureExists);
    unitsGroupUnit.is_default = is_default;
    return await this.entityRepository.persistAndFlush(unitsGroupUnit);
  }

  async updateUnitGroupUnit(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId,
    unitsofUnitId: number,
    is_default: boolean
  ) {
    // Check for duplicate (excluding the current unit)
    const existingCombination = await this.entityRepository.findOne({
      unitsGroup: { id: unitsGroupId },
      unitOfMeasure: {
        id: unitOfMeasureId,
        measurementType: { id: measurementTypeId },
      },
      id: { $ne: unitsofUnitId },
    });

    if (existingCombination) {
      throw new Error("Unit of group already exists");
    }
    const unitsGroupUnit = await this.entityRepository.findOne({
      id: unitsofUnitId,
    });
    if (!unitsGroupUnit) {
      throw new Error("Unit not found");
    }
    const [unitOfMeasure, unitsGroup, measurementType] = await Promise.all([
      this.unitOfMeasureRepository.findOne({ id: unitOfMeasureId }),
      this.unitsGroupRepository.findOne({ id: unitsGroupId }),
      this.measurementTypeRepository.findOne({ id: measurementTypeId }),
    ]);
    if (!unitOfMeasure) throw new Error("Unit of measure not found");
    if (!unitsGroup) throw new Error("Units group not found");
    if (!measurementType) throw new Error("Measurement type not found");

    // Unset default for other units in same unitsGroup and measurementType
    const otherUnits = await this.entityRepository.find({
      unitsGroup: { id: unitsGroupId },
      unitOfMeasure: {
        measurementType: { id: measurementTypeId },
      },
      id: { $ne: unitsofUnitId },
    });

    otherUnits.forEach((unit) => {
      unit.is_default = false;
    });

    // Update the target unit
    unitsGroupUnit.unitsGroup = Reference.create(unitsGroup);
    unitsGroupUnit.unitOfMeasure = Reference.create(unitOfMeasure);
    if (!unitsGroupUnit.is_default) {
      unitsGroupUnit.is_default = !unitsGroupUnit.is_default;
    }

    await this.entityRepository.persistAndFlush([
      ...otherUnits,
      unitsGroupUnit,
    ]);
  }

  async createUnitOfMeasure(
    name: string,
    measurementTypeId: MeasurementTypeId
  ) {
    const measurementType = await this.measurementTypeRepository.findOneOrFail({
      id: measurementTypeId,
    });
    const unitOfMeasureExists = await this.unitOfMeasureRepository.findOne({
      name,
      measurementType: { id: measurementTypeId },
    });
    if (unitOfMeasureExists) {
      throw new Error("Unit of measure already exists");
    }
    const unitOfMeasure = new UnitOfMeasure();
    unitOfMeasure.name = name;
    unitOfMeasure.measurementType = Reference.create(measurementType);
    return await this.unitOfMeasureRepository.persistAndFlush(unitOfMeasure);
  }

  async updateUnitOfMeasure(
    name: string,
    measurementTypeId: MeasurementTypeId,
    unitOfMeasureId: number
  ) {
    const findExistingUnit = await this.unitOfMeasureRepository.findOne({
      name,
      measurementType: { id: measurementTypeId },
      id: { $ne: unitOfMeasureId },
    });
    if (findExistingUnit) {
      throw new Error("Unit of measure already exists");
    }
    const unitOfMeasure = await this.unitOfMeasureRepository.findOne({
      id: unitOfMeasureId,
    });
    if (!unitOfMeasure) {
      throw new Error("Unit of measure not found");
    }
    const measurementType = await this.measurementTypeRepository.findOne({
      id: measurementTypeId,
    });
    if (!measurementType) {
      throw new Error("Measurement type not found");
    }
    unitOfMeasure.name = name;
    unitOfMeasure.measurementType = Reference.create(measurementType);
    return await this.unitOfMeasureRepository.persistAndFlush(unitOfMeasure);
  }

  async setDefaultUniGroupUnit({
    unitsGroupId,
    unitOfMeasureId,
    measurementTypeId,
    unitsofUnitId,
  }: {
    unitsGroupId: UnitsGroupId;
    unitOfMeasureId: number;
    measurementTypeId: MeasurementTypeId;
    unitsofUnitId: string;
  }) {
    const unitsGroupUnit = await this.entityRepository.find(
      {
        $and: [
          { unitsGroup: { id: unitsGroupId } },
          {
            unitOfMeasure: {
              measurementType: { id: measurementTypeId },
            },
          },
        ],
      },
      {
        populate: [
          "unitOfMeasure",
          "unitsGroup",
          "unitOfMeasure.measurementType",
        ],
      }
    );
    if (unitsGroupUnit.length === 0) {
      throw new Error("Unit not found");
    }
    await this.entityRepository.persistAndFlush(
      unitsGroupUnit.map((unit) => {
        unit.is_default = unit.id.toString() === unitsofUnitId ? true : false;
        return unit;
      })
    );

    return unitsGroupUnit;
  }
}
