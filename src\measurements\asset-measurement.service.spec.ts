import { Test } from '@nestjs/testing';
import { AssetMeasurementService } from './asset-measurement.service';
import { MeasurementsBackofficeService } from './measurements-backoffice.service';
import { CustomerService } from 'src/customers/customer.service';
import { AssetService } from 'src/assets/asset.service';
import { TimeSeriesService } from './time-series.service';
import { UnitOfMeasureService } from './unit-of-measure.service';
import { AssetMeasurementCreationData } from './domain/types';
import { AssetMeasurementRepository } from './repository/asset-measurement.repository';
import {
  assetMeasurementFactory,
  datasourceFactory,
  measurementBackofficeFactory,
} from './__tests__/factories';
import { DatasourceService } from './datasource.service';
import { assetFactory, metricFactory } from 'src/assets/__tests__/factories';
import { AssetMeasurement } from './domain/asset-measurement.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { Asset } from 'src/assets/domain/asset.entity';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { Measurement } from './domain/measurement.entity';
import { Location } from './domain/location.entity';
import { MeasurementType } from './domain/measurement-type.entity';
import { UnitOfMeasure } from './domain/unit-of-measure.entity';
import { DataType } from './domain/data-type.entity';
import { ValueType } from './domain/value-type.entity';
import { Datasource } from './domain/datasource.entity';
import { Metric } from '../assets/domain/metric.entity';
import { MetricService } from 'src/assets/metric.service';

const customerId = 7;
const assetId = 42;
const userId = 77;
const existingAssetMeasurementId = 99;

const assetMeasurement: AssetMeasurementCreationData = {
  tag: 'Fan/Speed',
  typeId: 4,
  dataTypeId: 3,
  unitOfMeasureId: 4,
  valueTypeId: 1,
  description: 'RPMs of given fan',
  assetId,
  locationId: 777,
  meterFactor: 3.141,
  datasourceId: 6,
};

const currentType = measurementBackofficeFactory.createCurrentMeasurementType();
const currentUnit = measurementBackofficeFactory.createCurrentUnitOfMeasure();
const frontLocation = measurementBackofficeFactory.createFrontLocation();
const calculatedValue =
  measurementBackofficeFactory.createCalculatedValueType();

describe('Asset Measurement service', () => {
  describe('create', () => {
    test('given a valid measurement it should persist correctly', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();

      await measurementService.create(assetMeasurement, customerId, userId);

      const assetMeasurementCreationData = assetMeasurementRepositoryMock.add
        .mock.calls[0][0] as AssetMeasurementCreationData;
      expect(assetMeasurementCreationData.tag).toBe('Fan/Speed');
      expect(assetMeasurementCreationData.typeId).toBe(4);
      expect(assetMeasurementCreationData.dataTypeId).toBe(3);
      expect(assetMeasurementCreationData.unitOfMeasureId).toBe(4);
      expect(assetMeasurementCreationData.valueTypeId).toBe(1);
      expect(assetMeasurementCreationData.description).toBe(
        'RPMs of given fan',
      );
      expect(assetMeasurementCreationData.meterFactor).toBe(3.141);
      expect(assetMeasurementCreationData.assetId).toBe(assetId);
      expect(assetMeasurementCreationData.locationId).toBe(777);
      expect(assetMeasurementCreationData.datasourceId).toBe(6);
    });

    test('given a non existing metric id an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            metricId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Metric does not exist');
    });

    test('given a metric id belonging to a different asset type an exception should be thrown', async () => {
      const { measurementService, engineMetricId } =
        await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            metricId: engineMetricId,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Metric does not belong to asset type');
    });

    test('given a metric id already present in asset an exception should be thrown', async () => {
      const { measurementService, existingAssetPumpMetricId } =
        await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            metricId: existingAssetPumpMetricId,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Metric already present for given asset');
    });

    test('given a non existing measurement type id an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            typeId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Measurement type does not exist');
    });

    test('given a non existing data type id an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            dataTypeId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Data type does not exist');
    });

    test('given a non existing unit of measure id an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            unitOfMeasureId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Unit of measure does not exist');
    });

    test('given a non existing value type an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            valueTypeId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Value type does not exist');
    });

    test('given a non existing asset an exception should be thrown', async () => {
      const { measurementService, assetServiceMock } =
        await createMeasurementService();
      assetServiceMock.findById = async (id) =>
        id === 404 ? null : assetFactory.createWaterPump(id);

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            assetId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Asset does not exist');
    });

    test('given a non existing customer an exception should be thrown', async () => {
      const { measurementService, customerServiceMock } =
        await createMeasurementService();
      customerServiceMock.findById = (id) => (id === 404 ? null : id);

      await expect(
        measurementService.create(assetMeasurement, 404, userId),
      ).rejects.toThrowError('Customer does not exist');
    });

    test('given a non existing location an exception should be thrown', async () => {
      const { measurementService: measurementService } =
        await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            locationId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Location does not exist');
    });

    test('given an asset not belonging to given customer an exception should be thrown', async () => {
      const { measurementService, assetServiceMock } =
        await createMeasurementService();
      assetServiceMock.findById = async (id, custId) =>
        id === 404 || custId == customerId
          ? null
          : assetFactory.createWaterPump(id);

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Asset does not exist');
    });

    test('given a non existing datasource an exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.create(
          {
            ...assetMeasurement,
            datasourceId: 404,
          },
          customerId,
          userId,
        ),
      ).rejects.toThrowError('Datasource does not exist');
    });
  });

  describe('getAll', () => {
    test('given a valid customer and asset id, it should call repository query for all related measurements', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();
      const findCalls =
        assetMeasurementRepositoryMock.getAllByAssetId.mock.calls;

      await measurementService.getAll(7, assetId);

      expect(findCalls[0][0]).toBe(assetId);
    });

    test('given a non existing customer, an exception should be thrown', async () => {
      const { measurementService, customerServiceMock } =
        await createMeasurementService();
      customerServiceMock.findById = (id) => (id === 404 ? null : id);

      await expect(
        measurementService.getAll(404, assetId),
      ).rejects.toThrowError('Customer does not exist');
    });

    test('given a non existing asset, an exception should be thrown', async () => {
      const { measurementService, assetServiceMock } =
        await createMeasurementService();
      assetServiceMock.findById = async (id) =>
        id === 404 ? null : assetFactory.createWaterPump(id, customerId);

      await expect(measurementService.getAll(7, 404)).rejects.toThrowError(
        'Asset does not exist',
      );
    });

    test('given an asset not belonging to given customer an exception should be thrown', async () => {
      const { measurementService, assetServiceMock } =
        await createMeasurementService();
      assetServiceMock.findById = async (id, customerId) =>
        id === 404 || customerId === 7
          ? null
          : assetFactory.createWaterPump(id, 404);

      await expect(measurementService.getAll(7, 47)).rejects.toThrowError(
        'Asset does not exist',
      );
    });
  });

  describe('getById', () => {
    test('given valid customer, asset and measurement id then repository should be called accordingly', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();
      const findOneCalls = assetMeasurementRepositoryMock.findById.mock.calls;

      await measurementService.getById(7, assetId, 9);

      expect(findOneCalls[0][0]).toBe(9);
      expect(findOneCalls[0][1]).toStrictEqual({ assetId, customerId });
    });
  });

  describe('removeById', () => {
    test('given valid ids, existing asset measurement should be removed', async () => {
      const {
        measurementService,
        assetMeasurementRepositoryMock,
        pumpVoltageMeasurement,
      } = await createMeasurementService();
      const removeCalls = assetMeasurementRepositoryMock.remove.mock.calls;

      await measurementService.removeById(
        customerId,
        assetId,
        existingAssetMeasurementId,
        userId,
      );

      expect(removeCalls[0][0]).toBe(pumpVoltageMeasurement);
    });

    test('given a non matching id, not found exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.removeById(customerId, assetId, 404, userId),
      ).rejects.toThrowError('Asset measurement not found');
    });
  });

  describe('handleAssetUpdate', () => {
    test('non existing asset should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(measurementService.handleAssetUpdate(404)).rejects.toThrow(
        'Asset does not exist',
      );
    });

    test('given existing asset timeseries update should be called', async () => {
      const {
        measurementService,
        timeSeriesServiceMock,
        pumpVoltageMeasurement,
      } = await createMeasurementService();

      await measurementService.handleAssetUpdate(assetId);

      expect(timeSeriesServiceMock.update.mock.calls.length).toBe(1);
      const timeSeriesUpdateCall = timeSeriesServiceMock.update.mock.calls[0];
      expect(timeSeriesUpdateCall[0].id).toBe(pumpVoltageMeasurement.id);
    });
  });

  describe('update', () => {
    test('non existing asset should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          404,
          existingAssetMeasurementId,
          {},
          userId,
        ),
      ).rejects.toThrow('Asset does not exist');
    });

    test('given a non matching id, not found exception should be thrown', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(customerId, assetId, 404, {}, userId),
      ).rejects.toThrowError('Asset measurement not found');
    });

    test('multiple properties to existing asset should change them', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();

      await measurementService.update(
        customerId,
        assetId,
        existingAssetMeasurementId,
        {
          description: 'new description',
          tag: 'Water-Pump',
          meterFactor: 2.0,
        },
        userId,
      );

      const updatedAssetMeasurement = assetMeasurementRepositoryMock.update.mock
        .calls[0][0] as AssetMeasurement;
      expect(updatedAssetMeasurement.description).toBe('new description');
      expect(updatedAssetMeasurement.tag).toBe('Water-Pump');
      expect(updatedAssetMeasurement.meterFactor).toBe(2.0);
    });

    test('non existing location should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            locationId: 404,
          },
          userId,
        ),
      ).rejects.toThrow('Location does not exist');
    });

    test('non existing type should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            typeId: 404,
          },
          userId,
        ),
      ).rejects.toThrow('Measurement type does not exist');
    });

    test('non existing unit of measure should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            unitOfMeasureId: 404,
          },
          userId,
        ),
      ).rejects.toThrow('Unit of measure does not exist');
    });

    test('non existing value type should throw an exception', async () => {
      const { measurementService } = await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            valueTypeId: 404,
          },
          userId,
        ),
      ).rejects.toThrow('Value type does not exist');
    });

    test('multiple types should change them', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();

      await measurementService.update(
        customerId,
        assetId,
        existingAssetMeasurementId,
        {
          typeId: currentType.id,
          unitOfMeasureId: currentUnit.id,
          valueTypeId: calculatedValue.id,
          locationId: frontLocation.id,
        },
        userId,
      );

      const updatedAssetMeasurement = assetMeasurementRepositoryMock.update.mock
        .calls[0][0] as AssetMeasurement;
      expect(updatedAssetMeasurement.measurement.measurementType).toBe(
        currentType,
      );
      expect(updatedAssetMeasurement.measurement.unitOfMeasure).toBe(
        currentUnit,
      );
      expect(updatedAssetMeasurement.measurement.valueType).toBe(
        calculatedValue,
      );
      expect(updatedAssetMeasurement.location).toBe(frontLocation);
    });

    test('empty update should not change anything', async () => {
      const { measurementService, assetMeasurementRepositoryMock } =
        await createMeasurementService();
      const originalPumpVoltageMeasurement =
        assetMeasurementFactory.createPumpVoltage(assetId);

      await measurementService.update(
        customerId,
        assetId,
        existingAssetMeasurementId,
        {},
        userId,
      );

      const updatedAssetMeasurement = assetMeasurementRepositoryMock.update.mock
        .calls[0][0] as AssetMeasurement;
      expect(updatedAssetMeasurement.measurement.measurementType.name).toBe(
        originalPumpVoltageMeasurement.measurement.measurementType.name,
      );
      expect(updatedAssetMeasurement.measurement.unitOfMeasure?.name).toBe(
        originalPumpVoltageMeasurement.measurement.unitOfMeasure?.name,
      );
      expect(updatedAssetMeasurement.measurement.valueType.name).toBe(
        originalPumpVoltageMeasurement.measurement.valueType.name,
      );
    });

    test('valid property should call time series service update', async () => {
      const {
        measurementService,
        assetMeasurementRepositoryMock,
        timeSeriesServiceMock,
      } = await createMeasurementService();

      await measurementService.update(
        customerId,
        assetId,
        existingAssetMeasurementId,
        {
          description: 'new description',
        },
        userId,
      );

      const updatedAssetMeasurement = assetMeasurementRepositoryMock.update.mock
        .calls[0][0] as AssetMeasurement;
      expect(timeSeriesServiceMock.update.mock.calls.length).toBe(1);
      expect(timeSeriesServiceMock.update.mock.calls[0][0]).toBe(
        updatedAssetMeasurement,
      );
    });

    test('non existing metric id should throw an exception', async () => {
      const { measurementService, nonExistingMetricId } =
        await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            metricId: nonExistingMetricId,
          },
          userId,
        ),
      ).rejects.toThrow('Metric does not exist');
    });

    test('metric id belonging to different asset type should throw an exception', async () => {
      const { measurementService, engineMetricId } =
        await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            metricId: engineMetricId,
          },
          userId,
        ),
      ).rejects.toThrowError('Metric does not belong to asset type');
    });

    test('metric id already present in asset should throw an exception', async () => {
      const { measurementService, existingAssetPumpMetricId } =
        await createMeasurementService();

      await expect(
        measurementService.update(
          customerId,
          assetId,
          existingAssetMeasurementId,
          {
            metricId: existingAssetPumpMetricId,
          },
          userId,
        ),
      ).rejects.toThrowError('Metric already present for given asset');
    });
  });
});

const createMeasurementService = async () => {
  const assetMeasurementRepositoryMock: jest.Mocked<
    Pick<
      AssetMeasurementRepository,
      | 'add'
      | 'getAllByAssetId'
      | 'findById'
      | 'remove'
      | 'update'
      | 'countByAssetId'
    >
  > = {
    countByAssetId: jest.fn(async (_, filter) =>
      filter?.metricId === existingAssetPumpMetricId ? 1 : 0,
    ),
    add: jest.fn(),
    getAllByAssetId: jest
      .fn()
      .mockImplementation((id) =>
        id === assetId ? [pumpVoltageMeasurement] : [],
      ),
    findById: jest.fn(
      async (
        id: number,
        filter?: { assetId?: number; customerId?: number },
      ) => {
        if (
          id === 99 &&
          filter &&
          filter.assetId &&
          filter.customerId &&
          filter.assetId === assetId &&
          filter.customerId === customerId
        ) {
          return pumpVoltageMeasurement;
        }
        return null;
      },
    ),
    remove: jest.fn(),
    update: jest.fn(),
  };

  const measurementsBackofficeServiceMock: Pick<
    MeasurementsBackofficeService,
    | 'getMeasurementTypeById'
    | 'getDataTypeById'
    | 'getValueTypeById'
    | 'getLocationById'
  > = {
    getLocationById: jest.fn(async (id) => {
      if (id === frontLocation.id) {
        return frontLocation;
      }
      return null;
    }),
    getMeasurementTypeById: jest.fn(async (id) => {
      const voltType =
        measurementBackofficeFactory.createVoltageMeasurementType();
      if (id === voltType.id) {
        return voltType;
      } else if (id === currentType.id) {
        return currentType;
      }
      return null;
    }),
    getDataTypeById: jest.fn(async (id) => {
      const realDataType = measurementBackofficeFactory.createRealDataType();
      if (id === realDataType.id) {
        return realDataType;
      }
      return null;
    }),
    getValueTypeById: jest.fn(async (id) => {
      const nominalValueType =
        measurementBackofficeFactory.createNominalValueType();
      if (id === nominalValueType.id) {
        return nominalValueType;
      } else if (id === calculatedValue.id) {
        return calculatedValue;
      }
      return null;
    }),
  };

  const datasourceServiceMock: Pick<DatasourceService, 'getById'> = {
    getById: jest.fn(async (id) => {
      const nasaDatasource = datasourceFactory.createNasaDatasource();
      if (id === nasaDatasource.id) {
        return nasaDatasource;
      }
      return null;
    }),
  };

  const customerServiceMock = {
    findById: (id) => id,
  };

  const assetServiceMock: Pick<AssetService, 'findById'> = {
    findById: async (id) =>
      id === assetId
        ? assetFactory.createWaterPump(assetId, customerId, pumpAssetTypeId)
        : null,
  };

  const pumpAssetTypeId = 4;

  const nonExistingMetricId = 404;
  const existingAssetPumpMetricId = 57;
  const pumpMetricId = 55;
  const engineMetricId = 65;
  const metricServiceMock: Pick<MetricService, 'findById'> = {
    findById: async (id) => {
      if (id === pumpMetricId) {
        return metricFactory.createPumpPressureMetric(
          pumpMetricId,
          pumpAssetTypeId,
        );
      } else if (id === existingAssetPumpMetricId) {
        return metricFactory.createPumpPressureMetric(
          existingAssetPumpMetricId,
          pumpAssetTypeId,
        );
      } else if (id === engineMetricId) {
        return metricFactory.createEngineRpmMetric(engineMetricId, 5);
      } else {
        return null;
      }
    },
  };

  const timeSeriesServiceMock: jest.Mocked<
    Pick<TimeSeriesService, 'create' | 'update'>
  > = {
    create: jest.fn(),
    update: jest.fn(),
  };

  const unitOfMeasureServiceMock: Pick<UnitOfMeasureService, 'getById'> = {
    getById: jest.fn(async (id) => {
      const voltUnit = measurementBackofficeFactory.createVoltsUnitOfMeasure();
      if (id === voltUnit.id) {
        return voltUnit;
      } else if (id === currentUnit.id) {
        return currentUnit;
      }
      return null;
    }),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [
      createMikroOrmTestModule([
        Asset,
        AssetType,
        AssetMeasurement,
        Customer,
        Measurement,
        MeasurementType,
        Location,
        UnitOfMeasure,
        DataType,
        ValueType,
        Datasource,
        Metric,
      ]),
    ],
    providers: [
      {
        provide: AssetMeasurementRepository,
        useValue: assetMeasurementRepositoryMock,
      },
      {
        provide: MeasurementsBackofficeService,
        useValue: measurementsBackofficeServiceMock,
      },
      {
        provide: DatasourceService,
        useValue: datasourceServiceMock,
      },
      {
        provide: UnitOfMeasureService,
        useValue: unitOfMeasureServiceMock,
      },
      {
        provide: CustomerService,
        useValue: customerServiceMock,
      },
      {
        provide: AssetService,
        useValue: assetServiceMock,
      },
      {
        provide: MetricService,
        useValue: metricServiceMock,
      },
      {
        provide: TimeSeriesService,
        useValue: timeSeriesServiceMock,
      },
      AssetMeasurementService,
    ],
  }).compile();

  const measurementService = moduleRef.get(AssetMeasurementService);

  const pumpVoltageMeasurement =
    assetMeasurementFactory.createPumpVoltage(assetId);

  return {
    measurementService,
    assetMeasurementRepositoryMock,
    measurementsBackofficeServiceMock,
    customerServiceMock,
    assetServiceMock,
    unitOfMeasureServiceMock,
    timeSeriesServiceMock,
    pumpVoltageMeasurement,
    pumpMetricId,
    engineMetricId,
    nonExistingMetricId,
    existingAssetPumpMetricId,
  };
};
