import { HttpException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { UserCreationParams, User, UserFactory } from './domain/user.entity';
import { UserApiController } from './user.controller';
import { UserService } from './user.service';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { testUserFactory } from './__tests__/factories';
import { Customer } from 'src/customers/domain/customer.entity';
import { UserMapper } from './dto/user.mapper';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';

describe('UserApiController', () => {
  let userApiController: UserApiController;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer])],
      providers: [
        {
          provide: UserService,
          useValue: { create: jest.fn() },
        },
        { provide: UserMapper, useClass: UserMapper },
      ],
      controllers: [UserApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    userApiController = moduleRef.get(UserApiController);
  });

  describe('can create', () => {
    let globalAdmin: User;
    let otherGlobalAdmin: User;
    let globalUser: User;
    let otherGlobalUser: User;
    let scopedUser: User;
    let otherCustomerScopedUser: User;
    let scopedAdmin: User;

    beforeAll(async () => {
      globalAdmin = testUserFactory.createGlobalScopeUser('admin', Role.ADMIN);
      otherGlobalAdmin = testUserFactory.createGlobalScopeUser(
        'other_admin',
        Role.ADMIN,
      );
      globalUser = testUserFactory.createGlobalScopeUser('user', Role.USER);
      otherGlobalUser = testUserFactory.createGlobalScopeUser(
        'other_user',
        Role.USER,
      );
      scopedUser = testUserFactory.createCustomerScopedUser('scoped_user', [
        { role: Role.USER, customerIds: [34] },
      ]);
      otherCustomerScopedUser = testUserFactory.createCustomerScopedUser(
        'other_scoped_user',
        [{ role: Role.USER, customerIds: [5] }],
      );
      scopedAdmin = testUserFactory.createCustomerScopedUser('scoped_admin', [
        { role: Role.ADMIN, customerIds: [34] },
      ]);
    });

    test('global admin can create a global admin', () => {
      expect(
        UserApiController.canCreateUser(globalAdmin, {
          ...otherGlobalAdmin,
          scopedRoles: [],
        }),
      ).toBeTruthy();
    });

    test('global user can not create a global admin', () => {
      expect(
        UserApiController.canCreateUser(globalUser, mapToParams(globalAdmin)),
      ).toBeFalsy();
    });

    test('global user can not create a global user', () => {
      expect(
        UserApiController.canCreateUser(
          globalUser,
          mapToParams(otherGlobalUser),
        ),
      ).toBeFalsy();
    });

    test('global admin can create scoped user', () => {
      expect(
        UserApiController.canCreateUser(globalAdmin, mapToParams(scopedUser)),
      ).toBeTruthy();
    });

    test('global admin can create scoped admin', () => {
      expect(
        UserApiController.canCreateUser(globalAdmin, mapToParams(scopedAdmin)),
      ).toBeTruthy();
    });

    test('scoped admin can create scoped user of same customer', () => {
      expect(
        UserApiController.canCreateUser(scopedAdmin, mapToParams(scopedUser)),
      ).toBeTruthy();
    });

    test('scoped admin can not create scoped user of other customer', () => {
      expect(
        UserApiController.canCreateUser(
          scopedAdmin,
          mapToParams(otherCustomerScopedUser),
        ),
      ).toBeFalsy();
    });
  });

  describe('create', () => {
    test('when ADMIN tries to create a SUPER_USER it should fail with an exception', async () => {
      const rejection = await expect(() =>
        userApiController.create(
          UserFactory.create({
            username: 'admin',
            password: 'somepass',
            email: '<EMAIL>',
            firstName: 'f',
            lastName: 'l',
            globalRole: Role.USER,
          }),
          {
            username: 'newUser',
            password: '123123',
            email: '<EMAIL>',
            global_role: 'ADMIN',
            first_name: 'f',
            last_name: 'l',
          },
        ),
      ).rejects;
      rejection.toThrowError(HttpException);
      rejection.toThrow('Forbidden');
    });
  });
});

const mapToParams = (user: User): UserCreationParams => {
  return {
    ...user,
    globalRole: user.globalRole,
    scopedRoles: Object.keys(Role)
      .map((role) => Number(role) as Role)
      .filter((role) => role !== undefined)
      .filter((role) => user.scopedRole(role).length > 0)
      .map((role) => ({ role: role, customerIds: user.scopedRole(role) })),
  };
};
