import { Test } from '@nestjs/testing';
import { UserService } from './user.service';
import { CustomerUserApiController } from './customer-user.controller';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { User } from './domain/user.entity';
import { testUserFactory } from './__tests__/factories';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { UserMapper } from './dto/user.mapper';
import { Customer } from 'src/customers/domain/customer.entity';

describe('CustomerUserApiController', () => {
  let customerUsersApiController: CustomerUserApiController;
  let userServiceMock: jest.Mocked<
    Pick<UserService, 'getAllByCustomerId' | 'update'>
  >;
  let globalAdmin: User;
  let scopedAdmin: User;

  beforeEach(async () => {
    userServiceMock = {
      getAllByCustomerId: jest.fn(async (_) => []),
      update: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer])],
      providers: [
        { provide: UserService, useValue: userServiceMock },
        { provide: UserMapper, useClass: UserMapper },
      ],
      controllers: [CustomerUserApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    globalAdmin = testUserFactory.createGlobalScopeUser(
      'global_admin',
      Role.ADMIN,
    );
    scopedAdmin = testUserFactory.createCustomerScopedUser('scoped_admin', [
      { role: Role.ADMIN, customerIds: [1, 2, 4] },
      { role: Role.USER, customerIds: [43] },
    ]);
    customerUsersApiController = moduleRef.get(CustomerUserApiController);
  });

  describe('getAll', () => {
    test('global admin should get all', async () => {
      await customerUsersApiController.getAll(globalAdmin, '2');

      expect(userServiceMock.getAllByCustomerId.mock.calls.length).toBe(1);
      expect(userServiceMock.getAllByCustomerId.mock.calls[0][0]).toBe(2);
    });

    test('scoped admin should get all if under admin scope', async () => {
      await customerUsersApiController.getAll(scopedAdmin, '2');

      expect(userServiceMock.getAllByCustomerId.mock.calls.length).toBe(1);
      expect(userServiceMock.getAllByCustomerId.mock.calls[0][0]).toBe(2);
    });

    test('scoped admin should get an exception if not under admin scope', async () => {
      await expect(
        customerUsersApiController.getAll(scopedAdmin, '43'),
      ).rejects.toThrow('Forbidden');
    });
  });

  describe('patchById', () => {
    test('global admin should patch', async () => {
      await customerUsersApiController.patchById(globalAdmin, '2', '54', {
        first_name: 'new name',
      });

      expect(userServiceMock.update.mock.calls.length).toBe(1);
      expect(userServiceMock.update.mock.calls[0][0]).toBe(54);
      expect(userServiceMock.update.mock.calls[0][3]).toBe(2);
    });

    test('scoped admin should patch if under admin scope', async () => {
      await customerUsersApiController.patchById(scopedAdmin, '2', '54', {
        first_name: 'new name',
      });

      expect(userServiceMock.update.mock.calls.length).toBe(1);
      expect(userServiceMock.update.mock.calls[0][0]).toBe(54);
      expect(userServiceMock.update.mock.calls[0][3]).toBe(2);
    });

    test('scoped admin should get an exception if user to patch not under admin scope', async () => {
      await expect(
        customerUsersApiController.patchById(scopedAdmin, '43', '77', {
          first_name: 'new name',
        }),
      ).rejects.toThrow('Forbidden');
    });
  });
});
