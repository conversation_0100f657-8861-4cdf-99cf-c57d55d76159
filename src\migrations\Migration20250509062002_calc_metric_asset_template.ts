import { Migration } from '@mikro-orm/migrations';

export class Migration20250509062002_calc_metric_asset_template extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "calculation_metric_instance" ("id" serial primary key, "calculation" int not null, "output_metric" int not null, "ispersisted" boolean not null, "poll_period" int null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_output_metric_unique" unique ("output_metric");');

    this.addSql('create table "calculation_metric" ("id" serial primary key, "calculation_metric_instance" int not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);');

    this.addSql('create table "calculation_metric_input" ("id" serial primary key, "input_label" varchar(255) not null, "calculation_instance" int not null, "metric" int null, "constant_number" varchar(255) null, "constant_string" varchar(30) null, "comment" varchar(50) null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);');

    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_calculation_foreign" foreign key ("calculation") references "calculation_template" ("id") on update cascade;');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_output_metric_foreign" foreign key ("output_metric") references "metric" ("id") on update cascade;');

    this.addSql('alter table "calculation_metric" add constraint "calculation_metric_calculation_metric_instance_foreign" foreign key ("calculation_metric_instance") references "calculation_metric_instance" ("id") on update cascade;');

    this.addSql('alter table "calculation_metric_input" add constraint "calculation_metric_input_calculation_instance_foreign" foreign key ("calculation_instance") references "calculation_metric_instance" ("id") on update cascade;');
    this.addSql('alter table "calculation_metric_input" add constraint "calculation_metric_input_metric_foreign" foreign key ("metric") references "metric" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_metric" drop constraint "calculation_metric_calculation_metric_instance_foreign";');

    this.addSql('alter table "calculation_metric_input" drop constraint "calculation_metric_input_calculation_instance_foreign";');

    this.addSql('drop table if exists "calculation_metric_instance" cascade;');

    this.addSql('drop table if exists "calculation_metric" cascade;');

    this.addSql('drop table if exists "calculation_metric_input" cascade;');
  }

}
