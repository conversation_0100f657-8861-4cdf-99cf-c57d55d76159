import { getRepositoryToken } from '@mikro-orm/nestjs';
import { Test } from '@nestjs/testing';
import { ValueType } from './domain/value-type.entity';
import { MeasurementsBackofficeService } from './measurements-backoffice.service';
import { DataType } from './domain/data-type.entity';
import { MeasurementType } from './domain/measurement-type.entity';
import { Location } from './domain/location.entity';

describe('Measurements backoffice service', () => {
  test('getting data types should only call data type repository', async () => {
    const {
      measurementsBackofficeService,
      dataTypeRepositoryMock,
      valueTypeRepositoryMock,
      measurementTypeRepositoryMock,
      locationRepositoryMock,
    } = await createMeasurementsBackofficeService();

    measurementsBackofficeService.getAll('data');

    expect(dataTypeRepositoryMock.findAll.mock.calls.length).toBe(1);
    expect(valueTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(measurementTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(locationRepositoryMock.findAll.mock.calls.length).toBe(0);
  });

  test('getting value types should only call value type repository', async () => {
    const {
      measurementsBackofficeService,
      dataTypeRepositoryMock,
      valueTypeRepositoryMock,
      measurementTypeRepositoryMock,
      locationRepositoryMock,
    } = await createMeasurementsBackofficeService();

    measurementsBackofficeService.getAll('value');

    expect(dataTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(valueTypeRepositoryMock.findAll.mock.calls.length).toBe(1);
    expect(measurementTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(locationRepositoryMock.findAll.mock.calls.length).toBe(0);
  });

  test('getting measurement types should only call measurement type repository', async () => {
    const {
      measurementsBackofficeService,
      dataTypeRepositoryMock,
      valueTypeRepositoryMock,
      measurementTypeRepositoryMock,
      locationRepositoryMock,
    } = await createMeasurementsBackofficeService();

    measurementsBackofficeService.getAll('measurement');

    expect(dataTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(valueTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(measurementTypeRepositoryMock.findAll.mock.calls.length).toBe(1);
    expect(locationRepositoryMock.findAll.mock.calls.length).toBe(0);
  });

  test('getting locations should only call location repository', async () => {
    const {
      measurementsBackofficeService,
      dataTypeRepositoryMock,
      valueTypeRepositoryMock,
      measurementTypeRepositoryMock,
      locationRepositoryMock,
    } = await createMeasurementsBackofficeService();

    measurementsBackofficeService.getAll('location');

    expect(dataTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(valueTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(measurementTypeRepositoryMock.findAll.mock.calls.length).toBe(0);
    expect(locationRepositoryMock.findAll.mock.calls.length).toBe(1);
  });
});

const createMeasurementsBackofficeService = async () => {
  const valueTypeRepositoryMock = { findAll: jest.fn() };
  const dataTypeRepositoryMock = { findAll: jest.fn() };
  const measurementTypeRepositoryMock = { findAll: jest.fn() };
  const locationRepositoryMock = { findAll: jest.fn() };
  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(ValueType),
        useValue: valueTypeRepositoryMock,
      },
      {
        provide: getRepositoryToken(DataType),
        useValue: dataTypeRepositoryMock,
      },
      {
        provide: getRepositoryToken(MeasurementType),
        useValue: measurementTypeRepositoryMock,
      },
      {
        provide: getRepositoryToken(Location),
        useValue: locationRepositoryMock,
      },
      MeasurementsBackofficeService,
    ],
  }).compile();

  const measurementsBackofficeService = moduleRef.get(
    MeasurementsBackofficeService,
  );

  return {
    measurementsBackofficeService,
    dataTypeRepositoryMock,
    valueTypeRepositoryMock,
    measurementTypeRepositoryMock,
    locationRepositoryMock,
  };
};
