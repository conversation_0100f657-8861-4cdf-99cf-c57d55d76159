import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ForgotPasswordSerivce } from "./forgot-password.service";

@Controller({
  path: "forgot",
  version: "0",
})
export class ForgotPasswordController {
  constructor(private readonly forgotPassword: ForgotPasswordSerivce) {}

  @Post("/email-username")
  async forgotUser(@Body() { user: emailUserName }: { user: string }) {
    return this.forgotPassword.forgotUserPassword(emailUserName);
  }

  @Get("/:token")
  async findUserByTokenAndResetPassowrd(@Param() { token }: { token: string }) {
    return await this.forgotPassword.findUserByTokenAndResetPassowrd(token);
  }

  @Post("reset")
  async resetPassowrd(
    @Body() reset: { token: string; password: string; reset_password: string }
  ) {
    return await this.forgotPassword.resetPassword(reset);
  }
}
