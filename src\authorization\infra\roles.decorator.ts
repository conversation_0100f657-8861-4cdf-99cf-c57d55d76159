import { SetMetadata } from "@nestjs/common";
import { Role } from "../domain/customer-user-role.entity";

export const ROLES_KEY = "roles";
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);

export const CUSTOMER_ROLE_KEY = "customer_role";
export const HasCustomerRole = (role: Role) =>
  SetMetadata(CUSTOMER_ROLE_KEY, role);

export const ROLE_KEY = "role";
export const HasRole = (role: Role) => SetMetadata(ROLE_KEY, role);
