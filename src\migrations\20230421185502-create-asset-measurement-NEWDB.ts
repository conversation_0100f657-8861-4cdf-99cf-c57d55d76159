import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230421185502 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset_measurement" ("id" serial primary key, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "measurement" int not null, "asset" int not null, "location" int null);',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "meas_fk" foreign key (measurement) references "measurement" ("id");',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "asset_fk" foreign key (measurement) references "asset" ("id");',
    );

    this.addSql(
      'alter table "asset_measurement" add constraint "location_fk" foreign key (measurement) references "location" ("id");',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "asset_measurement" cascade;');
  }
}
