import { Migration } from "@mikro-orm/migrations";

export class Migration20250311074919_nullable_alert_fields extends Migration {
  async up(): Promise<void> {
    // Drop existing foreign key constraint for condition column
    this.addSql(
      'alter table "alerts" drop constraint if exists "alerts_condition_foreign";'
    );

    // Make condition column nullable
    this.addSql('alter table "alerts" alter column "condition" drop not null;');

    // Make threshold_value and reset_deadband nullable while keeping them as real (floating-point)
    this.addSql(
      'alter table "alerts" alter column "threshold_value" type real using ("threshold_value"::real);'
    );
    this.addSql(
      'alter table "alerts" alter column "threshold_value" drop not null;'
    );
    this.addSql(
      'alter table "alerts" alter column "reset_deadband" type real using ("reset_deadband"::real);'
    );
    this.addSql(
      'alter table "alerts" alter column "reset_deadband" drop not null;'
    );

    // Re-add foreign key constraint for condition column with ON DELETE SET NULL
    this.addSql(
      'alter table "alerts" add constraint "alerts_condition_foreign" foreign key ("condition") references "alert_condition" ("id") on update cascade on delete set null;'
    );
  }

  async down(): Promise<void> {
    // Drop modified foreign key constraint
    this.addSql(
      'alter table "alerts" drop constraint if exists "alerts_condition_foreign";'
    );

    // Revert condition column to NOT NULL
    this.addSql('alter table "alerts" alter column "condition" set not null;');

    // Revert threshold_value and reset_deadband to NOT NULL and keep them as real
    this.addSql(
      'alter table "alerts" alter column "threshold_value" type real using ("threshold_value"::real);'
    );
    this.addSql(
      'alter table "alerts" alter column "threshold_value" set not null;'
    );
    this.addSql(
      'alter table "alerts" alter column "reset_deadband" type real using ("reset_deadband"::real);'
    );
    this.addSql(
      'alter table "alerts" alter column "reset_deadband" set not null;'
    );

    // Re-add original foreign key constraint for condition column
    this.addSql(
      'alter table "alerts" add constraint "alerts_condition_foreign" foreign key ("condition") references "alert_condition" ("id") on update cascade;'
    );
  }
}
