import { ApiProperty, ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { AssetTemplate } from "../domain/asset-template.entity";
import { MeasurementInstanceDto } from "./asset-template-instance.dto";
import { AssetDto } from "src/assets/dto/asset.dto";
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";

export class AssetTemplateMeasurementDto extends OmitType(
  MeasurementInstanceDto,
  ["id", "measurement_id", "tag", "unit_of_measure_id"]
) {}

export class EditAssetTemplateMeasurementDto extends OmitType(
  MeasurementInstanceDto,
  ["measurement_id", "tag", "unit_of_measure_id"]
) {}

export class AssetTemplateMeasurementCreationDto extends OmitType(
  AssetTemplateMeasurementDto,
  ["description", "location_id", "datasource_id", "meter_factor"]
) {
  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional()
  location_id?: number;

  @ApiPropertyOptional()
  datasource_id?: number;

  @ApiPropertyOptional()
  meter_factor?: number;
}

export class AssetTemplateDto
  implements Pick<AssetTemplate, "id" | "manufacturer">
{
  @ApiProperty()
  id!: number;

  @ApiProperty()
  manufacturer!: string;

  @ApiProperty()
  model_number!: string;

  @ApiProperty()
  asset_type_id!: number;

  @ApiProperty()
  customer!: number;

  @ApiProperty({ type: [AssetTemplateMeasurementDto] })
  measurements!: AssetTemplateMeasurementDto[];

  @ApiProperty({ type: [AssetDto] })
  asset?: AssetDto[];
}

export class AssetTemplateCreationDto extends OmitType(AssetTemplateDto, [
  "id",
  "measurements",
  "asset_type_id",
]) {
  @ApiProperty({ type: [AssetTemplateMeasurementCreationDto] })
  measurements!: AssetTemplateMeasurementCreationDto[];

  @ApiProperty()
  save_as_global_asset_template!: boolean;
}

export class AssetTemplateEditDto extends OmitType(AssetTemplateDto, [
  "manufacturer",
  "asset_type_id",
]) {
  @ApiProperty({ type: [EditAssetTemplateMeasurementDto] })
  measurements!: EditAssetTemplateMeasurementDto[];

  @ApiPropertyOptional()
  manufacturer?: string;

  @ApiPropertyOptional()
  asset_type_id?: number;
}

export class ExpressionVariableDto {
  @ApiProperty({ example: "$A" })
  @IsString()
  @IsNotEmpty()
  inputLabel: string;

  @ApiPropertyOptional({ example: 402 })
  @IsNumber()
  @IsOptional()
  metric?: number;

  @ApiPropertyOptional({ enum: ["number", "string"], example: "number" })
  @IsEnum(["number", "string"])
  @IsOptional()
  constantType?: "number" | "string";

  @ApiPropertyOptional({ example: "200" })
  @IsString()
  @IsOptional()
  constantValue?: string;

  @ApiPropertyOptional({ example: "with measurement ID" })
  @IsString()
  @IsOptional()
  comment?: string;
}

export class expressionInstanceDto {
  @ApiProperty()
  @IsNumber()
  templateId: number;

  @ApiProperty()
  @IsBoolean()
  ispersisted: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  iswriteback?: boolean;

  @ApiPropertyOptional()
  @IsNumber()
  pollPeriod?: number;

  @ApiProperty()
  @IsNumber()
  metricId: number;

  @ApiProperty({ type: [ExpressionVariableDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExpressionVariableDto)
  variables: ExpressionVariableDto[];
}
