import { Migration } from '@mikro-orm/migrations';

export class Migration20231206192843 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "user" add column "updated_at" timestamptz(6), add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "user" add constraint "user_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "user" drop constraint "user_updated_by_foreign";',
    );

    this.addSql('alter table "user" drop column "updated_at";');
    this.addSql('alter table "user" drop column "updated_by";');
  }
}
