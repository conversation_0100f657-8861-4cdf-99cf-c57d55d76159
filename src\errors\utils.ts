import { InvalidInputException } from './exceptions';

export const findOrThrowError = async <K, V>(
  id: K,
  queryFn: (id: K) => Promise<V | null>,
  notFoundErrorMessage: string,
) => {
  const value: V | null = await queryFn(id);
  if (value === null) {
    throw new InvalidInputException(notFoundErrorMessage);
  }
  return value;
};

export const optionallyFindOrThrowError = async <K, V>(
  id: K | undefined,
  queryFn: (id) => Promise<V | null>,
  notFoundErrorMessage: string,
) => {
  const value: V | null | undefined =
    id !== undefined ? await queryFn(id) : undefined;
  if (value === null) {
    throw new InvalidInputException(notFoundErrorMessage);
  }
  return value;
};
