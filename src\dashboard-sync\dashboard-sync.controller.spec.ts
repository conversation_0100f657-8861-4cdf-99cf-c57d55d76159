import { Test, TestingModule } from '@nestjs/testing';
import { DashboardSyncController } from './dashboard-sync.controller';

describe('DashboardSyncController', () => {
  let controller: DashboardSyncController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardSyncController],
    }).compile();

    controller = module.get<DashboardSyncController>(DashboardSyncController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
