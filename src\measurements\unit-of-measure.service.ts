import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import { UnitOfMeasure } from "./domain/unit-of-measure.entity";
import { EntityRepository } from "@mikro-orm/core";

@Injectable()
export class UnitOfMeasureService {
  constructor(
    @InjectRepository(UnitOfMeasure)
    private readonly unitOfMeasureRepository: EntityRepository<UnitOfMeasure>
  ) {}

  async getById(id: number) {
    return await this.unitOfMeasureRepository.findOne({ id });
  }

  async getAllById(ids: number[]) {
    return await this.unitOfMeasureRepository.find({ id: { $in: ids } });
  }

  async getAllByMeasurementType(measurementTypeId: number) {
    return await this.unitOfMeasureRepository.find(
      {
        measurementType: measurementTypeId,
      },
      { orderBy: { name: "<PERSON><PERSON>" } }
    );
  }

  async getMeasurementUnit(measurementTypeId: number, unitOfMeasure: number) {
    return await this.unitOfMeasureRepository.findOne({
      measurementType: measurementTypeId,
      id: unitOfMeasure,
    });
  }

  async getUnitOfMeausres() {
    return await this.unitOfMeasureRepository.findAll({
      populate: ["measurementType"],
      orderBy: { id: "ASC" },
    });
  }
}
