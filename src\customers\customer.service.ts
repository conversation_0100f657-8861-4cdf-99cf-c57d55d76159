import { EntityRepository, Reference } from '@mikro-orm/core';
import { InjectRepository } from '@mikro-orm/nestjs';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InvalidInputException } from 'src/errors/exceptions';
import { User } from 'src/users/domain/user.entity';
import { isValidNameId } from 'src/validations/validators';
import { Customer, CustomerId } from './domain/customer.entity';
import { CustomerUpdateDto, GetAllCustomersDto } from './dto/customer-creation.dto';

export type CustomerCreationData = Omit<
  Customer,
  'id' | 'enabled' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'
>;

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    private readonly customersRepository: EntityRepository<Customer>,
  ) { }

  async create(customer: CustomerCreationData, createdById: number) {
    if (!isValidNameId(customer.nameId)) {
      throw new InvalidInputException('Invalid customer name id');
    }

    if (
      (await this.customersRepository.count({ nameId: customer.nameId })) !== 0
    ) {
      throw new InvalidInputException(
        `Customer with name id "${customer.nameId}" already exists`,
      );
    }

    const newCustomer = this.customersRepository.create({
      ...customer,
      enabled: true,
      nameId: customer.nameId.toLowerCase(),
      createdBy: Reference.createFromPK(User, createdById),
    });
    await this.customersRepository.persistAndFlush(newCustomer);

    return newCustomer;
  }

  async update(customerId: CustomerId, customerDto: CustomerUpdateDto, createdById: number) {
    const customer = await this.customersRepository.findOne({ id: customerId })

    if (customer === null) {
      throw new NotFoundException('Customer does not exist');
    }

    if(customerDto.name) {
      customer.name = customerDto.name
    }

    if(customerDto.address) {
      customer.address = customerDto.address
    }

    if(customerDto.logo) {
      customer.logo = customerDto.logo
    }

    customer.updatedAt = new Date();
    customer.updatedBy = Reference.createFromPK(User, createdById);

    await this.customersRepository.persistAndFlush(customer);

    return customer;
  }

  async getAll(filter: GetAllCustomersDto): Promise<Customer[]> {
    return await this.customersRepository.findAll({
      orderBy: { name: 'ASC' },
      populate: filter.is_logo === 'true',
    });
  }

  async getAllById(ids: CustomerId[], filter: GetAllCustomersDto): Promise<Customer[]> {
    return await this.customersRepository.find(
      { id: { $in: ids } },
      {
        orderBy: { name: 'ASC' },
        populate: filter.is_logo === 'true'
      }
    );
  }

  async findById(id: number): Promise<Customer | null> {
    return await this.customersRepository.findOne({ id });
  }

  async findByNameId(nameId: string) {
    return await this.customersRepository.findOne({ nameId });
  }

  async allExistById(ids: CustomerId[]) {
    return (
      (await this.customersRepository.count({ id: { $in: ids } })) ===
      ids.length
    );
  }

  async getCustomerWithLogo(customerId: number) {
    return await this.customersRepository.findOne({ id: customerId }, { populate: ['logo'] });
  }
}
