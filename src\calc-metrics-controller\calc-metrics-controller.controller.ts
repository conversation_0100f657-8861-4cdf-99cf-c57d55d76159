import {
  Body,
  Controller,
  HttpCode,
  Post,
  UseGuards,
  ValidationPipe,
} from "@nestjs/common";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "src/users/domain/user.entity";
import { CalcMetricsControllerService } from "./calc-metrics-controller.service";
import {
  CalcMetricDTO,
  CalcMetricDTOArray,
} from "./dto/calculation-metric-create.dto";

@Controller({ path: "calc-metrics-template", version: "0" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class CalcMetricsControllerController {
  constructor(private readonly calcMetric: CalcMetricsControllerService) {}

  @Post("/")
  @HttpCode(201)
  @HasRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Body(ValidationPipe) body: CalcMetricDTOArray,
    @CookieToken() cookieToken: Request["headers"]
  ) {
    return await this.calcMetric.createCalcMetrics(body["data"], authUser);
  }
}
