import { MikroOrmModule } from "@mikro-orm/nestjs";
import { INestApplication, Module, VersioningType } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import cookieParser from "cookie-parser";
import { AuthAppLoggerMiddleware } from "src/lib/response.filter";
import { AlertStatsModule } from "./alert-stats/alert-stats.module";
import { schedulerConfig } from "./alert/alert.config";
import { AlertModule } from "./alert/alert.module";
import { AnnotationsModule } from "./annotations/annotations.module";
import { AssetTemplateModule } from "./asset-template/asset-template.module";
import { AssetTemplateDto } from "./asset-template/dto/asset-template.dto";
import { AssetModule } from "./assets/asset.module";
import authConfig from "./authentication/auth.config";
import { AuthModule } from "./authentication/auth.module";
import { CalcEngineModule } from "./calc-engine/calc-engine.module";
import { CalcMetricsControllerModule } from "./calc-metrics-controller/calc-metrics-controller.module";
import { CustomImageModule } from "./custom-image/custom-image.module";
import { CustomersModule } from "./customers/customers.module";
import { DashboardTemplateModule } from "./dashboard-template/dashboard-template.module";
import { DashboardsModule } from "./dashboards/dashboards.module";
import { DiagramModule } from "./diagram/diagram.module";
import { FactorModule } from "./factor/factor.module";
import { ForgotPasswordModule } from "./forgot-password/forgot-password.module";
import { HealthModule } from "./health/health.module";
import { ContextInterceptor } from "./interceptors/context.interceptor";
import { kafkaConfig } from "./kafka/kafka.config";
import {
  UnitsGroupDto,
  UnitsGroupUnitDto,
} from "./measurements/dto/units-group.dto";
import { fastApiConfig } from "./measurements/fastapi.config";
import { MeasurementModule } from "./measurements/measurement.module";
import { notificationConfig } from "./notification/notification.config";
import { NotificationModule } from "./notification/notification.module";
import { rabbitMQConfig } from "./rabbit-mq/rabbit-mq.config";
import { RabbitMqModule } from "./rabbit-mq/rabbit-mq.module";
import securityConfig, { SECURITY_CONFIG } from "./security/security.config";
import { timeSeriesConfig } from "./timeseries/timeseries.config";
import { TimeseriesModule } from "./timeseries/timeseries.module";
import { TwillioModule } from "./twillio/twillio.module";
import { WeatherModule } from "./weather/weather.module";

const DEVELOPMENT_ENV = "development";
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [
        rabbitMQConfig,
        authConfig,
        securityConfig,
        timeSeriesConfig,
        schedulerConfig,
        notificationConfig,
        kafkaConfig,
        fastApiConfig,
      ],
      isGlobal: true,
      envFilePath: `${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env`,
      cache: true,
    }),
    MikroOrmModule.forRoot(),
    AuthModule,
    CustomersModule,
    DashboardsModule,
    AssetModule,
    AssetTemplateModule,
    MeasurementModule,
    EventEmitterModule.forRoot(),
    WeatherModule,
    CalcEngineModule,
    FactorModule,
    AlertModule,
    TwillioModule,
    DashboardTemplateModule,
    ...(process.env.NODE_ENV?.toLowerCase() !== DEVELOPMENT_ENV
      ? [NotificationModule, RabbitMqModule]
      : [TimeseriesModule]),
    ...(process.env.NODE_ENV?.toLowerCase() === DEVELOPMENT_ENV
      ? [TimeseriesModule]
      : []),
    ForgotPasswordModule,
    HealthModule,
    DiagramModule,
    AlertStatsModule,
    AnnotationsModule,
    CustomImageModule,
    CalcMetricsControllerModule,
    // DashboardSyncModule,
  ],
  controllers: [],
  providers: [
    ...(process.env.NODE_ENV?.toLowerCase() !== DEVELOPMENT_ENV
      ? [
          {
            provide: APP_INTERCEPTOR,
            useClass: ContextInterceptor,
          },
        ]
      : []),
  ],
})
export class AppModule {
  static ENVIRONMENT = process.env.NODE_ENV?.toLowerCase() ?? DEVELOPMENT_ENV;
  static CONFIG_DIR = process.env.APP_CONFIG_DIR ?? process.cwd();

  static configure(app: INestApplication) {
    const securityConfiguration = app
      .get(ConfigService)
      .get<ReturnType<typeof securityConfig>>(SECURITY_CONFIG);

    if (!securityConfiguration) {
      throw new Error("Missing security configuration");
    }
    app.useGlobalInterceptors(new AuthAppLoggerMiddleware());

    app.enableCors({
      origin: securityConfiguration.corsOriginUrl,
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
      credentials: true,
    });
    app.enableShutdownHooks();
    app.enableVersioning({
      type: VersioningType.URI,
    });
    app.use(cookieParser());

    if (this.ENVIRONMENT === DEVELOPMENT_ENV) {
    }
    const config = new DocumentBuilder()
      .setTitle("Brompton Energy Admin API")
      .setDescription("Create/Retrieve/Update/Delete platform data")
      .build();
    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [AssetTemplateDto, UnitsGroupDto, UnitsGroupUnitDto],
    });

    SwaggerModule.setup("swagger", app, document);
  }
}
