import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  createCustomerWithAPI,
  deleteRedisKey,
  flushRedis,
  getStreamByKey,
  getTimeSeriesById,
  loginUser,
  setupApp,
} from './test-utils';
import request from 'supertest';
import {
  deleteMeasurementByTag,
  measurementFixtureFactory,
} from './fixtures/measurement.fixture';
import { assetFixtureFactory } from './fixtures/asset.fixture';
import { createSuperUser, deleteUser } from './fixtures/user.fixture';
import { deleteCustomer } from './fixtures/customer.fixture';

export const ASSET_MEASUREMENT_VISIBLE_FIELDS = new Set([
  'id',
  'asset_id',
  'measurement_id',
  'unit_of_measure_id',
  'tag',
  'metric_id',
  'data_type_id',
  'description',
  'type_id',
  'value_type_id',
  'location_id',
  'datasource_id',
  'meter_factor',
]);

describe('/customers/:customerId/assets/:assetId/measurements', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let httpClient: request.SuperAgentTest;
  let customerId: number;
  let assetFixture;
  let measurementFixture;

  // flush redis, create test user, customer, measurement backoffice and asset
  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    await flushRedis(testingModule);
    await deleteUser(testingModule, 'testuser');
    const superUserId = await createSuperUser(testingModule);

    httpClient = await loginUser(
      app.getHttpServer(),
      'testuser',
      'testpassword',
    );

    customerId = (await createCustomerWithAPI(httpClient, 'Kawasaki')).body.id;

    measurementFixture = await measurementFixtureFactory.createMetadata(
      testingModule,
    );
    assetFixture = await assetFixtureFactory.createEngines(
      testingModule,
      superUserId,
      customerId,
    );
  });

  afterAll(async () => {
    await measurementFixture.cleanUp(testingModule);
    await assetFixture.cleanUp(testingModule);
    await deleteCustomer(testingModule, 'Kawasaki');
    await deleteUser(testingModule, 'testuser');
    await flushRedis(testingModule);
    await app.close();
  });

  describe('POST', () => {
    describe('time-series measurement creation', () => {
      let response: request.Response;
      // create time-series measurement
      beforeAll(async () => {
        response = await httpClient
          .post(
            `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
          )
          .send({
            tag: 'Bike_Engine/Charge',
            type_id: measurementFixture.chargeTypeId,
            data_type_id: measurementFixture.realDataTypeId,
            value_type_id: measurementFixture.nominalValueTypeId,
            location_id: measurementFixture.outputLocationId,
            description: 'A motorbike engine',
            meter_factor: 1,
          });
      });

      afterAll(async () => {
        await deleteMeasurementByTag(testingModule, 'Bike_Engine/Charge');
        await deleteRedisKey(testingModule, response.body.measurement_id);
      });

      it('should return a 201', () => {
        expect(response.statusCode).toBe(201);
      });

      it('should expose only visible asset measurement fields', () => {
        expect(new Set(Object.keys(response.body))).toEqual(
          ASSET_MEASUREMENT_VISIBLE_FIELDS,
        );
      });

      it('should return asset measurement with all fields', () => {
        const responseBody = response.body;
        expect(responseBody.id).toBe(response.body.id);
        expect(responseBody.asset_id).toBe(assetFixture.bikeEngineAssetId);
        expect(responseBody.measurement_id).not.toBeNull();
        expect(responseBody.tag).toBe('Bike_Engine/Charge');
        expect(responseBody.data_type_id).toBe(
          measurementFixture.realDataTypeId,
        );
        expect(responseBody.description).toBe('A motorbike engine');
        expect(responseBody.type_id).toBe(measurementFixture.chargeTypeId);
        expect(responseBody.unit_of_measure_id).toBeNull();
        expect(responseBody.value_type_id).toBe(
          measurementFixture.nominalValueTypeId,
        );
        expect(responseBody.location_id).toBe(
          measurementFixture.outputLocationId,
        );
        expect(responseBody.meter_factor).toBe(1);
        expect(responseBody.datasource_id).toBeNull();
      });

      it('should create a redis raw timeseries with all labels', async () => {
        const info = await getTimeSeriesById(
          testingModule,
          response.body.measurement_id.toString(),
        );
        const findLabel = (key) =>
          info.labels.find((value) => value.name === key)?.value;

        expect(findLabel('tag')).toBe('Bike_Engine/Charge');
        expect(findLabel('meas_type')).toBe('Charge');
        expect(findLabel('equipment')).toBe('Bike Engine');
        expect(findLabel('data_type')).toBe('REAL');
        expect(findLabel('cust_id')).toBe(customerId.toString());
        expect(findLabel('customer')).toBe('Kawasaki');
        expect(findLabel('meter_factor')).toBe('1');
        expect(findLabel('units')).toBe('-');
        expect(findLabel('timezone')).toBe('UTC');
        expect(findLabel('agg')).toBe('raw');
      });

      it('should create a redis avg aggregation timeseries with all labels', async () => {
        const info = await getTimeSeriesById(
          testingModule,
          `${response.body.measurement_id}_avg`,
        );
        const findLabel = (key) =>
          info.labels.find((value) => value.name === key)?.value;

        expect(findLabel('tag')).toBe('Bike_Engine/Charge');
        expect(findLabel('meas_type')).toBe('Charge');
        expect(findLabel('equipment')).toBe('Bike Engine');
        expect(findLabel('data_type')).toBe('REAL');
        expect(findLabel('cust_id')).toBe(customerId.toString());
        expect(findLabel('customer')).toBe('Kawasaki');
        expect(findLabel('meter_factor')).toBe('1');
        expect(findLabel('units')).toBe('-');
        expect(findLabel('timezone')).toBe('UTC');
        expect(findLabel('agg')).toBe('avg');
      });

      it('should create a redis rule for each aggregation of the timeseries', async () => {
        const info = await getTimeSeriesById(
          testingModule,
          response.body.measurement_id.toString(),
        );

        expect(info.rules.length).toBe(5);
        expect(info.rules[0].key).toBe(`${response.body.measurement_id}_avg`);
        expect(info.rules[0].aggregationType).toBe('AVG');
        expect(info.rules[0].timeBucket).toBe(300000);
      });
    });

    describe('stream measurement creation', () => {
      let response: request.Response;
      // create stream measurement
      beforeAll(async () => {
        response = await httpClient
          .post(
            `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
          )
          .send({
            tag: 'Bike_Engine/Charge_Status',
            type_id: measurementFixture.chargeTypeId,
            data_type_id: measurementFixture.stringDataTypeId,
            value_type_id: measurementFixture.nominalValueTypeId,
            location_id: measurementFixture.outputLocationId,
            description: 'A motorbike engine',
          });
      });

      afterAll(async () => {
        await deleteMeasurementByTag(
          testingModule,
          'Bike_Engine/Charge_Status',
        );
        await deleteRedisKey(testingModule, response.body.measurement_id);
      });

      it('should create a redis stream with 0-1 id', async () => {
        const info = await getStreamByKey(
          testingModule,
          response.body.measurement_id.toString(),
        );
        expect(info.firstEntry?.id).toBe('0-1');
      });

      it('should create a redis stream with labels as message', async () => {
        const info = await getStreamByKey(
          testingModule,
          response.body.measurement_id.toString(),
        );
        expect(info.firstEntry?.message).toMatchObject({
          cust_id: customerId.toString(),
          customer: 'Kawasaki',
          equipment: 'Bike Engine',
          meas_type: 'Charge',
          tag: 'Bike_Engine/Charge_Status',
          timezone: 'UTC',
          units: '-',
          value: 'CREATED',
        });
      });
    });
  });

  describe('GET', () => {
    let bikeEngineChargeMeasurementId: number;
    // create two measurements
    beforeAll(async () => {
      bikeEngineChargeMeasurementId = (
        await httpClient
          .post(
            `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
          )
          .send({
            tag: 'Bike_Engine/Charge',
            type_id: measurementFixture.chargeTypeId,
            data_type_id: measurementFixture.realDataTypeId,
            value_type_id: measurementFixture.nominalValueTypeId,
            location_id: measurementFixture.outputLocationId,
            description: 'An electric motorbike engine charge',
            meter_factor: 1,
          })
      ).body.id;
      await httpClient
        .post(
          `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
        )
        .send({
          tag: 'Bike_Engine/Voltage',
          type_id: measurementFixture.voltageTypeId,
          data_type_id: measurementFixture.realDataTypeId,
          value_type_id: measurementFixture.nominalValueTypeId,
          location_id: measurementFixture.outputLocationId,
          description: 'An electric motorbike engine voltage',
          meter_factor: 1,
        });

      await httpClient
        .post(
          `/v0/customers/${customerId}/assets/${assetFixture.carEngineAssetId}/measurements`,
        )
        .send({
          tag: 'Car_Engine/Charge',
          type_id: measurementFixture.chargeTypeId,
          data_type_id: measurementFixture.realDataTypeId,
          value_type_id: measurementFixture.nominalValueTypeId,
          location_id: measurementFixture.outputLocationId,
          description: 'A car electric engine charge',
          meter_factor: 1,
        });
    });

    afterAll(async () => {
      await deleteMeasurementByTag(testingModule, 'Bike_Engine/Charge');
      await deleteMeasurementByTag(testingModule, 'Bike_Engine/Voltage');
      await deleteMeasurementByTag(testingModule, 'Car_Engine/Charge');
    });

    describe('querying all measurements for car engine', () => {
      let response: request.Response;
      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/customers/${customerId}/assets/${assetFixture.carEngineAssetId}/measurements`,
        );
      });

      it('should return a 200 code', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a list of one element', () => {
        expect(response.body.total).toBe(1);
        expect(response.body.items.length).toBe(1);
      });

      it('should expose only visible unit of asset measurement fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          ASSET_MEASUREMENT_VISIBLE_FIELDS,
        );
      });
    });

    describe('get bike engine charge measurement by id', () => {
      let response: request.Response;
      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}\
          /measurements/${bikeEngineChargeMeasurementId}`,
        );
      });

      it('should return a 200 code', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should expose only visible unit of asset measurement fields', () => {
        expect(new Set(Object.keys(response.body))).toEqual(
          ASSET_MEASUREMENT_VISIBLE_FIELDS,
        );
      });
    });

    describe('querying all measurements for bike engine', () => {
      let response: request.Response;
      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
        );
      });

      it('should return a 200 code', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a list of two elements', () => {
        expect(response.body.total).toBe(2);
        expect(response.body.items.length).toBe(2);
      });
    });
  });

  describe('DELETE', () => {
    // create measurement
    let response: request.Response;
    beforeAll(async () => {
      const chargeMeasurementId = (
        await httpClient
          .post(
            `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
          )
          .send({
            tag: 'Bike_Engine/Charge',
            type_id: measurementFixture.chargeTypeId,
            data_type_id: measurementFixture.realDataTypeId,
            value_type_id: measurementFixture.nominalValueTypeId,
            location_id: measurementFixture.outputLocationId,
            description: 'An electric motorbike engine charge',
            meter_factor: 1,
          })
      ).body.id;

      response = await httpClient.delete(
        `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}\
        /measurements/${chargeMeasurementId}`,
      );
    });

    afterAll(async () => {
      await deleteMeasurementByTag(testingModule, 'Bike_Engine/Charge');
    });

    it('should return a 204', async () => {
      expect(response.statusCode).toBe(204);
    });

    it('should remove measurement from its asset', async () => {
      const totalMeasurements = (
        await httpClient.get(
          `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
        )
      ).body.total;
      expect(totalMeasurements).toBe(0);
    });
  });

  describe('PATCH', () => {
    let response: request.Response;
    let chargeMeasurementId: number;

    beforeAll(async () => {
      chargeMeasurementId = (
        await httpClient
          .post(
            `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements`,
          )
          .send({
            tag: 'Bike_Engine/Charge',
            type_id: measurementFixture.chargeTypeId,
            data_type_id: measurementFixture.realDataTypeId,
            value_type_id: measurementFixture.nominalValueTypeId,
            location_id: measurementFixture.outputLocationId,
            description: 'An electric motorbike engine charge',
            meter_factor: 1,
          })
      ).body.id;

      response = await httpClient
        .patch(
          `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}\
        /measurements/${chargeMeasurementId}`,
        )
        .send({
          tag: 'Bike_Engine//Charge',
          description: 'Electric charge generated by engine',
        });
    });

    afterAll(async () => {
      await deleteMeasurementByTag(testingModule, 'Bike_Engine//Charge');
    });

    it('should return a 204', async () => {
      expect(response.statusCode).toBe(204);
    });

    it('should update asset measurement', async () => {
      const updatedAssetMeasurement = await httpClient.get(
        `/v0/customers/${customerId}/assets/${assetFixture.bikeEngineAssetId}/measurements/${chargeMeasurementId}`,
      );

      expect(updatedAssetMeasurement.body.description).toBe(
        'Electric charge generated by engine',
      );
      expect(updatedAssetMeasurement.body.tag).toBe('Bike_Engine//Charge');
    });

    it('should update redis tag accordingly', async () => {
      const info = await getTimeSeriesById(
        testingModule,
        chargeMeasurementId.toString(),
      );

      const findLabel = (key) =>
        info.labels.find((value) => value.name === key)?.value;

      expect(findLabel('tag')).toBe('Bike_Engine//Charge');
    });
  });
});
