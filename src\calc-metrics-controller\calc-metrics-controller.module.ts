import { MikroOrmModule } from "@mikro-orm/nestjs";
import { forwardRef, Module } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { CalculationPeriod } from "src/calc-engine/domain/calculation-period.entity";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { DataType } from "src/measurements/domain/data-type.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { CalcMetricsControllerController } from "./calc-metrics-controller.controller";
import { CalcMetricsControllerService } from "./calc-metrics-controller.service";
import { CalculationMetricInput } from "./domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "./domain/calculation-metric-instance.entity";
import { CalcMetricRepository } from "./repositoty/calc-metric.repositoy";
import { TransactionFactory } from "src/db/TransactionFactory";
import { Metric } from "src/assets/domain/metric.entity";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      Measurement,
      CalculationMetricInput,
      CalculationMetricInstance,
      CalculationPeriod,
      CalculationTemplate,
      DataType,
      AssetMeasurement,
      Metric,
    ]),
    forwardRef(() => AuthModule),
  ],
  controllers: [CalcMetricsControllerController],
  providers: [
    CalcMetricRepository,
    CalcMetricsControllerService,
    TransactionFactory,
  ],
})
export class CalcMetricsControllerModule {}
