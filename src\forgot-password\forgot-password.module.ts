import { MikroOrmModule } from "@mikro-orm/nestjs";
import { Lo<PERSON>, Modu<PERSON> } from "@nestjs/common";
import { User } from "src/users/domain/user.entity";
import { ForgotPassword } from "./domain/forgot-password.entity";
import { ForgotPasswordController } from "./forgot-password.controller";
import { ForgotPasswordSerivce } from "./forgot-password.service";
import { ForgotPasswordRepository } from "./repository/forgot-password.repository";
import { NotificationService } from "src/notification/notification.service";
import { PasswordEncoder } from "src/security/password-encoder.service";
@Module({
  controllers: [ForgotPasswordController],
  imports: [MikroOrmModule.forFeature([User, ForgotPassword])],
  providers: [
    ForgotPasswordSerivce,
    NotificationService,
    ForgotPasswordRepository,
    PasswordEncoder,
    {
      provide: Logger,
      useValue: new Logger(ForgotPasswordSerivce.name),
    },
  ],
  exports: [ForgotPasswordSerivce],
})
export class ForgotPasswordModule {}
