import { ApiProperty } from "@nestjs/swagger";
import { IsNumber } from "class-validator";
import { User } from "src/users/domain/user.entity";
import { FactorSchedule } from "../domain/factor-schedule.entity";
import { TimeVaryingFactor } from "../domain/TimeVaryingFactor.entity";
export class FactorScheduleDTO
  implements
    Pick<
      FactorSchedule,
      | "id"
      | "factor"
      | "createdAt"
      | "effectiveDate"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
    >
{
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  effectiveDate: string;

  @ApiProperty()
  factor: TimeVaryingFactor;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  createdBy: User;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  updatedBy: User;
}
