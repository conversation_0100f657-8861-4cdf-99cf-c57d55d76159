export const kafkaConfig = () => ({
    kafka: {
        brokers: process.env.KAFKA_BROKER || 'kafka-0.kafka.kafka.svc.cluster.local:9092',
        topicAlert: process.env.KAFKA_TOPIC_ALERT || 'be-alert-dev-new',
        clientId: process.env.KAFKA_CLIENT_ID || 'nestjs-client',
        kafkaGroupId: process.env.KAFKA_GROUP_ID || 'nestjs-consumer',
        kafkaGroupIdInstance: process.env.KAFKA_GROUP_ID_INSTANCE || 'nestjs-consumer-instance',
        topicNotification: process.env.KAFKA_TOPIC_NOTIFICATION || 'be-notification-dev-new',
        kafkaNotificationGroupId: process.env.KAFKA_NOTIFICATION_GROUP_ID || 'notification-consumer',
        kafkaNotificationIdInstance: process.env.KAFKA_NOTIFICATIONP_ID_INSTANCE || 'notification-consumer-instance',
        frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    },
});
  
  export type KafkaConfig = ReturnType<typeof kafkaConfig>;