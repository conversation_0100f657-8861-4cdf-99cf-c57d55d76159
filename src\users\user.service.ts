import { <PERSON>k<PERSON><PERSON><PERSON>, UseRequestContext } from "@mikro-orm/core";
import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { randomBytes } from "crypto";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { CustomerService } from "src/customers/customer.service";
import { CustomerId } from "src/customers/domain/customer.entity";
import { InvalidInputException } from "src/errors/exceptions";
import { isValidNameId } from "src/validations/validators";
import { PasswordEncoder } from "../security/password-encoder.service";
import {
  ScopedRole,
  User,
  UserCreationParams,
  UserFactory,
  UserId,
} from "./domain/user.entity";
import { ResetPasswordDto, UserFilters } from "./dto/user.dto";
import { UserRepository } from "./repository/user.repository";

export type UserUpdateData = Partial<
  Omit<UserCreationParams, "username"> & Pick<User, "enabled">
>;

@Injectable()
export class UserService implements OnModuleInit {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly customerService: CustomerService,
    private readonly orm: MikroORM,
    private readonly passwordEncoder: PasswordEncoder,
    private readonly logger: Logger
  ) {}

  async create(
    user: UserCreationParams,
    createdById: number | null
  ): Promise<User> {
    if (!isValidNameId(user.username)) {
      throw new InvalidInputException("Invalid username");
    }

    if (await this.findByEmail(user.email)) {
      throw new InvalidInputException(
        `User already exists with "${user.email}" email.`
      );
    }
    if (await this.findByUsername(user.username)) {
      throw new InvalidInputException(
        `User already exists with "${user.username}" user name.`
      );
    }
    if (
      user.scopedRoles &&
      !(await this.customerService.allExistById(
        this.mapScopedRolesToCustomerIds(user.scopedRoles)
      ))
    ) {
      throw new InvalidInputException("Customer id does not exist");
    }

    const newUser = UserFactory.create({
      ...user,
      username: user.username.toLowerCase(),
      email: user.email.toLowerCase(),
      password: await this.passwordEncoder.hash(user.password),
    });

    return await this.userRepository.add(newUser, createdById ?? undefined);
  }

  private mapScopedRolesToCustomerIds(scopedRoles: ScopedRole[]): CustomerId[] {
    return [
      ...new Set(scopedRoles.flatMap((scopedRole) => scopedRole.customerIds)),
    ];
  }

  async findByUsername(username: string) {
    return await this.userRepository.findByUsername(username);
  }

  async allCustomers(): Promise<CustomerId[]> {
    const filter = {}; // Add your filter object here
    return await this.customerService
      .getAll(filter)
      .then((customers) => customers.map((customer) => customer.id));
  }
  async findById(id: UserId, customerId?: CustomerId) {
    return await this.userRepository.findById(id, { customerId });
  }

  async getAllUsers(filters: UserFilters) {
    return await this.userRepository.getAllUsers(filters);
  }

  async update(
    userId: UserId,
    update: UserUpdateData,
    runBy: UserId,
    customerId?: CustomerId
  ) {
    const user = await this.findById(userId, customerId);

    if (user === null) {
      throw new NotFoundException("User does not exist");
    }

    // enable/disable
    if (update.enabled !== undefined) {
      user.enabled = update.enabled;
    }

    // personal information
    if (update.firstName) {
      user.firstName = update.firstName;
    }

    if (update.lastName) {
      user.lastName = update.lastName;
    }

    if (update.password) {
      user.password = await this.passwordEncoder.hash(update.password);
    }
    if (update.phone_no) {
      user.phone_no = update.phone_no;
    }
    if (update.country_code) {
      user.country_code = update.country_code;
    }
    if (update.email && (await this.findByEmail(update.email)) === null) {
      user.email = update.email;
    } else if (update.email) {
      throw new InvalidInputException("Email is already in use");
    }

    // authorization
    if (update.globalRole) {
      user.assignGlobalRole(update.globalRole);
    }

    if (
      update.scopedRoles &&
      !(await this.customerService.allExistById(
        this.mapScopedRolesToCustomerIds(update.scopedRoles)
      ))
    ) {
      throw new InvalidInputException("Customer id does not exist");
    } else if (update.scopedRoles) {
      user.assignScopedRoles(update.scopedRoles);
    }
    await this.userRepository.update(user, runBy);
  }

  async findByEmail(email: string) {
    return this.userRepository.findByEmail(email);
  }

  async createUpdateUserPreference(userId: UserId, preference: Object) {
    await this.userRepository.addUpdateUserPreference(userId, preference);
  }

  async getUserPreference(userId: UserId) {
    return await this.userRepository.getUserPreference(userId);
  }
  async resetPassword(userId: UserId, resetPassowrd: ResetPasswordDto) {
    const user = await this.findById(userId);
    if (
      user === null ||
      !(await this.passwordEncoder.verify(
        resetPassowrd.current_password,
        user.password
      ))
    ) {
      throw new InvalidInputException("Invalid password");
    }
    if (resetPassowrd.new_password !== resetPassowrd.confirm_password) {
      throw new InvalidInputException("Passwords do not match");
    }
    user.password = await this.passwordEncoder.hash(resetPassowrd.new_password);
    await this.userRepository.update(user, userId);
  }
  @UseRequestContext()
  async onModuleInit(): Promise<void> {
    const superUserCount = await this.userRepository.countByUsername(
      "superuser"
    );

    if (superUserCount === 0) {
      const randomPassword = await generateRandomString(16);

      const superUser: UserCreationParams = {
        username: "superuser",
        globalRole: Role.ADMIN,
        password: randomPassword,
        email: "<EMAIL>",
        firstName: "_",
        lastName: "_",
      };

      await this.create(superUser, null);

      this.logger.log(
        `Created user '${superUser.username}', password is: '${randomPassword}'`
      );
    }
  }
}

async function generateRandomString(length: number) {
  return await new Promise<string>((resolve, reject) =>
    randomBytes(length, (err, buf) => {
      if (err) {
        reject(err);
      } else {
        resolve(buf.toString("hex"));
      }
    })
  );
}
