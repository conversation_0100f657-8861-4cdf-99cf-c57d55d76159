import { EntityRepository, Reference } from '@mikro-orm/core';
import { AssetHierarchy } from '../domain/asset-hierarchy.entity';
import { InjectRepository } from '@mikro-orm/nestjs';
import { User, UserId } from 'src/users/domain/user.entity';
import { AssetId } from '../domain/asset.entity';

export class AssetHierarchyRepository {
  constructor(
    @InjectRepository(AssetHierarchy)
    private readonly entityRepository: EntityRepository<AssetHierarchy>,
  ) {}

  async add(
    assetHierarchy: AssetHierarchy,
    createdById?: UserId,
  ): Promise<AssetHierarchy> {
    if (createdById) {
      assetHierarchy.createdById = createdById;
    }
    assetHierarchy.createdAt = new Date();

    await this.entityRepository.persistAndFlush(assetHierarchy);

    return assetHierarchy;
  }

  async removeByParentAndChildId(
    parentId: AssetId,
    childId: AssetId,
    runById: UserId,
  ) {
    const assetHierarchy = await this.entityRepository.findOne({
      parent: { id: parentId },
      child: { id: childId },
    });

    if (assetHierarchy !== null) {
      assetHierarchy.deletedAt = new Date();
      assetHierarchy.deletedBy = Reference.createFromPK(User, runById);

      await this.entityRepository.persistAndFlush(assetHierarchy);
    }
  }
}
