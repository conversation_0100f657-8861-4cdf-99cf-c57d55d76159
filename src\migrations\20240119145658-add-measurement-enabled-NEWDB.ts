import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20240119145658 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'alter table "measurement" alter column "enabled" type boolean using ("enabled"::boolean);',
    );
    this.addSql(
      'alter table "measurement" alter column "enabled" set default true;',
    );
  }

  async conditionalDown(): Promise<void> {
    // do nothing
  }
}
