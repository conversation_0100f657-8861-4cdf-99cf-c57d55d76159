import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  UseGuards,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { ApiCreatedResponse, ApiOkResponse } from "@nestjs/swagger";
import authConfiguration from "src/authentication/auth.config";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import {
  CollectionDto,
  collectionSchema,
} from "src/serialization/collection.dto";
import { User } from "src/users/domain/user.entity";
import { AssetTypeService } from "./asset-type.service";
import { AssetTypeWithTemplateCount } from "./domain/asset-type.entity";
import {
  AssetTypeCreationDto,
  AssetTypeDto,
  AssetTypeEditDto,
} from "./dto/asset-type.dto";
import { MetricService } from "./metric.service";
import { TimeZoneService } from "./timezone.service";

@Controller({ version: "0", path: "assets-backoffice" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetsBackofficeApiController {
  constructor(
    private readonly assetTypesService: AssetTypeService,
    private readonly timeZonesService: TimeZoneService,
    private readonly metricService: MetricService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Post("asset-types")
  @ApiCreatedResponse({ type: AssetTypeDto })
  @HasRole(Role.POWER_USER)
  async createAssetType(
    @AuthUser() authUser: User,
    @Body() newAssetType: AssetTypeCreationDto
  ): Promise<AssetTypeDto> {
    const createdAssetType = await this.assetTypesService.create(
      {
        name: newAssetType.name,
        parentTypeId: newAssetType.parent_type_id,
      },
      authUser.id
    );

    return this.mapAssetTypeToDto(createdAssetType);
  }

  @Patch("asset-types/:assetTypeId")
  @HasRole(Role.POWER_USER)
  async updateAssetType(
    @AuthUser() authUser: User,
    @Body() editAssetType: AssetTypeEditDto
  ) {
    const updateAssetType = await this.assetTypesService.update(
      {
        id: editAssetType.id,
        name: editAssetType.name,
        parentTypeId: editAssetType.parent_type_id,
      },
      authUser.id
    );
    return this.mapAssetTypeToDto(updateAssetType);
  }

  private mapAssetTypeToDto(
    assetType: AssetTypeWithTemplateCount
  ): AssetTypeDto {
    const { id, name, parentType, asset_template_count } = assetType;
    return {
      id,
      name,
      parent_type_id: parentType?.id ?? null,
      asset_template_count: asset_template_count ?? 0,
    };
  }

  @Get("asset-types")
  @HasRole(Role.USER)
  @ApiOkResponse(collectionSchema(AssetTypeDto))
  async getAllAssetTypes(
    @CookieToken() headers: Request["headers"]
  ): Promise<CollectionDto<AssetTypeDto>> {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    const items = await this.assetTypesService.getAll(customerId);

    return {
      items: items.map(this.mapAssetTypeToDto),
      total: items.length,
    };
  }

  @Get("asset-types/:id")
  @HasRole(Role.POWER_USER)
  async getAssetTypes(@Param() { id }: { id: number }) {
    return await this.assetTypesService.findById(id);
  }

  @Delete("asset-types/:id")
  @HasRole(Role.POWER_USER)
  async deleteAssetType(@Param() { id }: { id: number }) {
    return this.assetTypesService.delete(id);
  }

  @Get("asset-types/:assetTypeId/metrics")
  @HasRole(Role.POWER_USER)
  async getAllMetricsByAssetTypeId(@Param("assetTypeId") assetTypeId: string) {
    const items = await this.metricService.getAllByAssetTypeId(
      Number(assetTypeId)
    );

    return {
      items: items,
      total: items.length,
    };
  }

  @Get("metrics")
  @HasRole(Role.POWER_USER)
  async getAllMetrics() {
    const items = await this.metricService.getAllAssetTypes();

    return {
      items: items,
      total: items.length,
    };
  }

  @Post("asset-types/:assetTypeId/metrics")
  @HasRole(Role.POWER_USER)
  async createMetric(
    @Param("assetTypeId") assetTypeId: number,
    @Body() newMetric: { name: string },
    @AuthUser() authUser: User
  ) {
    return await this.metricService.create({
      name: newMetric.name,
      assetTypeId: Number(assetTypeId),
      user: authUser,
    });
  }

  @Post("asset-types/:assetTypeId/bulk-metrics")
  @HasRole(Role.POWER_USER)
  async createMetricBulk(
    @Param("assetTypeId") assetTypeId: number,
    @Body() body: { name: string[] },
    @AuthUser() authUser: User
  ) {
    const metricNames = body.name;
    return await this.metricService.createMany({
      metrics: metricNames,
      assetTypeId: Number(assetTypeId),
      user: authUser,
    });
  }

  @Get("time-zones")
  @HasRole(Role.POWER_USER)
  async getAllTimeZones() {
    const items = await this.timeZonesService.getAll();

    return {
      items: items.map((timeZone) => timeZone.name),
      total: items.length,
    };
  }
}
