import { Migration } from '@mikro-orm/migrations';

export class Migration20240202193052 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_metric_unique";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "meter_template" to "asset_template";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_asset_template_foreign" foreign key ("asset_template") references "asset_template" ("id") on update cascade on delete cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_asset_template_metric_unique" unique ("asset_template", "metric");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_asset_template_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_asset_template_metric_unique";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "asset_template" to "meter_template";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_foreign" foreign key ("meter_template") references "asset_template" ("id") on update cascade on delete cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_metric_unique" unique ("meter_template", "metric");',
    );
  }
}
