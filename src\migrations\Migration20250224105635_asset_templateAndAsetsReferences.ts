import { Migration } from '@mikro-orm/migrations';

export class Migration20250224105635_asset_templateAndAsetsReferences extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "asset_template" add column "customer" int null;');
    this.addSql('alter table "asset_template" add constraint "asset_template_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade on delete set null;');

    this.addSql('alter table "asset" add column "assetTemplate" int null;');
    this.addSql('alter table "asset" add constraint "asset_assetTemplate_foreign" foreign key ("assetTemplate") references "asset_template" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "asset_template" drop constraint "asset_template_customer_foreign";');

    this.addSql('alter table "asset" drop constraint "asset_assetTemplate_foreign";');

    this.addSql('alter table "asset_template" drop column "customer";');

    this.addSql('alter table "asset" drop column "assetTemplate";');
  }

}
