import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from "@nestjs/common";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";

@Injectable()
export class AuthAppLoggerMiddleware implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const logger = new Logger("API");
    const { url, method, body } = context.switchToHttp().getRequest();
    const environment = process.env.NODE_ENV?.toLowerCase();
    return next.handle().pipe(
      tap(() => {
        const { statusCode } = context.switchToHttp().getResponse();

        logger.log(`${method} ${url} ${statusCode} `);
        if (environment !== "development") {
          logger.warn(body);
        }
      })
    );
  }
}
