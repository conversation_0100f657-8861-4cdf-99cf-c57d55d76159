import { Migration } from '@mikro-orm/migrations';

export class Migration20230313160258 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_type" add column "created_at" timestamptz(6) null, \
      add column "updated_at" timestamptz(6) null, add column "created_by" int null, \
      add column "updated_by" int null;',
    );
    this.addSql('alter table "asset_type" alter column "name" set not null;');

    this.addSql('alter table "asset_type" drop constraint "parent_fk";');
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_parent_type_foreign" \
      foreign key ("parent_type") references "asset_type" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "asset_type" drop constraint "asset_type_name_parent_type_key";',
    );
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_created_by_foreign" \
      foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_updated_by_foreign" \
      foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "asset_type" drop column "created";');
    this.addSql('alter table "asset_type" drop column "updated";');
    this.addSql('alter table "asset_type" drop column "createdby";');
    this.addSql('alter table "asset_type" drop column "updatedby";');

    this.addSql(
      'alter table "asset_type" add column "lower_case_name" VARCHAR \
      GENERATED ALWAYS AS (LOWER("name")) STORED not null;',
    );
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_parent_type_lower_case_name_unique" \
      unique nulls not distinct ("parent_type", "lower_case_name");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_type" drop constraint "asset_type_parent_type_lower_case_name_unique";',
    );
    this.addSql('alter table "asset_type" drop column "lower_case_name";');

    this.addSql(
      'alter table "asset_type" drop constraint "asset_type_created_by_foreign";',
    );
    this.addSql(
      'alter table "asset_type" drop constraint "asset_type_updated_by_foreign";',
    );

    this.addSql(
      'alter table "asset_type" add column "created" timestamptz null default null, \
      add column "updated" timestamptz null default null, add column "createdby" int4 null default null, add column "updatedby" int4 null default null;',
    );
    this.addSql('alter table "asset_type" alter column "name" drop not null;');
    this.addSql('alter table "asset_type" drop column "created_at";');
    this.addSql('alter table "asset_type" drop column "updated_at";');
    this.addSql('alter table "asset_type" drop column "created_by";');
    this.addSql('alter table "asset_type" drop column "updated_by";');
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_name_parent_type_key" unique ("name", "parent_type");',
    );
    this.addSql(
      'alter table "asset_type" drop constraint "asset_type_parent_type_foreign";',
    );
    this.addSql(
      'alter table "asset_type" add constraint "parent_fk" \
      foreign key ("parent_type") references "asset_type" ("id") on update cascade on delete set null;',
    );
  }
}
