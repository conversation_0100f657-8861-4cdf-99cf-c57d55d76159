import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import {
  setupApp,
  loginUser,
  createCustomerWithAPI,
  deleteRedisKey,
  getTimeSeriesById,
} from './test-utils';
import {
  deleteAssetByTag,
  deleteAssetType,
  createAssetType,
  createAssetWithAPI,
  assetFixtureFactory,
  OfficeSiteFixture,
  EnginesFixture,
} from './fixtures/asset.fixture';
import { createSuperUser, deleteUser } from './fixtures/user.fixture';
import { deleteCustomer } from './fixtures/customer.fixture';
import {
  deleteMeasurementByTag,
  measurementFixtureFactory,
} from './fixtures/measurement.fixture';

export const ASSET_VISIBLE_FIELDS = new Set([
  'id',
  'type_id',
  'customer_id',
  'description',
  'parent_ids',
  'children_ids',
  'tag',
  'time_zone',
]);

describe('/customers/:customerId/assets', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let superUserId: number;
  let customerId: number;
  let httpClient: request.SuperAgentTest;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    superUserId = await createSuperUser(testingModule);

    httpClient = await loginUser(
      app.getHttpServer(),
      'testuser',
      'testpassword',
    );

    customerId = (await createCustomerWithAPI(httpClient, 'Kawasaki')).body.id;
  });

  afterAll(async () => {
    await deleteCustomer(testingModule, 'Kawasaki');
    await deleteUser(testingModule, 'testuser');
    await app.close();
  });

  describe('POST', () => {
    describe('root asset with correct data', () => {
      let response: request.Response;
      let assetTypeId: number;

      beforeAll(async () => {
        await deleteAssetByTag(testingModule, 'Bike Engine');
        await deleteAssetType(testingModule, 'engine');

        assetTypeId = (await createAssetType(testingModule, 'Engine')).id;

        response = await createAssetWithAPI(httpClient, customerId, {
          tag: 'Bike Engine',
          type_id: assetTypeId,
          description: 'A motorbike engine',
          time_zone: 'Asia/Beirut',
          parent_ids: [],
        });
      });

      afterAll(async () => {
        await deleteAssetByTag(testingModule, 'Bike Engine');
        await deleteAssetType(testingModule, 'engine');
      });

      it('should return a 201', () => {
        expect(response.statusCode).toBe(201);
      });

      it('should return new asset with an id', () => {
        expect(response.body.id).not.toBeUndefined();
        expect(response.body.id).not.toBeNull();
      });

      it('should only return visible asset fields', () => {
        expect(new Set(Object.keys(response.body))).toEqual(
          ASSET_VISIBLE_FIELDS,
        );
      });

      it('should return fields loaded correctly', () => {
        expect(response.body.type_id).toBe(assetTypeId);
        expect(response.body.customer_id).toBe(customerId);
        expect(response.body.description).toBe('A motorbike engine');
        expect(response.body.parent_ids).toStrictEqual([]);
        expect(response.body.children_ids).toStrictEqual([]);
        expect(response.body.tag).toBe('Bike Engine');
        expect(response.body.time_zone).toBe('Asia/Beirut');
      });
    });

    describe('child asset with correct data', () => {
      let rootAssetResponse: request.Response;
      let response: request.Response;
      let assetTypeId: number;

      beforeAll(async () => {
        await deleteAssetByTag(testingModule, 'Showcase Belgrano');
        await deleteAssetByTag(testingModule, 'Showcase Arg');

        assetTypeId = (await createAssetType(testingModule, 'Location')).id;

        rootAssetResponse = await createAssetWithAPI(httpClient, customerId, {
          tag: 'Showcase Arg',
          type_id: assetTypeId,
          description: 'Cinema enterprise',
          parent_ids: [],
        });

        response = await createAssetWithAPI(httpClient, customerId, {
          tag: 'Showcase Belgrano',
          type_id: assetTypeId,
          description: 'Cinema located at Belgrano',
          parent_ids: [rootAssetResponse.body.id],
        });
      });

      afterAll(async () => {
        await deleteAssetByTag(testingModule, 'Showcase Belgrano');
        await deleteAssetByTag(testingModule, 'Showcase Arg');
        await deleteAssetType(testingModule, 'Location');
      });

      it('should return a 201', () => {
        expect(response.statusCode).toBe(201);
      });

      it('should return new asset with an id', () => {
        expect(response.body.id).not.toBeUndefined();
        expect(response.body.id).not.toBeNull();
      });

      it('should return new asset with correct parents id', () => {
        expect(response.body.parent_ids).toStrictEqual([
          rootAssetResponse.body.id,
        ]);
      });
    });
  });

  describe('GET', () => {
    let response: request.Response;
    let assetTypeId: number;
    let rootAssetId: number;

    beforeAll(async () => {
      await deleteAssetByTag(testingModule, 'Bike Engine');
      await deleteAssetType(testingModule, 'engine');

      assetTypeId = (await createAssetType(testingModule, 'Engine')).id;

      const rootAssetResponse = await createAssetWithAPI(
        httpClient,
        customerId,
        {
          tag: 'Bike Engine',
          type_id: assetTypeId,
          description: 'A motorbike engine',
          parent_ids: [],
        },
      );

      rootAssetId = rootAssetResponse.body.id;
    });

    afterAll(async () => {
      await deleteAssetByTag(testingModule, 'Bike Engine');
      await deleteAssetType(testingModule, 'engine');
    });

    describe('querying all root nodes assets and filtering by asset id', () => {
      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/customers/${customerId}/assets?rootNodes=true,ids=${rootAssetId}`,
        );
      });

      it('should return a collection with one element', () => {
        expect(response.body.total).toBe(1);
        expect(response.body.items.length).toBe(1);
      });

      it('should return the root asset', () => {
        const rootAssetResponse = response.body.items[0];
        expect(rootAssetResponse.type_id).toBe(assetTypeId);
        expect(rootAssetResponse.customer_id).toBe(customerId);
        expect(rootAssetResponse.description).toBe('A motorbike engine');
        expect(rootAssetResponse.parent_ids).toStrictEqual([]);
        expect(rootAssetResponse.children_ids).toStrictEqual([]);
        expect(rootAssetResponse.tag).toBe('Bike Engine');
      });
    });

    describe('querying exsiting asset by id', () => {
      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/customers/${customerId}/assets/${rootAssetId}`,
        );
      });

      it('should return asset with all fields', () => {
        expect(response.body.type_id).toBe(assetTypeId);
        expect(response.body.customer_id).toBe(customerId);
        expect(response.body.description).toBe('A motorbike engine');
        expect(response.body.parent_ids).toStrictEqual([]);
        expect(response.body.children_ids).toStrictEqual([]);
        expect(response.body.tag).toBe('Bike Engine');
      });
    });
  });

  describe('DELETE', () => {
    let officeSiteFixture: OfficeSiteFixture;
    let enginesFixture: EnginesFixture;

    beforeAll(async () => {
      officeSiteFixture = await assetFixtureFactory.createOfficeSite(
        testingModule,
        superUserId,
        customerId,
      );

      enginesFixture = await assetFixtureFactory.createEngines(
        testingModule,
        superUserId,
        customerId,
      );
    });

    afterAll(async () => {
      await officeSiteFixture.cleanUp();
      await enginesFixture.cleanUp();
    });

    describe('delete bike engine single asset', () => {
      let response: request.Response;
      beforeAll(async () => {
        response = await httpClient.delete(
          `/v0/customers/${customerId}/assets/${enginesFixture.bikeEngineAssetId}`,
        );
      });

      it('should return a 204 code', () => {
        expect(response.statusCode).toBe(204);
      });

      it('should no longer exist', async () => {
        const getResponse = await httpClient.get(
          `/v0/customers/${customerId}/assets/${enginesFixture.bikeEngineAssetId}`,
        );

        expect(getResponse.statusCode).toBe(404);
      });
    });

    describe('delete car engine asset with measurement', () => {
      let measurementFixture;
      let chargeAssetMeasurementId: number;
      let chargeMeasurementId: number;
      let response: request.Response;
      beforeAll(async () => {
        measurementFixture = await measurementFixtureFactory.createMetadata(
          testingModule,
        );
        const measurementResponse = (
          await httpClient
            .post(
              `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}/measurements`,
            )
            .send({
              tag: 'Car_Engine/Charge',
              type_id: measurementFixture.chargeTypeId,
              data_type_id: measurementFixture.realDataTypeId,
              value_type_id: measurementFixture.nominalValueTypeId,
              location_id: measurementFixture.outputLocationId,
              description: 'An electric car engine charge',
              meter_factor: 1,
            })
        ).body;
        chargeAssetMeasurementId = measurementResponse.id;
        chargeMeasurementId = measurementResponse.measurement_id;

        response = await httpClient.delete(
          `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}`,
        );
      });

      it('should return a 204 code', () => {
        expect(response.statusCode).toBe(204);
      });

      it('should no longer exist', async () => {
        const getResponse = await httpClient.get(
          `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}`,
        );

        expect(getResponse.statusCode).toBe(404);
      });

      it('should also remove measurement', async () => {
        const getResponse = await httpClient.get(
          `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}/measurements/${chargeAssetMeasurementId}`,
        );

        expect(getResponse.statusCode).toBe(404);
      });

      afterAll(async () => {
        await deleteRedisKey(testingModule, chargeMeasurementId);
        await deleteMeasurementByTag(testingModule, 'Car_Engine/Charge');
        await measurementFixture.cleanUp();
      });
    });
  });

  describe('PATCH', () => {
    describe('asset with no measurements', () => {
      let assetId: number;
      let response: request.Response;
      beforeAll(async () => {
        const assetTypeId = (await createAssetType(testingModule, 'Engine')).id;

        const rootAssetResponse = await createAssetWithAPI(
          httpClient,
          customerId,
          {
            tag: 'Bike Engine',
            type_id: assetTypeId,
            description: 'A motorbike engine',
            parent_ids: [],
          },
        );
        assetId = rootAssetResponse.body.id;

        response = await httpClient
          .patch(`/v0/customers/${customerId}/assets/${assetId}`)
          .send({
            description: 'An electric motorbike engine',
          });
      });

      afterAll(async () => {
        await deleteAssetByTag(testingModule, 'Bike Engine');
        await deleteAssetType(testingModule, 'engine');
      });

      it('should return a 204 response', () => {
        expect(response.statusCode).toBe(204);
      });

      it('should update asset', async () => {
        const updatedAsset = await httpClient.get(
          `/v0/customers/${customerId}/assets/${assetId}`,
        );

        expect(updatedAsset.body.description).toBe(
          'An electric motorbike engine',
        );
      });
    });

    describe('asset with measurement', () => {
      let enginesFixture: EnginesFixture;
      let measurementFixture;
      let response: request.Response;
      let chargeMeasurementId: number;
      beforeAll(async () => {
        enginesFixture = await assetFixtureFactory.createEngines(
          testingModule,
          superUserId,
          customerId,
        );
        measurementFixture = await measurementFixtureFactory.createMetadata(
          testingModule,
        );
        chargeMeasurementId = (
          await httpClient
            .post(
              `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}/measurements`,
            )
            .send({
              tag: 'Car_Engine/Charge',
              type_id: measurementFixture.chargeTypeId,
              data_type_id: measurementFixture.realDataTypeId,
              value_type_id: measurementFixture.nominalValueTypeId,
              location_id: measurementFixture.outputLocationId,
              description: 'An electric car engine charge',
              meter_factor: 1,
            })
        ).body.measurement_id;

        response = await httpClient
          .patch(
            `/v0/customers/${customerId}/assets/${enginesFixture.carEngineAssetId}`,
          )
          .send({ tag: 'EV Car Engine' });
      });

      afterAll(async () => {
        await deleteRedisKey(testingModule, chargeMeasurementId);
        await deleteMeasurementByTag(testingModule, 'Car_Engine/Charge');
        await deleteAssetByTag(testingModule, 'EV Car Engine');
        await enginesFixture.cleanUp();
        await measurementFixture.cleanUp();
      });

      it('should return a 204 response', () => {
        expect(response.statusCode).toBe(204);
      });

      it('should update redis tag accordingly', async () => {
        const info = await getTimeSeriesById(
          testingModule,
          chargeMeasurementId.toString(),
        );

        const findLabel = (key) =>
          info.labels.find((value) => value.name === key)?.value;

        expect(findLabel('equipment')).toBe('EV Car Engine');
      });
    });
  });
});
