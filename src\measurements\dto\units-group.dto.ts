import { ApiProperty } from '@nestjs/swagger';
import { UnitsGroup } from '../domain/units-group.entity';
import { UnitOfMeasureDto } from './units-of-measure.dto';

export class UnitsGroupDto implements Pick<UnitsGroup, 'id' | 'name'> {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  name!: string;
}

export class UnitsGroupUnitDto extends UnitOfMeasureDto {
  @ApiProperty()
  is_measurement_type_default!: boolean;
}
