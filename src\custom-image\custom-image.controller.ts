import { Body, Controller, Get, Param, ParseIntPipe, Post, UseGuards } from "@nestjs/common";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { CustomImageService } from "./custom-image.service";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { User } from "src/users/domain/user.entity";

@Controller({
  path: "customers/:customerId/custom-image",
  version: "0",
})
@UseGuards(JwtAuthGuard, CsrfGuard)
export class CustomImageController {
  constructor(private readonly customerImageService: CustomImageService) {}

  @Post()
  async createCustomerImage(
    @Param("customerId") customerId: string,
    @AuthUser() user: User,
    @Body() data: { image: string }
  ) {
    return await this.customerImageService.create({
      customerId: Number(customerId),
      logo: data.image,
      user,
    });
  }

  @Get()
  async getCustomerImage(@Param("customerId") customerId: number) {
    const items = await this.customerImageService.getCustomerImage(customerId);
    return {
      total: items.length,
      items,
    };
  }

  @Get("/:imageId")
  async getImageById(
    @Param("customerId") customerId: number,
    @Param("imageId", ParseIntPipe) imageId: number
  ) {
    return await this.customerImageService.getImage(imageId);
  }
}
