import { Test } from '@nestjs/testing';
import { RedisService } from 'src/redis/redis.service';
import { TimeSeriesService } from './time-series.service';
import { Asset, AssetFactory } from 'src/assets/domain/asset.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { AssetService } from 'src/assets/asset.service';
import { TimeSeriesAggregationType } from 'redis';
import { AssetMeasurement } from './domain/asset-measurement.entity';
import { CustomerService } from 'src/customers/customer.service';
import {
  assetMeasurementFactory,
  measurementBackofficeFactory,
} from './__tests__/factories';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { AssetType } from 'src/assets/domain/asset-type.entity';

describe('TimeSeries Service', () => {
  describe('create', () => {
    test('asset measurement with non existing asset should throw an exception', async () => {
      const {
        timeSeriesService,
        pumpAsset,
        nonExistingAssetId,
        fargoCustomer,
      } = await createTimeSeriesService();

      const assetMeasurement = createTestAssetMeasurement(pumpAsset);
      assetMeasurement.assetId = nonExistingAssetId;

      expect(
        timeSeriesService.create(assetMeasurement, fargoCustomer.id),
      ).rejects.toThrow('Asset not found');
    });

    test('non existing customer should throw an exception', async () => {
      const { timeSeriesService, pumpAsset, nonExistingCustomerId } =
        await createTimeSeriesService();
      const assetMeasurement = createTestAssetMeasurement(pumpAsset);

      expect(
        timeSeriesService.create(assetMeasurement, nonExistingCustomerId),
      ).rejects.toThrow('Customer not found');
    });

    test('non stream asset measurement should create timeseries and aggregates', async () => {
      const { timeSeriesService, redisServiceMock, pumpAsset, fargoCustomer } =
        await createTimeSeriesService();
      const assetMeasurement = createTestAssetMeasurement(pumpAsset);

      await timeSeriesService.create(assetMeasurement, fargoCustomer.id);

      expect(redisServiceMock.createTimeSeries.mock.calls.length).toBe(1);
      const timeSeriesArgs = redisServiceMock.createTimeSeries.mock.calls[0];
      const labels = timeSeriesArgs[1] ?? {};
      expect(timeSeriesArgs[0]).toBe('42');
      expect(labels.tag).toBe('Pump/voltage');
      expect(labels.meas_type).toBe('Voltage');
      expect(labels.equipment).toBe('Pump');
      expect(labels.data_type).toBe('REAL');
      expect(labels.cust_id).toBe('9');
      expect(labels.customer).toBe('Fargo');
      expect(labels.meter_factor).toBe('1');
      expect(labels.timezone).toBe('UTC');
      expect(labels.units).toBe('volts');
      expect(timeSeriesArgs[2]).toBe('RAW');
      expect(timeSeriesArgs[3]).toStrictEqual([
        TimeSeriesAggregationType.AVG,
        TimeSeriesAggregationType.MAX,
        TimeSeriesAggregationType.MIN,
        TimeSeriesAggregationType.VAR_P,
        TimeSeriesAggregationType.TWA,
      ]);
    });

    test('given a measurement with value type different to nonimal, it should be appended to the label', async () => {
      const { timeSeriesService, redisServiceMock, pumpAsset, fargoCustomer } =
        await createTimeSeriesService();
      const assetMeasurement = createTestAssetMeasurement(pumpAsset);
      assetMeasurement.measurement.valueType =
        measurementBackofficeFactory.createCalculatedValueType();

      await timeSeriesService.create(assetMeasurement, fargoCustomer.id);

      const labels = redisServiceMock.createTimeSeries.mock.calls[0][1] ?? {};
      expect(labels.meas_type).toBe('Voltage calculated');
    });

    describe('given a string data type measurement', () => {
      let redisServiceCreateTimeSeriesCalls;
      let redisServiceCreateStreamCalls;
      beforeAll(async () => {
        const {
          timeSeriesService,
          redisServiceMock,
          pumpAsset,
          fargoCustomer,
        } = await createTimeSeriesService();
        redisServiceCreateTimeSeriesCalls =
          redisServiceMock.createTimeSeries.mock.calls;
        redisServiceCreateStreamCalls =
          redisServiceMock.createStream.mock.calls;
        const assetMeasurement = createTestAssetMeasurement(pumpAsset);
        assetMeasurement.measurement.dataType =
          measurementBackofficeFactory.createStringDataType();

        await timeSeriesService.create(assetMeasurement, fargoCustomer.id);
      });

      it('should create a stream with given measurement id as key', () => {
        expect(redisServiceCreateTimeSeriesCalls.length).toBe(0);
        expect(redisServiceCreateStreamCalls.length).toBe(1);
        expect(redisServiceCreateStreamCalls[0][0]).toBe('42');
      });

      it('should remove data type and meter factor labels', () => {
        expect(redisServiceCreateStreamCalls[0][1].data_type).toBeUndefined();
        expect(
          redisServiceCreateStreamCalls[0][1].meter_factor,
        ).toBeUndefined();
      });

      it('should use 0 epoch as creation date', () => {
        expect(redisServiceCreateStreamCalls[0][2]).toBe(0);
      });
    });

    test('given an asset measurement with datasource, no timeseries should be created', async () => {
      const { timeSeriesService, redisServiceMock, pumpAsset, fargoCustomer } =
        await createTimeSeriesService();
      const assetMeasurement =
        createTestAssetMeasurementWithDatasource(pumpAsset);

      const redisServiceCreateTimeSeriesCalls =
        redisServiceMock.createTimeSeries.mock.calls;
      const redisServiceCreateStreamCalls =
        redisServiceMock.createStream.mock.calls;

      await timeSeriesService.create(assetMeasurement, fargoCustomer.id);

      expect(redisServiceCreateTimeSeriesCalls.length).toBe(0);
      expect(redisServiceCreateStreamCalls.length).toBe(0);
    });
  });

  describe('update', () => {
    test('asset measurement with non existing asset should throw an exception', async () => {
      const { timeSeriesService, pumpAsset, nonExistingAssetId } =
        await createTimeSeriesService();

      const assetMeasurement = createTestAssetMeasurement(pumpAsset);
      assetMeasurement.assetId = nonExistingAssetId;

      expect(timeSeriesService.update(assetMeasurement)).rejects.toThrow(
        'Asset not found',
      );
    });

    test('given a datasource measurement it should not be updated', async () => {
      const {
        timeSeriesService,
        redisServiceMock,
        assetServiceMock,
        pumpAsset,
      } = await createTimeSeriesService();
      const assetMeasurement =
        createTestAssetMeasurementWithDatasource(pumpAsset);
      assetServiceMock.findById = async (id) => {
        if (id === pumpAsset.id) {
          pumpAsset.tag = 'new tag';
          pumpAsset.timeZone = 'new timezone';
          return pumpAsset;
        }
        return null;
      };

      await timeSeriesService.update(assetMeasurement);

      expect(redisServiceMock.updateTimeSeries.mock.calls.length).toBe(0);
    });

    test('given a stream measurement it should not be updated', async () => {
      const {
        timeSeriesService,
        redisServiceMock,
        assetServiceMock,
        pumpAsset,
      } = await createTimeSeriesService();
      const assetMeasurement = createTestAssetMeasurement(pumpAsset);
      assetMeasurement.measurement.dataType =
        measurementBackofficeFactory.createStringDataType();
      assetServiceMock.findById = async (id) => {
        if (id === pumpAsset.id) {
          pumpAsset.tag = 'new tag';
          pumpAsset.timeZone = 'new timezone';
          return pumpAsset;
        }
        return null;
      };

      await timeSeriesService.update(assetMeasurement);

      expect(redisServiceMock.updateTimeSeries.mock.calls.length).toBe(0);
    });

    test('given an asset measurement its timeseries labels should be updated', async () => {
      const {
        timeSeriesService,
        redisServiceMock,
        assetServiceMock,
        pumpAsset,
      } = await createTimeSeriesService();
      const assetMeasurement = createTestAssetMeasurement(pumpAsset);
      assetServiceMock.findById = async (id) => {
        if (id === pumpAsset.id) {
          pumpAsset.tag = 'new tag';
          pumpAsset.timeZone = 'new timezone';
          return pumpAsset;
        }
        return null;
      };

      await timeSeriesService.update(assetMeasurement);

      expect(redisServiceMock.updateTimeSeries.mock.calls.length).toBe(1);
      const updateTimeSeriesCall =
        redisServiceMock.updateTimeSeries.mock.calls[0];
      expect(updateTimeSeriesCall[0]).toBe('42');
      expect(updateTimeSeriesCall[1]).toStrictEqual({
        tag: 'Pump/voltage',
        meas_type: 'Voltage',
        equipment: 'new tag',
        meter_factor: '1',
        timezone: 'new timezone',
        units: 'volts',
      });
      expect(updateTimeSeriesCall[2]).toStrictEqual([
        TimeSeriesAggregationType.AVG,
        TimeSeriesAggregationType.MAX,
        TimeSeriesAggregationType.MIN,
        TimeSeriesAggregationType.VAR_P,
        TimeSeriesAggregationType.TWA,
      ]);
    });
  });
});

const createTestAssetMeasurementWithDatasource = (
  asset: Asset,
): AssetMeasurement => {
  const assetMeasurement = assetMeasurementFactory.createNasaPumpVoltage(
    asset.id,
  );

  assetMeasurement.measurement.id = 43;

  return assetMeasurement;
};

const createTestAssetMeasurement = (asset: Asset): AssetMeasurement => {
  const assetMeasurement = assetMeasurementFactory.createPumpVoltage(asset.id);

  assetMeasurement.measurement.id = 42;

  return assetMeasurement;
};

const createTimeSeriesService = async () => {
  const redisServiceMock: jest.Mocked<
    Pick<
      RedisService,
      'delete' | 'createTimeSeries' | 'createStream' | 'updateTimeSeries'
    >
  > = {
    delete: jest.fn(),
    createTimeSeries: jest.fn(),
    createStream: jest.fn(),
    updateTimeSeries: jest.fn(),
  };

  const assetServiceMock: Pick<AssetService, 'findById'> = {
    findById: jest.fn(async (id) => (id === pumpAsset.id ? pumpAsset : null)),
  };

  const customerServiceMock: Pick<CustomerService, 'findById'> = {
    findById: jest.fn(async (id) =>
      id === fargoCustomer.id ? fargoCustomer : null,
    ),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [createMikroOrmTestModule([Asset, AssetType, Customer])],
    providers: [
      {
        provide: AssetService,
        useValue: assetServiceMock,
      },
      {
        provide: RedisService,
        useValue: redisServiceMock,
      },
      {
        provide: CustomerService,
        useValue: customerServiceMock,
      },
      TimeSeriesService,
    ],
  }).compile();
  const timeSeriesService = moduleRef.get(TimeSeriesService);

  const fargoCustomer = createCustomer();
  const nonExistingCustomerId = 404;

  const pumpAsset = createAsset(fargoCustomer);
  const nonExistingAssetId = 404;

  return {
    timeSeriesService,
    redisServiceMock,
    assetServiceMock,
    fargoCustomer,
    nonExistingCustomerId,
    pumpAsset,
    nonExistingAssetId,
  };
};

function createAsset(customer: Customer) {
  const asset = AssetFactory.create({
    tag: 'Pump',
    customerId: customer.id,
    assetTypeId: 3,
  });

  asset.id = 4242;

  return asset;
}

function createCustomer() {
  const customer = new Customer();
  customer.id = 9;
  customer.name = 'Fargo';
  return customer;
}
