import {
  Body,
  Controller,
  HttpCode,
  Param,
  Patch,
  UseGuards
} from "@nestjs/common";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import {
  HasCustomerRole
} from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "./domain/user.entity";
import { UserUpdateDto } from "./dto/user.dto";
import { UserMapper } from "./dto/user.mapper";
import { UserService } from "./user.service";

@Controller({ version: "0", path: "customers/:customerId/users" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class CustomerUserApiController {
  constructor(
    private readonly userService: UserService,
    private readonly userDtoMapper: UserMapper
  ) {}

  @Patch(":id")
  @HttpCode(204)
  @HasCustomerRole(Role.ADMIN)
  async patchById(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Param("id") id: string,
    @Body() userUpdate: UserUpdateDto
  ) {
    await this.userService.update(
      Number(id),
      {
        ...userUpdate,
        firstName: userUpdate.first_name,
        lastName: userUpdate.last_name,
        globalRole: userUpdate.global_role
          ? Role[userUpdate.global_role]
          : undefined,
        scopedRoles: userUpdate.scoped_roles?.map((scopedRole) => ({
          role: Role[scopedRole.role],
          customerIds: scopedRole.customer_ids,
        })),
      },
      authUser.id,
      Number(customerId)
    );
  }
}
