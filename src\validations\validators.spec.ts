import { isValidNameId } from './validators';

describe('Validators', () => {
  describe('Name id', () => {
    test('mixed case should return false', () => {
      expect(isValidNameId('uSeR')).toBe(false);
    });

    test('lower case should return true', () => {
      expect(isValidNameId('user')).toBe(true);
    });

    test('numbers should return true', () => {
      expect(isValidNameId('user32')).toBe(true);
    });

    test('dots should return false', () => {
      expect(isValidNameId('us.er.')).toBe(false);
    });

    test('spaces should return false', () => {
      expect(isValidNameId('us er')).toBe(false);
    });

    test('underscore should return true', () => {
      expect(isValidNameId('us_er')).toBe(true);
    });

    test('dash should return true', () => {
      expect(isValidNameId('us-er')).toBe(true);
    });
  });
});
