import { Migration } from '@mikro-orm/migrations';

export class Migration20230802201010 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_measurement" add column "deleted_at" timestamptz(6) null, add column "deleted_by" int null;',
    );
    this.addSql(
      'alter table "asset_measurement" add constraint "asset_measurement_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_measurement" drop constraint "asset_measurement_deleted_by_foreign";',
    );

    this.addSql('alter table "asset_measurement" drop column "deleted_at";');
    this.addSql('alter table "asset_measurement" drop column "deleted_by";');
  }
}
