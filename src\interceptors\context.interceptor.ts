import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { WinstonLogger } from '../logger/custom_logger';

@Injectable()
export class ContextInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const req = ctx.getRequest();

    // Extract variables from request parameters
    const customer_id = req.params['customerId'] || '00';
    const measurement_id = req.params['assetMeasurementId'] || null;
    const asset_id = req.params['assetId'] || null;

    // Attach variables to the request for downstream usage
    req['customer_id'] = customer_id;
    req['measurement_id'] = measurement_id;
    req['asset_id'] = asset_id;

    return next.handle().pipe(
      tap(() => {
        // Log the request with variables
        WinstonLogger.log({
          level: 'info',
          message: 'Request handled',
          customer_id,
          measurement_id,
          asset_id,
        });
      }),
    );
  }
}
