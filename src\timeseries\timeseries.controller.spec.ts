import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Test } from '@nestjs/testing';
import config from 'src/mikro-orm.config';
import { testUserFactory } from 'src/users/__tests__/factories';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { User } from 'src/users/domain/user.entity';
import { TimeseriesApiController } from './timeseries.controller';
import { Customer } from 'src/customers/domain/customer.entity';
import { HttpService } from '@nestjs/axios';
import { TIMESERIES_CONFIG } from './timeseries.config';
import { of } from 'rxjs';
import { AxiosResponse } from 'axios';

describe('TimeSeriesApiController', () => {
  let globalAdmin: User;
  let scopedUser: User;
  let timeseriesApiController: TimeseriesApiController;
  let httpServiceMock: jest.Mocked<Pick<HttpService, 'get'>>;

  beforeEach(async () => {
    httpServiceMock = {
      get: jest
        .fn()
        .mockImplementation(() => of({} as AxiosResponse<unknown, any>)),
    };

    const moduleRef = await Test.createTestingModule({
      imports: [
        MikroOrmModule.forRoot({
          ...config,
          entities: [Customer, User],
          allowGlobalContext: true,
          connect: false,
          dbName: 'test',
        }),
      ],
      providers: [
        { provide: HttpService, useValue: httpServiceMock },
        {
          provide: TIMESERIES_CONFIG,
          useValue: {
            host: 'test',
            port: 3333,
          },
        },
      ],
      controllers: [TimeseriesApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    globalAdmin = testUserFactory.createGlobalScopeUser(
      'global_admin',
      Role.ADMIN,
    );
    scopedUser = testUserFactory.createCustomerScopedUser('scoped_user', [
      { role: Role.USER, customerIds: [1, 2, 4] },
    ]);
    timeseriesApiController = moduleRef.get(TimeseriesApiController);
  });

  test('global admin should get current', async () => {
    await timeseriesApiController.getCurrent(globalAdmin, '42', '3');

    expect(httpServiceMock.get.mock.calls.length).toBe(1);
  });

  test('global admin should get history', async () => {
    await timeseriesApiController.getHistory(globalAdmin, '42', '3', 32423423);

    expect(httpServiceMock.get.mock.calls.length).toBe(1);
  });

  test('global admin should get aggegation', async () => {
    await timeseriesApiController.getAggregation(
      globalAdmin,
      '42',
      '3',
      32423423,
    );

    expect(httpServiceMock.get.mock.calls.length).toBe(1);
  });

  test('scoped user should get an exception getting current from customer outside scope', () => {
    expect(() =>
      timeseriesApiController.getCurrent(scopedUser, '42', '3'),
    ).rejects.toThrow('Forbidden');
  });

  test('scoped user should get current when customer in scope', async () => {
    await timeseriesApiController.getCurrent(globalAdmin, '42', '2');

    expect(httpServiceMock.get.mock.calls.length).toBe(1);
  });

  test('scoped user should get an exception getting history from customer outside scope', () => {
    expect(() =>
      timeseriesApiController.getHistory(scopedUser, '42', '3', 234234),
    ).rejects.toThrow('Forbidden');
  });

  test('scoped user should get an exception getting aggregation from customer outside scope', () => {
    expect(() =>
      timeseriesApiController.getAggregation(scopedUser, '42', '3', 234234),
    ).rejects.toThrow('Forbidden');
  });
});
