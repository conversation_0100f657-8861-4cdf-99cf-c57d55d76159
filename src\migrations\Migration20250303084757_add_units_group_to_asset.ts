import { Migration } from '@mikro-orm/migrations';

export class Migration20250303084757_add_units_group_to_asset extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "asset" add column "UnitsGroup" int null;');
    this.addSql('alter table "asset" add constraint "asset_UnitsGroup_foreign" foreign key ("UnitsGroup") references "units_group" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "asset" drop constraint "asset_UnitsGroup_foreign";');

    this.addSql('alter table "asset" drop column "UnitsGroup";');
  }

}
