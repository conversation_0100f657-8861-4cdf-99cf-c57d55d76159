import {
  CustomerUserRole,
  Role,
} from 'src/authorization/domain/customer-user-role.entity';
import { testUserFactory } from 'src/users/__tests__/factories';
import { AssetTemplateInstanceApiController } from './asset-template-instance.controller';
import { Test } from '@nestjs/testing';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { User } from 'src/users/domain/user.entity';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { Customer } from 'src/customers/domain/customer.entity';
import { AssetTemplateInstanceService } from './asset-template-instance.service';
import { AssetTemplateInstanceMapper } from './dto/asset-template-instance.mapper';

describe('AssetTemplateInstanceApiController', () => {
  let assetTemplateInstanceApiController: AssetTemplateInstanceApiController;
  const newInstanceParams = {
    units_group_id: 0,
    asset: {
      customer_id: 0,
      tag: '',
      type_id: 0,
      parent_ids: [],
    },
    measurements: [],
  };

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer, CustomerUserRole])],
      providers: [
        { provide: AssetTemplateInstanceService, useValue: {} },
        {
          provide: AssetTemplateInstanceMapper,
          useValue: new AssetTemplateInstanceMapper(),
        },
      ],
      controllers: [AssetTemplateInstanceApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    assetTemplateInstanceApiController = moduleRef.get(
      AssetTemplateInstanceApiController,
    );
  });

  describe('create', () => {
    test('scoped admin should not be able to create template instance on customer outside scope', () => {
      const scopedAdmin = testUserFactory.createCustomerScopedUser(
        'scoped_admin',
        [{ role: Role.ADMIN, customerIds: [4] }],
      );

      expect(() =>
        assetTemplateInstanceApiController.create(
          scopedAdmin,
          '42',
          '4',
          newInstanceParams,
        ),
      ).rejects.toThrow('Forbidden');
    });
  });
});
