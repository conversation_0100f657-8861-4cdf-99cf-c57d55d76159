import { Migration } from '@mikro-orm/migrations';

export class Migration20240103191120 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template" add column "created_at" timestamptz(6) null, add column "created_by" int null;',
    );
    this.addSql(
      'alter table "asset_template" add constraint "asset_template_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template" drop constraint "asset_template_created_by_foreign";',
    );

    this.addSql('alter table "asset_template" drop column "created_at";');
    this.addSql('alter table "asset_template" drop column "created_by";');
  }
}
