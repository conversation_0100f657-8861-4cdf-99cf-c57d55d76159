import { Migration } from '@mikro-orm/migrations';

export class Migration20230417140321 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "measurement_type" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "measurement_type" add constraint "measurement_type_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement_type" add constraint "measurement_type_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "measurement_type" drop column "created";');
    this.addSql('alter table "measurement_type" drop column "updated";');
    this.addSql('alter table "measurement_type" drop column "createdby";');
    this.addSql('alter table "measurement_type" drop column "updatedby";');

    this.addSql(
      'alter table "data_type" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "data_type" add constraint "data_type_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "data_type" add constraint "data_type_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "data_type" drop column "created";');
    this.addSql('alter table "data_type" drop column "updated";');
    this.addSql('alter table "data_type" drop column "createdby";');
    this.addSql('alter table "data_type" drop column "updatedby";');

    this.addSql(
      'alter table "value_type" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "value_type" add constraint "value_type_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "value_type" add constraint "value_type_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "value_type" drop column "created";');
    this.addSql('alter table "value_type" drop column "updated";');
    this.addSql('alter table "value_type" drop column "createdby";');
    this.addSql('alter table "value_type" drop column "updatedby";');
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "data_type" drop constraint "data_type_created_by_foreign";',
    );
    this.addSql(
      'alter table "data_type" drop constraint "data_type_updated_by_foreign";',
    );

    this.addSql(
      'alter table "measurement_type" drop constraint "measurement_type_created_by_foreign";',
    );
    this.addSql(
      'alter table "measurement_type" drop constraint "measurement_type_updated_by_foreign";',
    );

    this.addSql(
      'alter table "value_type" drop constraint "value_type_created_by_foreign";',
    );
    this.addSql(
      'alter table "value_type" drop constraint "value_type_updated_by_foreign";',
    );

    this.addSql(
      'alter table "data_type" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "data_type" drop column "created_at";');
    this.addSql('alter table "data_type" drop column "updated_at";');
    this.addSql('alter table "data_type" drop column "created_by";');
    this.addSql('alter table "data_type" drop column "updated_by";');

    this.addSql(
      'alter table "measurement_type" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "measurement_type" drop column "created_at";');
    this.addSql('alter table "measurement_type" drop column "updated_at";');
    this.addSql('alter table "measurement_type" drop column "created_by";');
    this.addSql('alter table "measurement_type" drop column "updated_by";');

    this.addSql(
      'alter table "value_type" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "value_type" drop column "created_at";');
    this.addSql('alter table "value_type" drop column "updated_at";');
    this.addSql('alter table "value_type" drop column "created_by";');
    this.addSql('alter table "value_type" drop column "updated_by";');
  }
}
