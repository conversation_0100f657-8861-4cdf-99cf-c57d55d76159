import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { setupApp, loginUser, createCustomerWithAPI } from './test-utils';
import { createSuperUser, deleteUser } from './fixtures/user.fixture';
import { deleteCustomer } from './fixtures/customer.fixture';

describe('/customers', () => {
  let app: INestApplication;
  let testingModule: TestingModule;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    await deleteUser(testingModule, 'testuser');
    await createSuperUser(testingModule);
  });

  afterAll(async () => {
    await deleteUser(testingModule, 'testuser');
    await app.close();
  });

  describe('POST new customer', () => {
    describe('with correct data', () => {
      let response: request.Response;

      beforeAll(async () => {
        deleteCustomer(testingModule, 'Kawasaki');

        const httpClient = await loginUser(
          app.getHttpServer(),
          'testuser',
          'testpassword',
        );

        response = await createCustomerWithAPI(httpClient, 'Kawasaki');
      });

      afterAll(async () => {
        deleteCustomer(testingModule, 'Kawasaki');
      });

      it('should return a 201', () => {
        expect(response.statusCode).toBe(201);
      });

      it('should return new customer with an id', () => {
        expect(response.body.id).not.toBeUndefined();
        expect(response.body.id).not.toBeNull();
      });

      it('should return new customer with given fields', () => {
        expect(response.body).toMatchObject({
          name_id: 'kawasaki',
          name: 'Kawasaki',
          address: 'Somewhere in japan',
        });
      });
    });
  });

  describe('GET all customers', () => {
    let httpClient: request.SuperAgentTest;

    beforeAll(async () => {
      httpClient = await loginUser(
        app.getHttpServer(),
        'testuser',
        'testpassword',
      );
    });

    describe('no customers loaded', () => {
      let response: request.Response;

      beforeAll(async () => {
        response = await httpClient.get('/v0/customers');
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return an empty list', () => {
        expect(response.body.total).toBe(0);
        expect(response.body.items).toStrictEqual([]);
      });
    });

    describe('customers loaded', () => {
      let response: request.Response;

      beforeAll(async () => {
        await deleteCustomer(testingModule, 'Kawasaki');
        await deleteCustomer(testingModule, 'Yamaha');

        await createCustomerWithAPI(httpClient, 'Kawasaki');
        await createCustomerWithAPI(httpClient, 'Yamaha');

        response = await httpClient.get('/v0/customers');
      });

      afterAll(async () => {
        await deleteCustomer(testingModule, 'Kawasaki');
        await deleteCustomer(testingModule, 'Yamaha');
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a list of size 2', () => {
        expect(response.body.total).toBe(2);
      });

      it('should return a list with the customers', () => {
        expect(response.body.items[0]).toMatchObject({
          name_id: 'kawasaki',
          name: 'Kawasaki',
          address: 'Somewhere in japan',
        });
        expect(response.body.items[1]).toMatchObject({
          name_id: 'yamaha',
          name: 'Yamaha',
          address: 'Somewhere in japan',
        });
      });
    });
  });
});
