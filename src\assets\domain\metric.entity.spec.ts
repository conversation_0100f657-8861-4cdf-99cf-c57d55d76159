import { Test } from '@nestjs/testing';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { User } from 'src/users/domain/user.entity';
import { AssetType } from './asset-type.entity';
import { Metric } from './metric.entity';

describe('Metric', () => {
  beforeAll(async () => {
    await Test.createTestingModule({
      imports: [createMikroOrmTestModule([Metric, AssetType, User, Customer])],
    }).compile();
  });

  describe('create', () => {
    it('should create an instance when given valid arguments', () => {
      const pumpAssetType = new AssetType({ name: 'Pump' });

      const rpmPumpMetric = new Metric({
        name: 'RPM',
        assetType: pumpAssetType,
      });

      expect(rpmPumpMetric.name).toBe('RPM');
      expect(rpmPumpMetric.assetType).toBe(pumpAssetType);
    });

    describe('name containing illegal characters should throw an exception', () => {
      const createMetricWithName = (name: string) => () => {
        const pumpAssetType = new AssetType({ name: 'Pump' });

        return new Metric({ name, assetType: pumpAssetType });
      };

      test('"', () => {
        expect(createMetricWithName('"Pump"voltage')).toThrow(
          'Name contains invalid characters',
        );
      });

      test("'", () => {
        expect(createMetricWithName("'Pump'voltage")).toThrow(
          'Name contains invalid characters',
        );
      });

      test('<space>', () => {
        expect(createMetricWithName('Pump voltage')).toThrow(
          'Name contains invalid characters',
        );
      });
    });
  });
});
