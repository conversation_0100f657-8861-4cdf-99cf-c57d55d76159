import { Migration } from '@mikro-orm/migrations';

export class Migration20231213144223 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "units_group_unit" drop column "created";');
    this.addSql('alter table "units_group_unit" drop column "updated";');
    this.addSql('alter table "units_group_unit" drop column "createdby";');
    this.addSql('alter table "units_group_unit" drop column "updatedby";');
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_created_by_foreign";',
    );
    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_updated_by_foreign";',
    );

    this.addSql(
      'alter table "units_group_unit" add column "created" timestamptz null default null, add column "updated" timestamptz null default null, add column "createdby" int4 null default null, add column "updatedby" int4 null default null;',
    );
    this.addSql('alter table "units_group_unit" drop column "created_at";');
    this.addSql('alter table "units_group_unit" drop column "updated_at";');
    this.addSql('alter table "units_group_unit" drop column "created_by";');
    this.addSql('alter table "units_group_unit" drop column "updated_by";');
  }
}
