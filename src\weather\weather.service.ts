import { Injectable, NotFoundException } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class WeatherService {
  async getStationWeather(stationId: number) {
    const weatherRes = await axios.get(`${process.env.TEMPEST_WEATHER_API_URL}/observations/station/${stationId}?token=${process.env.TEMPEST_WEATHER_API_TOKEN}`).catch(error => error)
    if (weatherRes.status !== 200) {
      throw new NotFoundException('Station not found')
    }

    const { station_units } = weatherRes.data;
    const weatherForecastRes = await axios.get(`${process.env.TEMPEST_WEATHER_API_URL}/better_forecast?token=${process.env.TEMPEST_WEATHER_API_TOKEN}&station_id=${stationId}&units_temp=${station_units.units_temp}&units_wind=${station_units.units_wind}&units_pressure=${station_units.units_pressure}&units_precip=${station_units.units_precip}&units_distance=${station_units.units_distance}`).catch(error => error)

    if (weatherForecastRes.status !== 200) {
      throw new NotFoundException('Station forecast not found')
    }
    const { current_conditions } = weatherForecastRes.data;

    return {
      air_temperature: current_conditions?.air_temperature ?? 0,
      feels_like: current_conditions?.feels_like ?? 0,
      relative_humidity: current_conditions?.relative_humidity ?? 0,
      sea_level_pressure: current_conditions?.sea_level_pressure ?? 0,
      wind_direction: current_conditions?.wind_direction ?? 0,
      wind_direction_cardinal: current_conditions?.wind_direction_cardinal ?? 0,
      conditions: current_conditions?.conditions ?? 0,
      wind_avg: current_conditions?.wind_avg ?? 0,
      uv: current_conditions?.uv ?? 0,
      station_units: station_units ?? {},
    };
  }
}
