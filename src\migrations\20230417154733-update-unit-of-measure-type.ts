import { Migration } from '@mikro-orm/migrations';

export class Migration20230417154733 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "unit_of_measure" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "unit_of_measure" add constraint "unit_of_measure_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "unit_of_measure" add constraint "unit_of_measure_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "unit_of_measure" drop column "created";');
    this.addSql('alter table "unit_of_measure" drop column "updated";');
    this.addSql('alter table "unit_of_measure" drop column "createdby";');
    this.addSql('alter table "unit_of_measure" drop column "updatedby";');

    this.addSql('alter table "unit_of_measure" drop constraint "type_fk";');
    this.addSql(
      'alter table "unit_of_measure" add constraint "unit_of_measure_m_type_foreign" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "unit_of_measure" drop constraint "unit_of_measure_m_type_foreign";',
    );
    this.addSql(
      'alter table "unit_of_measure" add constraint "type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "unit_of_measure" drop constraint "unit_of_measure_created_by_foreign";',
    );
    this.addSql(
      'alter table "unit_of_measure" drop constraint "unit_of_measure_updated_by_foreign";',
    );

    this.addSql(
      'alter table "unit_of_measure" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "unit_of_measure" drop column "created_at";');
    this.addSql('alter table "unit_of_measure" drop column "updated_at";');
    this.addSql('alter table "unit_of_measure" drop column "created_by";');
    this.addSql('alter table "unit_of_measure" drop column "updated_by";');
  }
}
