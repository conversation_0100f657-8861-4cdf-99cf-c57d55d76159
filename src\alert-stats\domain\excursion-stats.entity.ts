import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON>, Property } from "@mikro-orm/core";
import { Alerts } from "src/alert/domain/alert.entity";

@Entity()
export class ExcursionStats {
  @PrimaryKey()
  id: number;

  @ManyToOne({ entity: () => Alerts, fieldName: "alert_id", eager: true })
  alert!: Alerts;

  @Property()
  start_time!: Date;

  @Property()
  end_time!: Date;

  @Property()
  duration!: string;

  @Property({ nullable: true })
  max_value?: number;

  @Property({ nullable: true })
  min_value?: number;

  @Property({ nullable: true })
  avg_value?: number;

  @Property({ type: "interval", nullable: true })
  time_duration: {
    days?: number;
    hours?: number;
    minutes?: number;
    seconds?: number;
    milliseconds?: number;
  };

  get formattedTimeDuration(): string {
    if (!this.time_duration) return "N/A"; // Return 'N/A' if no time_duration is set

    let formattedDuration = "";

    if (this.time_duration.days) {
      formattedDuration += `${this.time_duration.days} day${
        this.time_duration.days > 1 ? "s" : ""
      } `;
    }

    if (this.time_duration.hours) {
      formattedDuration += `${this.time_duration.hours} hour${
        this.time_duration.hours > 1 ? "s" : ""
      } `;
    }

    if (this.time_duration.minutes) {
      formattedDuration += `${this.time_duration.minutes} minute${
        this.time_duration.minutes > 1 ? "s" : ""
      } `;
    }

    if (this.time_duration.seconds) {
      formattedDuration += `${this.time_duration.seconds} second${
        this.time_duration.seconds > 1 ? "s" : ""
      } `;
    }

    if (this.time_duration.milliseconds) {
      formattedDuration += `${this.time_duration.milliseconds} millisecond${
        this.time_duration.milliseconds > 1 ? "s" : ""
      }`;
    }

    return formattedDuration.trim() || "0 seconds"; // If no match, assume 0 seconds
  }
}
