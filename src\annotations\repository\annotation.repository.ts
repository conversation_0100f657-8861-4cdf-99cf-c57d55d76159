import { EntityRepository, FilterQuery, FindOneOptions } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Annotation } from "../domain/annotation.entity";

export class AnnotationRepository {
  constructor(
    @InjectRepository(Annotation)
    private repository: EntityRepository<Annotation>
  ) {}

  async createAnnotation(newAnnotation: Annotation) {
    const annotaiton = await this.repository.create({
      ...newAnnotation,
    });
    await this.repository.persistAndFlush(annotaiton);
    return annotaiton;
  }

  async updateAnnotation(updateAnnotation: Annotation) {
    // Persist the changes
    return await this.repository.persistAndFlush(updateAnnotation);
  }

  async getAnnotation({
    dashboard_id,
    widget_id,
    measureId,
    startTime,
    endTime,
  }: {
    dashboard_id: number;
    widget_id: number;
    measureId: number;
    startTime: number;
    endTime: number;
  }) {
    return await this.repository.find({
      dashboard: {
        id: dashboard_id,
      },
      measurement_id: {
        id: measureId,
      },
      widget_id,
      time_of_annotation: { $gte: startTime, $lte: endTime }, // Use epoch timestamps
    });
  }
  async findAnnotation(
    where: FilterQuery<Annotation>,
    options?: FindOneOptions<Annotation, never>
  ) {
    return await this.repository.findOne(where, options);
  }
}
