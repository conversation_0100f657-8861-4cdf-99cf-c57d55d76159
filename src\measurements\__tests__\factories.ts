import { AssetMeasurement } from 'src/measurements/domain/asset-measurement.entity';
import { MeasurementType } from '../domain/measurement-type.entity';
import { DataType } from '../domain/data-type.entity';
import { ValueType } from '../domain/value-type.entity';
import { UnitOfMeasure } from '../domain/unit-of-measure.entity';
import { Location } from '../domain/location.entity';
import { Datasource } from '../domain/datasource.entity';
import { Metric } from 'src/assets/domain/metric.entity';
import { AssetType } from 'src/assets/domain/asset-type.entity';

export const assetMeasurementFactory = {
  createPumpVoltage: (assetId: number): AssetMeasurement =>
    new AssetMeasurement({
      tag: 'Pump/voltage',
      assetId,
      measurementType:
        measurementBackofficeFactory.createVoltageMeasurementType(),
      dataType: measurementBackofficeFactory.createRealDataType(),
      valueType: measurementBackofficeFactory.createNominalValueType(),
      unitOfMeasure: measurementBackofficeFactory.createVoltsUnitOfMeasure(),
      meterFactor: 1,
    }),
  createPumpPower: (assetId: number): AssetMeasurement => {
    const powerPumpMetric = new Metric({
      name: 'Pump/Power',
      assetType: new AssetType({ name: 'Pump' }),
    });
    powerPumpMetric.id = 4;

    return new AssetMeasurement({
      tag: 'Pump/power',
      assetId,
      measurementType:
        measurementBackofficeFactory.createPowerMeasurementType(),
      dataType: measurementBackofficeFactory.createRealDataType(),
      valueType: measurementBackofficeFactory.createNominalValueType(),
      unitOfMeasure: measurementBackofficeFactory.createWattsUnitOfMeasure(),
      metric: powerPumpMetric,
      meterFactor: 1,
    });
  },
  createNasaPumpVoltage: (assetId: number): AssetMeasurement =>
    new AssetMeasurement({
      tag: 'NASA-Pump/voltage',
      assetId,
      measurementType:
        measurementBackofficeFactory.createVoltageMeasurementType(),
      dataType: measurementBackofficeFactory.createRealDataType(),
      valueType: measurementBackofficeFactory.createNominalValueType(),
      unitOfMeasure: measurementBackofficeFactory.createVoltsUnitOfMeasure(),
      datasource: datasourceFactory.createNasaDatasource(),
      meterFactor: 1,
    }),
};

export const measurementBackofficeFactory = {
  createVoltageMeasurementType: (): MeasurementType => {
    const voltageMeasurementType = new MeasurementType();

    voltageMeasurementType.id = 4;
    voltageMeasurementType.name = 'Voltage';

    return voltageMeasurementType;
  },

  createPowerMeasurementType: (): MeasurementType => {
    const powerMeasurementType = new MeasurementType();

    powerMeasurementType.id = 40;
    powerMeasurementType.name = 'Power';

    return powerMeasurementType;
  },

  createCurrentMeasurementType: (): MeasurementType => {
    const currentMeasurementType = new MeasurementType();

    currentMeasurementType.id = 5;
    currentMeasurementType.name = 'Current';

    return currentMeasurementType;
  },

  createCountMeasurementType: (): MeasurementType => {
    const voltageMeasurementType = new MeasurementType();

    voltageMeasurementType.id = 43;
    voltageMeasurementType.name = 'Count';

    return voltageMeasurementType;
  },

  createRealDataType: (): DataType => {
    const realDataType = new DataType();

    realDataType.id = 3;
    realDataType.name = 'REAL';

    return realDataType;
  },

  createStringDataType: (): DataType => {
    const stringDataType = new DataType();

    stringDataType.id = 5;
    stringDataType.name = 'STRING';

    return stringDataType;
  },

  createNominalValueType: (): ValueType => {
    const nominalValueType = new ValueType();

    nominalValueType.id = 1;
    nominalValueType.name = 'nominal';

    return nominalValueType;
  },

  createCalculatedValueType: (): ValueType => {
    const calculatedValueType = new ValueType();

    calculatedValueType.id = 4;
    calculatedValueType.name = 'calculated';

    return calculatedValueType;
  },

  createVoltsUnitOfMeasure: (): UnitOfMeasure => {
    const voltsUnit = new UnitOfMeasure();

    voltsUnit.id = 4;
    voltsUnit.name = 'volts';

    return voltsUnit;
  },

  createWattsUnitOfMeasure: (): UnitOfMeasure => {
    const wattsUnit = new UnitOfMeasure();

    wattsUnit.id = 40;
    wattsUnit.name = 'watts';

    return wattsUnit;
  },

  createCurrentUnitOfMeasure: (): UnitOfMeasure => {
    const currentUnit = new UnitOfMeasure();

    currentUnit.id = 7;
    currentUnit.name = 'current';

    return currentUnit;
  },

  createFrontLocation: (): Location => {
    const frontLocation = new Location();

    frontLocation.id = 777;
    frontLocation.name = 'FRONT';

    return frontLocation;
  },
};

export const datasourceFactory = {
  createNasaDatasource: (): Datasource => {
    const nasaDatasource = new Datasource();

    nasaDatasource.id = 6;
    nasaDatasource.name = 'Nasa - API';
    nasaDatasource.uri = 'https://nasa.gov/api';

    return nasaDatasource;
  },
};
