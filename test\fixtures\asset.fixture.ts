import { MikroORM, RequestContext } from '@mikro-orm/core';
import { TestingModule } from '@nestjs/testing';
import { AssetCreationData, AssetService } from 'src/assets/asset.service';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Asset } from 'src/assets/domain/asset.entity';
import { Metric } from 'src/assets/domain/metric.entity';
import request from 'supertest';
import { AwaitedReturn } from './type-utilities';

export async function createAssetWithAPI(
  httpClient: request.SuperAgentTest,
  customerId: number,
  assetDto,
) {
  return await httpClient
    .post(`/v0/customers/${customerId}/assets`)
    .send(assetDto);
}

export async function createAsset(
  testingModule: TestingModule,
  assetCreationData: AssetCreationData,
  userId: number,
) {
  const assetService = testingModule.get(AssetService);
  const orm = await testingModule.resolve(MikroORM);

  return await RequestContext.createAsync(orm.em, async () => {
    return await assetService.create(assetCreationData, userId);
  });
}

export async function deleteAssetType(
  testingModule: TestingModule,
  name: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(AssetType, {
      lowerCaseName: name.toLowerCase(),
    });
  });
}

export async function deleteMetric(testingModule: TestingModule, name: string) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(Metric, {
      name,
    });
  });
}

export async function deleteAssetByTag(
  testingModule: TestingModule,
  tag: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(
      Asset,
      {
        tag,
      },
      { filters: false },
    );
  });
}

export const createAssetType = async (
  testingModule: TestingModule,
  name: string,
) => {
  const orm = await testingModule.resolve(MikroORM);
  return await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();

    if (!em) {
      throw new Error('No entity manager available');
    }

    const newAssetType = new AssetType();
    newAssetType.name = name;
    await em.persistAndFlush(newAssetType);
    return newAssetType;
  });
};

export const createMetric = async (
  testingModule: TestingModule,
  name: string,
  assetType: AssetType,
) => {
  const orm = await testingModule.resolve(MikroORM);
  return await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();

    if (!em) {
      throw new Error('No entity manager available');
    }

    const newMetric = em.create(Metric, { name, assetType });
    await em.persistAndFlush(newMetric);
    return newMetric;
  });
};

export type OfficeSiteFixture = AwaitedReturn<
  typeof assetFixtureFactory.createOfficeSite
>;

export type EnginesFixture = AwaitedReturn<
  typeof assetFixtureFactory.createEngines
>;

export const assetFixtureFactory = {
  createOfficeSite: async (
    testingModule: TestingModule,
    createdById: number,
    customerId: number,
  ) => {
    const locationAssetTypeId = (
      await createAssetType(testingModule, 'Location')
    ).id;

    const fordBuildingAssetId = (
      await createAsset(
        testingModule,
        {
          tag: 'Ford Main Building',
          assetTypeId: locationAssetTypeId,
          parentIds: [],
          customerId,
        },
        createdById,
      )
    ).id;

    const officeAssetId = (
      await createAsset(
        testingModule,
        {
          tag: 'Office',
          assetTypeId: locationAssetTypeId,
          parentIds: [fordBuildingAssetId],
          customerId,
        },
        createdById,
      )
    ).id;

    return {
      fordBuildingAssetId,
      officeAssetId,
      cleanUp: async () => {
        await deleteAssetByTag(testingModule, 'Office');
        await deleteAssetByTag(testingModule, 'Ford Main Building');
        await deleteAssetType(testingModule, 'Location');
      },
    };
  },
  createEngineAssetType: async (testingModule: TestingModule) => {
    const engineAssetTypeId = (await createAssetType(testingModule, 'Engine'))
      .id;

    return {
      engineAssetTypeId,
      cleanUp: async () => {
        await deleteAssetType(testingModule, 'Engine');
      },
    };
  },
  createEnginePowerMetric: async (testingModule: TestingModule) => {
    const engineAssetType = await createAssetType(testingModule, 'Engine');

    const powerMetric = await createMetric(
      testingModule,
      'Power',
      engineAssetType,
    );

    return {
      powerMetricId: powerMetric.id,
      engineAssetTypeId: engineAssetType.id,
      cleanUp: async () => {
        await deleteMetric(testingModule, 'Power');
        await deleteAssetType(testingModule, 'Engine');
      },
    };
  },
  createEngines: async (
    testingModule: TestingModule,
    createdById: number,
    customerId: number,
  ) => {
    const engineAssetTypeFixture =
      await assetFixtureFactory.createEngineAssetType(testingModule);

    const bikeEngineAssetId = (
      await createAsset(
        testingModule,
        {
          tag: 'Bike Engine',
          assetTypeId: engineAssetTypeFixture.engineAssetTypeId,
          parentIds: [],
          customerId,
        },
        createdById,
      )
    ).id;

    const carEngineAssetId = (
      await createAsset(
        testingModule,
        {
          tag: 'Car Engine',
          assetTypeId: engineAssetTypeFixture.engineAssetTypeId,
          parentIds: [],
          customerId,
        },
        createdById,
      )
    ).id;

    return {
      bikeEngineAssetId,
      carEngineAssetId,
      cleanUp: async () => {
        await deleteAssetByTag(testingModule, 'Bike Engine');
        await deleteAssetByTag(testingModule, 'Car Engine');
        await engineAssetTypeFixture.cleanUp();
      },
    };
  },
};
