import { EntityRepository } from '@mikro-orm/core';
import { InjectRepository } from '@mikro-orm/nestjs';
import { Injectable } from '@nestjs/common';
import { UnitsGroup } from '../domain/units-group.entity';

@Injectable()
export class UnitsGroupRepository {
  constructor(
    @InjectRepository(UnitsGroup)
    private readonly entityRepository: EntityRepository<UnitsGroup>,
  ) {}

  async getAll() {
    return await this.entityRepository.find({ isBase: false });
  }
}
