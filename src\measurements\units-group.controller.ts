import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from "@nestjs/common";
import { ApiOkResponse, ApiQuery } from "@nestjs/swagger";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import {
  CollectionDto,
  collectionSchema,
} from "src/serialization/collection.dto";
import { UnitsGroupDto, UnitsGroupUnitDto } from "./dto/units-group.dto";
import { UnitsGroupService } from "./units-group.service";

@Controller({ version: "0", path: "measurements-backoffice/units-groups" })
@UseGuards(JwtAuthGuard, CsrfGuard)
export class UnitsGroupApiController {
  constructor(private readonly unitsGroupService: UnitsGroupService) {}

  @ApiOkResponse(collectionSchema(UnitsGroupDto))
  @Get()
  async getAll(): Promise<CollectionDto<UnitsGroupDto>> {
    const items = await this.unitsGroupService.getAll();

    return {
      items: items.map((unitsGroup) => ({
        id: unitsGroup.id,
        name: unitsGroup.name,
      })),
      total: items.length,
    };
  }

  @ApiOkResponse(collectionSchema(UnitsGroupUnitDto))
  @ApiQuery({ name: "measurementTypeId", required: false })
  @Get(":unitsGroupId/units-of-measure")
  async getAllGroupUnits(
    @Param("unitsGroupId") unitsGroupId: string,
    @Query("measurementTypeId") measurementTypeId?: string
  ): Promise<CollectionDto<UnitsGroupUnitDto>> {
    const groupUnits = await this.unitsGroupService.getGroupUnits(
      Number(unitsGroupId),
      measurementTypeId ? { measurementTypeId: Number(measurementTypeId) } : {}
    );

    return {
      items: groupUnits.map((groupUnit) => {
        const { isMeasurementTypeDefault, id, name, measurementType } =
          groupUnit;
        return {
          id,
          name,
          measurement_type_id: measurementType.id,
          is_measurement_type_default: isMeasurementTypeDefault,
        };
      }),
      total: groupUnits.length,
    };
  }

  @Get("/unit-of-measures")
  async getUnitOfMeasures() {
    const items = await this.unitsGroupService.getUnitOfMeasures();
    return {
      total: items.length,
      items: items,
    };
  }

  @Post("/:measurementTypeId")
  @ApiQuery({ name: "measurementTypeId", required: true })
  async createUnitOfMeasure(
    @Param("measurementTypeId") measurementTypeId: number,
    @Body() body: { name: string; measurementTypeId: number }
  ) {
    return this.unitsGroupService.createUnitOfMeasure(
      body.name,
      measurementTypeId
    );
  }

  @Put("/:unitOfMeasureId")
  async updateUnitOfMeasure(
    @Param("unitOfMeasureId") unitOfMeasureId: number,
    @Body() body: { name: string; measurementTypeId: number }
  ) {
    return this.unitsGroupService.updateUnitOfMeasure(
      body.name,
      body.measurementTypeId,
      Number(unitOfMeasureId)
    );
  }

  @Get("units-groups-units")
  async getUnitGroupUnit() {
    return this.unitsGroupService.getUnitGroupsUnits();
  }

  @Post("/units-group-unit/create")
  async createUnitGroupUnit(
    @Body()
    body: {
      measurementTypeId: number;
      unitsGroupId: number;
      unitOfMeasureId: number;
      is_default: boolean;
    }
  ) {
    return this.unitsGroupService.createUnitGroupUnit(
      body.unitsGroupId,
      body.unitOfMeasureId,
      body.measurementTypeId,
      body.is_default
    );
  }

  @Put("units-group-unit/:unitsofUnitId")
  async updateUnitGroupUnit(
    @Body()
    body: {
      measurementTypeId: number;
      unitsGroupId: number;
      unitOfMeasureId: number;
      is_default: boolean;
    },
    @Param("unitsofUnitId", ParseIntPipe) unitsofUnitId: number
  ) {
    return this.unitsGroupService.updateUnitGroupUnit(
      body.unitsGroupId,
      body.unitOfMeasureId,
      body.measurementTypeId,
      unitsofUnitId,
      body.is_default
    );
  }

  @Put("units-group-unit/:unitsofUnitId")
  async addUnitGroupUnit(
    @Body()
    body: {
      measurementTypeId: number;
      unitsGroupId: number;
      unitOfMeasureId: number;
    },
    @Param("unitsofUnitId") unitsofUnitId: string
  ) {
    return this.unitsGroupService.addUnitGroupUnit(
      body.unitsGroupId,
      body.unitOfMeasureId,
      body.measurementTypeId,
      unitsofUnitId
    );
  }
}
