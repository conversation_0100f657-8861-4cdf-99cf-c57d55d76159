import { Migration } from "@mikro-orm/migrations";

export class Migration20250604151745 extends Migration {
  async up(): Promise<void> {
    // Get all dashboard records
    const dashboards = await this.execute(
      "SELECT id, data FROM \"dashboard\" WHERE data IS NOT NULL AND data != ''"
    );

    console.log(
      `Processing ${dashboards.length} dashboard records for responsive layout migration...`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const dashboard of dashboards) {
      try {
        // Parse the existing JSON data
        const dashboardData = JSON.parse(dashboard.data);

        // Check if this dashboard already has responsive layouts (skip if already migrated)
        if (
          dashboardData.responsiveLayouts ||
          dashboardData.desktopMobile !== undefined
        ) {
          console.log(
            `Dashboard ${dashboard.id} already has responsive layouts, skipping...`
          );
          continue;
        }

        // Ensure the widget property exists
        if (!dashboardData.widget) {
          dashboardData.widget = {
            widgets: [],
            widgetLayout: [],
          };
        }

        // Get the current widgetLayout (fallback to empty array if not exists)
        const currentWidgetLayout = dashboardData.widget.widgetLayout || [];

        // Add the new responsive layout structure
        dashboardData.desktopMobile = 0; // Default to desktop mode
        dashboardData.responsiveLayouts = {
          desktop: {
            widgetLayout: [...currentWidgetLayout], // Copy current layout as desktop layout
            widgets: [...(dashboardData.widget.widgets || [])],
          },
          mobile: {
            widgetLayout: [...currentWidgetLayout], // Copy current layout as mobile layout (same initially)
            widgets: [...(dashboardData.widget.widgets || [])],
          },
        };

        // Keep the existing widgetLayout for backward compatibility
        // dashboardData.widget.widgetLayout remains unchanged

        // Convert back to JSON string
        const updatedData = JSON.stringify(dashboardData);

        // Update the dashboard record
        await this.execute('UPDATE "dashboard" SET data = ? WHERE id = ?', [
          updatedData,
          dashboard.id,
        ]);

        successCount++;
        console.log(`✓ Successfully migrated dashboard ${dashboard.id}`);
      } catch (error) {
        errorCount++;
        console.error(`✗ Error migrating dashboard ${dashboard.id}:`, error);

        // Log the problematic data for debugging (first 200 chars)
        const dataPreview = dashboard.data
          ? dashboard.data.substring(0, 200) + "..."
          : "null";
        console.error(`  Data preview: ${dataPreview}`);
      }
    }

    console.log(
      `Migration completed: ${successCount} successful, ${errorCount} errors`
    );

    if (errorCount > 0) {
      console.warn(
        `Warning: ${errorCount} dashboards failed to migrate. Please check the logs above.`
      );
    }
  }

  async down(): Promise<void> {
    // Get all dashboard records
    const dashboards = await this.execute(
      "SELECT id, data FROM \"dashboard\" WHERE data IS NOT NULL AND data != ''"
    );

    console.log(
      `Reverting responsive layout migration for ${dashboards.length} dashboard records...`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const dashboard of dashboards) {
      try {
        // Parse the existing JSON data
        const dashboardData = JSON.parse(dashboard.data);

        // Check if this dashboard has responsive layouts to remove
        if (
          !dashboardData.responsiveLayouts &&
          dashboardData.desktopMobile === undefined
        ) {
          console.log(
            `Dashboard ${dashboard.id} doesn't have responsive layouts, skipping...`
          );
          continue;
        }

        // Remove the responsive layout properties
        delete dashboardData.desktopMobile;
        delete dashboardData.responsiveLayouts;

        // The original widgetLayout should still be intact, so no need to restore it

        // Convert back to JSON string
        const updatedData = JSON.stringify(dashboardData);

        // Update the dashboard record
        await this.execute('UPDATE "dashboard" SET data = ? WHERE id = ?', [
          updatedData,
          dashboard.id,
        ]);

        successCount++;
        console.log(`✓ Successfully reverted dashboard ${dashboard.id}`);
      } catch (error) {
        errorCount++;
        console.error(`✗ Error reverting dashboard ${dashboard.id}:`, error);
      }
    }

    console.log(
      `Migration rollback completed: ${successCount} successful, ${errorCount} errors`
    );
  }
}
