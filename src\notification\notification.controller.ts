import { CreateRequestContext, MikroORM } from "@mikro-orm/core";
import {
  <PERSON>,
  Logger,
  OnModuleD<PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as amqp from "amqplib";
import { NotificationDTO } from "./notification.dto";
import { NotificationService } from "./notification.service";

@Controller("notification")
export class NotificationController implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NotificationController.name);
  private channel: amqp.Channel;
  private connection: amqp.Connection;
  private readonly topicExchange = "notifications_topic_exchange";
  private rabbitMQConfig: {
    brokers: string;
    topicAlert: string;
    clientId: string;
    topicNotification: string;
    kafkaGroupId: string;
    kafkaGroupIdInstance: string;
    frontendUrl: string;
    username: string;
    password: string;
    alertQueueName: string;
    notificationQueueName: string;
  };
  constructor(
    private readonly configService: ConfigService,
    private readonly orm: MikroORM,
    private readonly notificationService: NotificationService
  ) {
    const {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
      username,
      password,
      alertQueueName,
      notificationQueueName,
    } = configService.get("rabbitMQ");
    this.rabbitMQConfig = {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
      username,
      password,
      alertQueueName,
      notificationQueueName,
    };
    this.logger.log("RabbitMQ initalized");
  }
  @CreateRequestContext()
  async onModuleInit() {
    this.logger.log("Connecting to RabbitMQ...");
    // this.connection = await amqp.connect("amqp://localhost:5672");
    const { username, password, brokers } = this.rabbitMQConfig;
    const connectionUrl = `amqp://${username}:${password}@${brokers}`;
    this.connection = await amqp.connect(connectionUrl);
    this.channel = await this.connection.createChannel();
    await this.channel.assertExchange(this.topicExchange, "topic", {
      durable: true,
    });

    await this.channel.assertQueue(this.rabbitMQConfig.notificationQueueName, {
      durable: true,
    });
    await this.channel.bindQueue(
      this.rabbitMQConfig.notificationQueueName,
      this.topicExchange,
      "notification.*"
    );
    await this.startConsumer();
    this.logger.log("RabbitMQ NotificationController ready.");
  }

  async onModuleDestroy() {
    await this.channel.close();
    await this.connection.close();
    this.logger.log("RabbitMQ connection closed.");
  }

  async sendMessage(routingKey: string, payload: any) {
    const message = Buffer.from(JSON.stringify(payload));
    this.channel.publish(this.topicExchange, routingKey, message);
    this.logger.log(
      `Message published to RabbitMQ with routingKey=${routingKey}`
    );
  }

  private async startConsumer() {
    this.channel.consume(
      this.rabbitMQConfig.notificationQueueName,
      async (msg) => {
        if (!msg) {
          this.logger.warn("❌ Received null message");
          return;
        }

        try {
          this.logger.log("Received message from RabbitMQ");
          const parsed: NotificationDTO = JSON.parse(msg.content.toString());
          this.logger.log(parsed);

          if (parsed.notifyType === "sms") {
            const error = await this.notificationService.sendSMS(
              parsed.to,
              parsed.message
            );
            if (error.hasError) {
              this.logger.error("Error sending SMS", error.message);
              this.sendMessage("notification.dlq", {
                errorMessage: error.message,
                messageValue: parsed,
              });
            }
          } else if (parsed.notifyType === "email") {
            const error = await this.notificationService.sendEmail(
              [parsed.to],
              parsed.subject,
              parsed.message
            );
            if (error.hasError) {
              this.logger.error(`Error sending Emails ${error.message}`);
              this.sendMessage("notification.dlq", {
                errorMessage: error.message,
                messageValue: parsed,
              });
            }
          }
        } catch (err) {
          this.logger.error("Failed to process message", err);
        } finally {
          this.channel.ack(msg);
        }
      }
    );
  }

  async sendSMS(to: string, message: string): Promise<void> {
    await this.notificationService.sendSMS(to, message);
  }

  async sendEmail(to: string[], subject: string, body: string): Promise<void> {
    await this.notificationService.sendEmail(to, subject, body);
  }
}
