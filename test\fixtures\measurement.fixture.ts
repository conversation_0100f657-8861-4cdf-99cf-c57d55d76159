import { MikroORM, RequestContext } from '@mikro-orm/core';
import { TestingModule } from '@nestjs/testing';
import { AssetMeasurementService } from 'src/measurements/asset-measurement.service';
import { DataType } from 'src/measurements/domain/data-type.entity';
import { Location } from 'src/measurements/domain/location.entity';
import { MeasurementType } from 'src/measurements/domain/measurement-type.entity';
import {
  AssetMeasurementCreationData,
  MeasurementBackofficeType,
} from 'src/measurements/domain/types';
import { ValueType } from 'src/measurements/domain/value-type.entity';
import { Measurement } from 'src/measurements/domain/measurement.entity';

export async function createAssetMeasurement(
  testingModule: TestingModule,
  creationParams: AssetMeasurementCreationData,
  customerId: number,
  userId: number,
) {
  const orm = await testingModule.resolve(MikroORM);
  const assetMeasurementService = testingModule.get(AssetMeasurementService);

  return await RequestContext.createAsync(orm.em, async () => {
    return await assetMeasurementService.create(
      creationParams,
      customerId,
      userId,
    );
  });
}

export const createMetadataType = async (
  testingModule: TestingModule,
  name: string,
  type: MeasurementBackofficeType,
) => {
  const orm = await testingModule.resolve(MikroORM);
  const metadataTypeId = await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();

    if (!em) {
      throw new Error('No entity manager available');
    }

    let newMetadataType;
    if (type === 'measurement') {
      newMetadataType = em.create(MeasurementType, { name });
    } else if (type === 'data') {
      newMetadataType = em.create(DataType, { name });
    } else if (type === 'value') {
      newMetadataType = em.create(ValueType, { name });
    } else if (type === 'location') {
      newMetadataType = em.create(Location, { name });
    }
    await em.persistAndFlush(newMetadataType);
    return newMetadataType.id;
  });
  return metadataTypeId;
};

export async function deleteMetadataType(
  testingModule: TestingModule,
  name: string,
  type: MeasurementBackofficeType,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    let entityName;
    if (type === 'measurement') {
      entityName = MeasurementType;
    } else if (type === 'data') {
      entityName = DataType;
    } else if (type === 'value') {
      entityName = ValueType;
    } else if (type === 'location') {
      entityName = Location;
    }
    await RequestContext.getEntityManager()?.nativeDelete(entityName, {
      name,
    });
  });
}

export async function deleteMeasurementByTag(
  testingModule: TestingModule,
  tag: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(Measurement, {
      tag,
    });
  });
}

export const measurementFixtureFactory = {
  createMetadata: async (testingModule: TestingModule) => {
    const voltageTypeId = await createMetadataType(
      testingModule,
      'Voltage',
      'measurement',
    );
    const chargeTypeId = await createMetadataType(
      testingModule,
      'Charge',
      'measurement',
    );
    const powerTypeId = await createMetadataType(
      testingModule,
      'Power',
      'measurement',
    );
    const realDataTypeId = await createMetadataType(
      testingModule,
      'REAL',
      'data',
    );
    const stringDataTypeId = await createMetadataType(
      testingModule,
      'STRING',
      'data',
    );
    const nominalValueTypeId = await createMetadataType(
      testingModule,
      'nominal',
      'value',
    );
    const outputLocationId = await createMetadataType(
      testingModule,
      'Output',
      'location',
    );
    return {
      voltageTypeId,
      chargeTypeId,
      powerTypeId,
      realDataTypeId,
      stringDataTypeId,
      nominalValueTypeId,
      outputLocationId,
      cleanUp: async () => {
        await deleteMetadataType(testingModule, 'Output', 'location');
        await deleteMetadataType(testingModule, 'nominal', 'value');
        await deleteMetadataType(testingModule, 'REAL', 'data');
        await deleteMetadataType(testingModule, 'STRING', 'data');
        await deleteMetadataType(testingModule, 'Charge', 'measurement');
        await deleteMetadataType(testingModule, 'Power', 'measurement');
        await deleteMetadataType(testingModule, 'Voltage', 'measurement');
      },
    };
  },
};
