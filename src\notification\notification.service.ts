import { SendEmailCommand, SESClient } from "@aws-sdk/client-ses";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import AWS, { SES } from "aws-sdk";
import { <PERSON>wi<PERSON> } from "twilio";
@Injectable()
export class NotificationService {
  private aws: SES;
  private sesClient = new SESClient({ region: "us-east-1" });
  private client: Twilio;
  private twillioConfig: {
    send: boolean;
    account_sid: string;
    auth_token: string;
    email_api_token: string;
    email_from: string;
    sms_from_no: string;
  } = {
    send: false,
    account_sid: undefined,
    auth_token: undefined,
    email_api_token: undefined,
    email_from: undefined,
    sms_from_no: undefined,
  };
  public constructor(
    private configService: ConfigService,
    private logger: Logger
  ) {
    const notificationConfig = this.configService.get("notification");
    const SES_CONFIG = {
      accessKeyId: notificationConfig["access_key_id"],
      secretAccessKey: notificationConfig["secret_access_key"],
      region: notificationConfig["region"],
    };
    this.aws = new AWS.SES(SES_CONFIG);
    this.twillioConfig = {
      send: notificationConfig.send,
      account_sid: notificationConfig.account_sid,
      auth_token: notificationConfig.auth_token,
      email_api_token: notificationConfig.email_api_token,
      email_from: notificationConfig.email_from,
      sms_from_no: notificationConfig.sms_from_no,
    };
    this.client = new Twilio(
      this.twillioConfig.account_sid,
      this.twillioConfig.auth_token
    );
  }

  async sendSMS(to: string, message: string) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    if (this.twillioConfig.send) {
      try {
        const msg = await this.client.messages.create({
          body: message || this.getSampleMessage(message),
          from: this.twillioConfig.sms_from_no,
          to: to,
        });
      } catch (e: any) {
        this.logger.error("error send sms", e.message);
        error.hasError = true;
        error.message = e.message;
      }
    }
    this.logger.log("SMS sent to: ", to, " with message: ", message);
    return error;
  }
  getSampleMessage(message: string): string {
    return `Limit Alert
    At: 2023-10-05T14:30:00 UTC
    Asset: AssetTag123
    Measurement: MeasurementTag456
    CurrentValue: 75.0
    Settings:
    Threshold: GE 70.0
    Return to Normal: LE 65.0`;
  }

  async sendEmail(to: string[], subject: string, body: string) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    if (this.twillioConfig.send) {
      try {
        // const sendEmailCommands = new SendEmailCommand({
        //   Destination: {
        //     ToAddresses: to,
        //   },
        //   Message: {
        //     Body: {
        //       Html: {
        //         Charset: "UTF-8",
        //         Data: body || this.getSampleMessage(body),
        //       },
        //       Text: {
        //         Charset: "UTF-8",
        //         Data: body || this.getSampleMessage(body),
        //       },
        //     },
        //     Subject: {
        //       Charset: "UTF-8",
        //       Data: subject,
        //     },
        //   },
        //   Source: this.twillioConfig.email_from,
        // });
        // await this.sesClient.send(sendEmailCommands);
        const params: AWS.SES.SendEmailRequest = {
          Destination: {
            ToAddresses: to,
          },
          Message: {
            Body: {
              Html: {
                Charset: "UTF-8",
                Data: body || this.getSampleMessage(body),
              },
              Text: {
                Charset: "UTF-8",
                Data: body || this.getSampleMessage(body),
              },
            },
            Subject: {
              Charset: "UTF-8",
              Data: subject,
            },
          },
          Source: this.twillioConfig.email_from,
        };

        await this.aws.sendEmail(params).promise();
      } catch (e: any) {
        this.logger.error("error send email", e.message);
        error.hasError = true;
        error.message = e.message;
      }
    }
    this.logger.log(
      `Email sent to:  ${to} with subject:  ${subject}, and body: ${body}`
    );
    return error;
  }

  async sendForgotPasswordEmail(
    to: string[],
    subject: string,
    body: string,
    htmlBody: string
  ) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    if (this.twillioConfig.send) {
      try {
        const params: AWS.SES.SendEmailRequest = {
          Destination: {
            ToAddresses: to,
          },
          Message: {
            Body: {
              Html: {
                Charset: "UTF-8",
                Data: htmlBody || this.getSampleMessage(body),
              },
              Text: {
                Charset: "UTF-8",
                Data: body || this.getSampleMessage(body),
              },
            },
            Subject: {
              Charset: "UTF-8",
              Data: subject,
            },
          },
          Source: this.twillioConfig.email_from,
        };
        await this.aws.sendEmail(params).promise();
      } catch (e: any) {
        this.logger.error("error send email", e.message);
        error.hasError = true;
        error.message = e.message;
      }
    }
    this.logger.log(
      "Email sent to: ",
      to,
      " with subject: ",
      subject,
      " and body: ",
      body
    );
    return error;
  }

  async connect() {
    this.logger.log("connected");
  }

  async close() {
    this.logger.log("closed");
  }
}
