import { EntityRepository } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { Annotation } from "./domain/annotation.entity";
import { AnnotationRepository } from "./repository/annotation.repository";
import { DashboardService } from "src/dashboards/dashboard.service";

@Injectable()
export class AnnotationsService {
  constructor(
    private readonly annotationRepository: AnnotationRepository,
    @InjectRepository(Measurement)
    private measurement: EntityRepository<Measurement>,
    private readonly dashboardService: DashboardService
  ) {}

  async getAnnotation({
    dashboard_id,
    measureId,
    widget_id,
    endTime,
    startTime,
  }: {
    measureId: number;
    dashboard_id: number;
    widget_id: number;
    startTime: number;
    endTime: number;
  }) {
    return await this.annotationRepository.getAnnotation({
      dashboard_id,
      widget_id,
      measureId: measureId,
      startTime,
      endTime,
    });
  }

  async createAnnotation({
    dashboard_id,
    description,
    measurement_id,
    settings,
    time_of_annotation,
    value,
    widget_id,
  }: {
    dashboard_id: number;
    measurement_id: number;
    widget_id: number;
    description: string;
    time_of_annotation: number;
    value: number;
    settings: string;
  }) {
    const newAnnotation = new Annotation();
    const findMeasurement = await this.measurement.findOne({
      id: measurement_id,
    });
    if (!findMeasurement) {
      throw new Error("Measurement not found");
    }
    const findDashboard = await this.dashboardService.findById(dashboard_id);
    if (!findDashboard) {
      throw new Error("Dashboard not found");
    }
    newAnnotation.dashboard = findDashboard;
    newAnnotation.measurement_id = findMeasurement;
    newAnnotation.widget_id = widget_id;
    newAnnotation.description = description;
    newAnnotation.settings = settings;
    newAnnotation.time_of_annotation = time_of_annotation;
    newAnnotation.value = value;
    return await this.annotationRepository.createAnnotation(newAnnotation);
  }

  async updateAnnotation({
    id,
    dashboard_id,
    description,
    measurement_id,
    settings,
    time_of_annotation,
    value,
    widget_id,
  }: {
    id: number; // annotation id
    dashboard_id: number;
    widget_id: number;
    measurement_id: number;
    description: string;
    time_of_annotation: number;
    value: number;
    settings: string;
  }) {
    const annotation = await this.annotationRepository.findAnnotation({
      id,
      dashboard: { id: dashboard_id },
      measurement_id: { id: measurement_id },
    });

    if (!annotation) {
      throw new Error("Annotation not found");
    }

    const findMeasurement = await this.measurement.findOne({
      id: measurement_id,
    });
    if (!findMeasurement) {
      throw new Error("Measurement not found");
    }

    annotation.description = description;
    annotation.measurement_id = findMeasurement;
    annotation.settings = settings;
    annotation.time_of_annotation = time_of_annotation;
    annotation.value = value;
    annotation.widget_id = widget_id;

    await this.annotationRepository.updateAnnotation(annotation);
  }
}
