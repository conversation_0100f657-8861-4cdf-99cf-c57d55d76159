import { Migration } from '@mikro-orm/migrations';

export class Migration20240102154507 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "measurement" add column "metric_id" int null;');
    this.addSql(
      'alter table "measurement" add constraint "measurement_metric_id_foreign" foreign key ("metric_id") references "metric" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "measurement" drop constraint "measurement_metric_id_foreign";',
    );

    this.addSql('alter table "measurement" drop column "metric_id";');
  }
}
