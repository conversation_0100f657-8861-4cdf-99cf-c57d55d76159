import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from "@nestjs/common";
import { ApiNoContentResponse, ApiOkResponse } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole, Roles } from "src/authorization/infra/roles.decorator";
import { User, UserCreationParams } from "./domain/user.entity";
import {
  ResetPasswordDto,
  SelfUserUpdateDto,
  UserCreationDto,
  UserDto,
  UserFilterDto,
  UserFilters,
} from "./dto/user.dto";
import { UserMapper } from "./dto/user.mapper";
import { UserPreferencesDto } from "./dto/user_perference.dto";
import { UserService } from "./user.service";
import { RolesGuard } from "src/authorization/infra/roles.guard";

@Controller({ version: "0", path: "users" })
export class UserApiController {
  constructor(
    private readonly userService: UserService,
    private readonly userDtoMapper: UserMapper
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @Roles(Role.ADMIN)
  @ApiOkResponse({
    description: "The user records",
    type: UserDto,
    isArray: true,
  })
  async getAllUsers(@Query() query: UserFilterDto) {
    const filters: UserFilters = {
      customerName: query.customer_name ?? "",
      customerId: query.customer_id ? query.customer_id.toString() : null,
      userName: query.user_name ?? "",
      userEmail: query.email ?? "",
      role: query.role ?? "",
    };
    const users = await this.userService.getAllUsers(filters);
    return {
      total: users.length,
      items: users,
    };
  }

  @Get("/preference")
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async getUserPreference(@AuthUser() authUser: User) {
    return await this.userService.getUserPreference(authUser.id);
  }

  @Post("/preference")
  @HttpCode(204)
  @UseGuards(JwtAuthGuard, CsrfGuard)
  @UsePipes(new ValidationPipe())
  async createUpdateUserPreference(
    @AuthUser() authUser: User,
    @Body() body: UserPreferencesDto
  ) {
    await this.userService.createUpdateUserPreference(
      authUser.id,
      body.preferences
    );
  }

  @Put("/reset-password")
  @HttpCode(204)
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async resetPassword(
    @AuthUser() authUser: User,
    @Body() body: ResetPasswordDto
  ) {
    await this.userService.resetPassword(authUser.id, body);
  }

  @ApiOkResponse({ type: UserDto })
  @Get("/me")
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async getMe(@AuthUser() user: User): Promise<UserDto> {
    return this.userDtoMapper.toDto(user);
  }

  @ApiNoContentResponse()
  @HttpCode(204)
  @Patch("/me")
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async patchMe(
    @AuthUser() authUser: User,
    @Body() userUpdate: SelfUserUpdateDto
  ) {
    await this.userService.update(
      Number(authUser.id),
      {
        ...userUpdate,
        firstName: userUpdate.first_name,
        lastName: userUpdate.last_name,
      },
      authUser.id
    );
  }

  @ApiOkResponse({ type: UserDto })
  @Get(":id")
  @UseGuards(JwtAuthGuard, CsrfGuard)
  async getById(@Param("id") id: number): Promise<UserDto> {
    const user = await this.userService.findById(id);

    if (user === null) {
      throw new NotFoundException();
    }

    return this.userDtoMapper.toDto(user);
  }

  @ApiOkResponse({ type: UserDto })
  @HttpCode(204)
  @Patch(":id")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.ADMIN)
  async patchById(
    @AuthUser() authUser: User,
    @Param("id") id: number,
    @Body() userUpdate: UserDto
  ) {
    const user = await this.userService.findById(id);

    if (user === null) {
      throw new NotFoundException();
    }
    await this.userService.update(
      id,
      {
        ...userUpdate,
        firstName: userUpdate.first_name,
        lastName: userUpdate.last_name,
        globalRole: userUpdate.global_role
          ? Role[userUpdate.global_role]
          : undefined,
        scopedRoles: userUpdate.scoped_roles?.map((scopedRole) => ({
          role: Role[scopedRole.role],
          customerIds: scopedRole.customer_ids,
        })),
        phone_no: userUpdate.phone_number,
        country_code: userUpdate.country_code,
      },
      authUser.id
    );
  }

  @ApiOkResponse({ type: UserDto })
  @Post()
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.ADMIN)
  async create(
    @AuthUser() authUser: User,
    @Body() newUser: UserCreationDto
  ): Promise<UserDto> {
    const newUserParams = this.userDtoMapper.creationDtoToParams(newUser);

    if (!UserApiController.canCreateUser(authUser, newUserParams)) {
      throw new ForbiddenException();
    }

    const createdUser = await this.userService.create(
      newUserParams,
      authUser.id
    );

    return this.userDtoMapper.toDto(createdUser);
  }

  static canCreateUser = (
    actionUser: User,
    newUserRole: UserCreationParams
  ) => {
    return (
      actionUser.globalRole === Role.ADMIN ||
      (newUserRole.globalRole === undefined &&
        newUserRole.scopedRoles &&
        newUserRole.scopedRoles.every((newScopedRole) =>
          newScopedRole.customerIds.every((customerId) =>
            actionUser.hasCustomerScopeWithRole(customerId, Role.ADMIN)
          )
        ))
    );
  };
}
