import { Controller, Post, Body } from '@nestjs/common';
import { KafkaService } from './kafka.service';

@Controller('kafka')
export class KafkaController {
    constructor(private readonly kafkaService: KafkaService) {}

    @Post('send')
    async sendMessage(@Body() body: { key: string; value: string }) {
        const { key, value } = body;
        await this.kafkaService.sendMessage('be-alert-dev-new', key, value);
        return { success: true };
    }
}