import {
  <PERSON><PERSON><PERSON>,
  Filter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ey,
  Property,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { User } from 'src/users/domain/user.entity';

export type CustomerId = number;

@Entity()
@Filter({
  name: 'isEnabled',
  cond: {
    $or: [{ enabled: true }, { enabled: null }],
  },
  default: true,
})
export class Customer {
  @PrimaryKey()
  id!: CustomerId;

  @Unique()
  @Property({ length: 50, serializedName: 'name_id' })
  nameId!: string;

  @Property({ length: 50 })
  name!: string;

  @Property({ length: 150 })
  address!: string;

  @Property({ hidden: true, nullable: true })
  enabled?: boolean = true;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;

  @Property({ type: 'text', lazy: true, nullable: true})
  logo?: string;

}
