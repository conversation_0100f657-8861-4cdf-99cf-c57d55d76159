import { Migration } from '@mikro-orm/migrations';

export class Migration20230802192323 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "measurement" rename column "deleted" to "deleted_at";',
    );
    this.addSql(
      'alter table "measurement" rename column "deletedby" to "deleted_by";',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "measurement" drop constraint "measurement_deleted_by_foreign";',
    );

    this.addSql(
      'alter table "measurement" rename column "deleted_at" to "deleted";',
    );
    this.addSql(
      'alter table "measurement" rename column "deleted_by" to "deletedby";',
    );
  }
}
