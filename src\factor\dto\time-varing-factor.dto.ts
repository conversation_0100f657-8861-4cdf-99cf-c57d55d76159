import { ApiProperty } from "@nestjs/swagger";
import { IsNumber } from "class-validator";
import { User } from "../../users/domain/user.entity";
import { FactorSchedule } from "../domain/factor-schedule.entity";
import { FactorTimeOfDayValue } from "../domain/factor-time-of-day-value.entity";
import { TimeVaryingFactor } from "../domain/TimeVaryingFactor.entity";
import { FactorScheduleDTO } from "./factor-schedule.dto";
import { FactorTimeOfDayValueDTO } from "./factor-time-of-day-value.dto";
export class TimeVaryingFactorDTO
  implements
    Pick<
      TimeVaryingFactor,
      "id" | "seasonal" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy"
    >
{
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty({ required: true })
  @IsNumber()
  measurement: number;

  @ApiProperty({ example: true, required: true })
  seasonal: boolean;

  @ApiProperty({ required: true })
  @IsNumber()
  factorType: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  createdBy: User;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  updatedBy: User;
}

export class TimeVaryingFactorCreateDTO {
  @ApiProperty({ required: true })
  timeVaryingFactor: TimeVaryingFactorDTO;

  @ApiProperty({ required: true })
  factorSchedule: FactorScheduleDTO[];

  @ApiProperty({ required: true })
  factorTimeOfDayValue: FactorTimeOfDayValueDTO[];
}
export class TimVaringDayValueDTO{
  @ApiProperty()
  effectiveDate: string

  @ApiProperty()
  factorTimeOfDayValue: FactorTimeOfDayValue[];
}
export class TimeVaryingFactorDetailsDTO
  implements
    Pick<
      TimeVaryingFactor,
      "id" | "seasonal" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy"
    >
{
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty({ required: true })
  @IsNumber()
  measurement: number;

  @ApiProperty({ example: true, required: true })
  seasonal: boolean;

  @ApiProperty({ required: true })
  @IsNumber()
  factorType: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  createdBy?: User;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  updatedBy?: User;

  @ApiProperty()
  factorSchedule: FactorSchedule[];

  @ApiProperty()
  factorTimeOfDayValue: TimVaringDayValueDTO[];
}
