import { Migration } from '@mikro-orm/migrations';

export class Migration20240102155912 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" add column "metric_id" int;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_metric_id_foreign" foreign key ("metric_id") references "metric" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_metric_id_unique" unique ("meter_template", "metric_id");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_metric_id_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_metric_id_unique";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop column "metric_id";',
    );
  }
}
