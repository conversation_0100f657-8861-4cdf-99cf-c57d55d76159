import { Body, Controller, Param, Post, UseGuards } from "@nestjs/common";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { AssetMeasurementService } from "./asset-measurement.service";
import { AssetWithMeasurementsDto } from "./dto/asset-measurement.dto";

@Controller({
  version: "0",
  path: "customers/:customerId/measurements",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetMeasurementsListController {
  constructor(
    private readonly assetMeasurementService: AssetMeasurementService
  ) {}

  @Post()
  @HasCustomerRole(Role.USER)
  async list(
    @Param("customerId") customerId: string,
    @Body()
    body: AssetWithMeasurementsDto[]
  ) {
    const assetMeasures =
      await this.assetMeasurementService.getAllByAssetIdsMeasureIds({
        customerId: Number(customerId),
        data: body.map((asset) => ({
          assetId: asset.asset_id,
          measurementId: asset.measurement_ids,
        })),
      });
    return {
      total: assetMeasures.length,
      items: assetMeasures,
    };
  }
}
