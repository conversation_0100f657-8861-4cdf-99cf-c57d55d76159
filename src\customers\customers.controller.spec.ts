import { Test } from '@nestjs/testing';
import { CustomersApiController } from './customers.controller';
import { testUserFactory } from 'src/users/__tests__/factories';
import {
  CustomerUserRole,
  Role,
} from 'src/authorization/domain/customer-user-role.entity';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { CustomerService } from './customer.service';
import { User } from 'src/users/domain/user.entity';
import { Customer } from './domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';

describe('CustomersApiController', () => {
  let customersApiController: CustomersApiController;
  let customerServiceMock: jest.Mocked<
    Pick<CustomerService, 'create' | 'getAll' | 'getAllById' | 'findByNameId'>
  >;
  let globalAdmin: User;
  let globalUser: User;
  let scopedUser: User;

  const newCustomerParams = {
    name_id: 'new_company',
    name: 'New Company',
    address: 'somewhere',
  };

  beforeEach(async () => {
    customerServiceMock = {
      create: jest.fn(),
      getAll: jest.fn(async () => []),
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getAllById: jest.fn(async (customerIds) => []),
      findByNameId: jest.fn(async (nameId) => {
        const customer = new Customer();
        customer.id = 42;
        customer.nameId = nameId;
        return customer;
      }),
    };

    const moduleRef = await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer, CustomerUserRole])],
      providers: [{ provide: CustomerService, useValue: customerServiceMock }],
      controllers: [CustomersApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    globalAdmin = testUserFactory.createGlobalScopeUser(
      'global_admin',
      Role.ADMIN,
    );
    globalUser = testUserFactory.createGlobalScopeUser(
      'global_user',
      Role.USER,
    );
    scopedUser = testUserFactory.createCustomerScopedUser('scoped_user', [
      { role: Role.USER, customerIds: [1, 2, 4] },
    ]);
    customersApiController = moduleRef.get(CustomersApiController);
  });

  describe('create', () => {
    test('customer with global user should throw an error', () => {
      expect(
        customersApiController.create(globalUser, newCustomerParams),
      ).rejects.toThrow('Cannot create customer with current role');
    });

    test('customer with scoped admin should throw an error', () => {
      const scopedAdminUser = testUserFactory.createCustomerScopedUser(
        'scoped_admin',
        [{ role: Role.ADMIN, customerIds: [3] }],
      );
      expect(
        customersApiController.create(scopedAdminUser, newCustomerParams),
      ).rejects.toThrow('Cannot create customer with current role');
    });

    test('customer with global admin should call customers service', async () => {
      await customersApiController.create(globalAdmin, newCustomerParams);

      expect(customerServiceMock.create.mock.calls.length).toBe(1);
    });
  });

  describe('getAll', () => {
    test('global admin should get all', async () => {
      await customersApiController.getAll(globalAdmin);

      expect(customerServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('global user should get all customers', async () => {
      await customersApiController.getAll(globalUser);

      expect(customerServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('scoped user should get all scope', async () => {
      await customersApiController.getAll(scopedUser);

      expect(customerServiceMock.getAll.mock.calls.length).toBe(0);
      expect(customerServiceMock.getAllById.mock.calls.length).toBe(1);
      expect(customerServiceMock.getAllById.mock.calls[0][0]).toStrictEqual([
        1, 2, 4,
      ]);
    });
  });

  describe('getByNameId', () => {
    test('global admin should get any customer by name id', async () => {
      const result = await customersApiController.getByNameId(
        globalAdmin,
        'customer',
      );

      expect(result.nameId).toBe('customer');
    });

    test('scoped user should get an exception getting a customer not in scope', () => {
      expect(() =>
        customersApiController.getByNameId(scopedUser, 'customer'),
      ).rejects.toThrow('Forbidden');
    });
  });
});
