import { ApiProperty, getSchemaPath } from '@nestjs/swagger';

export class CollectionDto<T> {
  constructor(items: T[]) {
    this.items = items;
    this.total = items.length;
  }

  @ApiProperty()
  items: T[];

  @ApiProperty()
  total: number;
}

// eslint-disable-next-line @typescript-eslint/ban-types
export const collectionSchema = (model: string | Function) => ({
  schema: {
    properties: {
      total: {
        type: 'number',
      },
      items: {
        type: 'array',
        items: { $ref: getSchemaPath(model) },
      },
    },
  },
});
