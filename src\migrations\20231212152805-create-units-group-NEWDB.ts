import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20231212152805 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "units_group" ("id" serial primary key, "name" varchar(50) not null);',
    );

    this.addSql(
      'create table "units_group_unit" ("id" serial primary key, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "m_type" int not null, "unit_of_measure" int not null, "units_group" int not null);',
    );

    this.addSql(
      'alter table "units_group_unit" add constraint "meas_type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "uom_fk" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "uom_g_fk" foreign key ("units_group") references "units_group" ("id") on update cascade;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "units_group_unit" drop constraint "uom_g_fk";');

    this.addSql('drop table if exists "units_group" cascade;');

    this.addSql('drop table if exists "units_group_unit" cascade;');
  }
}
