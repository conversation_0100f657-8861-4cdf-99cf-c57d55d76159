import { Migration } from '@mikro-orm/migrations';

export class Migration20240108154930 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "measurement" drop constraint "measurement_metric_id_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_metric_id_foreign";',
    );

    this.addSql(
      'alter table "measurement" rename column "metric_id" to "metric";',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_metric_foreign" foreign key ("metric") references "metric" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_metric_id_unique";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "metric_id" to "metric";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_metric_foreign" foreign key ("metric") references "metric" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_metric_unique" unique ("meter_template", "metric");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "measurement" drop constraint "measurement_metric_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_metric_foreign";',
    );

    this.addSql(
      'alter table "measurement" rename column "metric" to "metric_id";',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_metric_id_foreign" foreign key ("metric_id") references "metric" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_metric_unique";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "metric" to "metric_id";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_metric_id_foreign" foreign key ("metric_id") references "metric" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_metric_id_unique" unique ("meter_template", "metric_id");',
    );
  }
}
