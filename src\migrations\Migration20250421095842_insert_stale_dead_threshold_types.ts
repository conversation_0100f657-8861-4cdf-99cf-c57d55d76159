import { Migration } from "@mikro-orm/migrations";

export class Migration20250421095842_insert_stale_dead_threshold_types extends Migration {
  async up(): Promise<void> {
    this.addSql(`
      INSERT INTO alert_threshold_type (id, threshold)
      SELECT 3, 'STALE'
      WHERE NOT EXISTS (
        SELECT 1 FROM alert_threshold_type WHERE id = 3 OR threshold = 'STALE'
      );

      INSERT INTO alert_threshold_type (id, threshold)
      SELECT 4, 'DEAD'
      WHERE NOT EXISTS (
        SELECT 1 FROM alert_threshold_type WHERE id = 4 OR threshold = 'DEAD'
      );
    `);
  }

  async down(): Promise<void> {
    this.addSql(`
      DELETE FROM alert_threshold_type WHERE id IN (3, 4);
    `);
  }
}
