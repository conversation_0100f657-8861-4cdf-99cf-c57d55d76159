import { Migration } from '@mikro-orm/migrations';

export class Migration20240102202400 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "units_group" add column "is_base" boolean not null default false;',
    );

    this.addSql(
      'create unique index "units_group_base_id" on "units_group"("is_base") where "is_base" is true;',
    );

    this.addSql(
      'alter table "units_group_unit" add column "is_m_type_default" boolean not null default false;',
    );
    this.addSql(
      'create unique index "units_group_unit_m_type_default" on "units_group_unit"("units_group", "m_type", "is_m_type_default") where "is_m_type_default" is true;',
    );
  }

  async down(): Promise<void> {
    this.addSql('drop index "units_group_base_id";');
    this.addSql('alter table "units_group" drop column "is_base";');

    this.addSql('drop index "units_group_unit_m_type_default";');
    this.addSql(
      'alter table "units_group_unit" drop column "is_m_type_default";',
    );
  }
}
