import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Inject,
  Injectable,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { Reflector } from "@nestjs/core";
import { Request } from "express";
import authConfiguration from "src/authentication/auth.config";
import { AuthService } from "src/authentication/auth.service";
import { ExceptionMessages } from "src/errors/exceptions.contants";
import { Role } from "../domain/customer-user-role.entity";
import { CUSTOMER_ROLE_KEY, ROLE_KEY } from "./roles.decorator";

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly authService: AuthService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredCustomerRoles = this.reflector.getAllAndOverride<Role>(
      CUSTOMER_ROLE_KEY,
      [context.getHandler(), context.getClass()]
    );
    let isValid = false;
    const request = context.switchToHttp().getRequest<Request>();
    const customerId = request.params.customerId || request.query.customerId;
    if (requiredCustomerRoles) {
      if (customerId) {
        if (request && request.cookies) {
          const accessToken =
            request.cookies[this.authConfig.accessTokenHeader];
          const csrfCookieToken =
            request.cookies[this.authConfig.csrfTokenHeader];
          const csrfHeaderToken = request.header(
            this.authConfig.csrfTokenHeader.toLowerCase()
          );
          if (
            csrfHeaderToken === undefined ||
            csrfHeaderToken !== csrfCookieToken ||
            !this.authService.isValidCsrfToken(accessToken, csrfHeaderToken)
          ) {
            // throw new HttpException("Failed CSRF validation", 401);
            throw new HttpException(
              ExceptionMessages.FORBIDDEN.message,
              ExceptionMessages.FORBIDDEN.statusCode
            );
          }
          isValid = this.authService.hasCustomerRole(
            accessToken,
            Number(customerId),
            requiredCustomerRoles
          );
        }
      }
    }
    const requiredRole = this.reflector.getAllAndOverride<Role>(ROLE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (
      requiredRole === Role.ADMIN ||
      requiredRole === Role.USER ||
      requiredRole === Role.POWER_USER
    ) {
      if (request && request.cookies) {
        const accessToken = request.cookies[this.authConfig.accessTokenHeader];
        const csrfCookieToken =
          request.cookies[this.authConfig.csrfTokenHeader];
        const csrfHeaderToken = request.header(
          this.authConfig.csrfTokenHeader.toLowerCase()
        );
        const activeCustomerKeyId = request.header(
          this.authConfig.activeCustomerKeyId
        );
        if (
          activeCustomerKeyId === undefined ||
          activeCustomerKeyId === null ||
          Number.isNaN(Number(activeCustomerKeyId)) ||
          csrfHeaderToken === undefined ||
          csrfHeaderToken !== csrfCookieToken ||
          !this.authService.isValidCsrfToken(accessToken, csrfHeaderToken)
        ) {
          // throw new HttpException("Failed CSRF validation", 401);
          throw new HttpException(
            ExceptionMessages.FORBIDDEN.message,
            ExceptionMessages.FORBIDDEN.statusCode
          );
        }
        // isValid = this.authService.hasRoles(accessToken, requiredRole);
        isValid = this.authService.hasMaxRoles(
          accessToken,
          requiredRole,
          Number(activeCustomerKeyId)
        );
      }
    }
    if (!isValid) {
      // throw new ForbiddenException("Unauthorized");
      throw new HttpException(
        ExceptionMessages.FORBIDDEN.message,
        ExceptionMessages.FORBIDDEN.statusCode
      );
    }
    return true;
  }
}
