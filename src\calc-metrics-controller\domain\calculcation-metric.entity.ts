import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property } from "@mikro-orm/core";
import { CalculationMetricInstance } from "./calculation-metric-instance.entity";

@Entity()
export class CalculationMetric {
  @PrimaryKey()
  id!: number;

  @ManyToOne({
    entity: () => CalculationMetricInstance,
    fieldName: "calculation_metric_instance",
  })
  calculationInstance!: CalculationMetricInstance;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;
}
