import { registerAs } from "@nestjs/config";

export const DEFAULT_CSRF_HEADER = "BE-CSRFToken";
const DEFAULT_ACCESS_TOKEN_HEADER = "BE-AccessToken";

export default registerAs("auth", () => ({
  cookieDomain: process.env.AUTH_COOKIE_DOMAIN,
  jwtSecret: process.env.AUTH_JWT_SECRET,
  accessTokenHeader:
    process.env.AUTH_ACCESS_TOKEN_HEADER || DEFAULT_ACCESS_TOKEN_HEADER,
  csrfTokenHeader: process.env.AUTH_CSRF_TOKEN_HEADER || DEFAULT_CSRF_HEADER,
  activeCustomerKeyId: process.env.X_ACTIVE_CUSTOMER || "x-active-customer-id",
  sessionDurationMinutes: parseInt(
    process.env.AUTH_SESSION_DURATION_MIN || "5"
  ),
}));
