import { Knex } from '@mikro-orm/postgresql';
import { setupMigrationTest } from './migration-testing-utils';

describe('Asset template tag to metrics migration', () => {
  let knex: Knex;
  let engineAssetTypeId: number;
  let pumpAssetTypeId: number;

  beforeAll(async () => {
    const { migrator, connection } = await setupMigrationTest();
    await migrator.up({
      to: '20240202193247-add-asset-template-unique-constraint',
    });

    knex = connection.getKnex();

    const assetTypeCreation = await knex
      .insert([
        {
          name: 'Engine',
        },
        {
          name: 'Pump',
        },
      ])
      .returning('id')
      .into('asset_type');
    engineAssetTypeId = assetTypeCreation[0].id;
    pumpAssetTypeId = assetTypeCreation[1].id;

    const assetTemplateCreation = await knex
      .insert([
        {
          manufacturer: 'SpaceX',
          model_no: 'Raptor',
          asset_type: engineAssetTypeId,
        },
        {
          manufacturer: 'Blue Origin',
          model_no: 'BE-4',
          asset_type: pumpAssetTypeId,
        },
      ])
      .returning('id')
      .into('asset_template');
    const spacexRaptorTemplateId = assetTemplateCreation[0].id;
    const blueOriginBE4TemplateId = assetTemplateCreation[1].id;

    const measurementTypeCreation = await knex
      .insert([
        {
          name: 'RPM',
        },
        {
          name: 'Thrust',
        },
      ])
      .returning('id')
      .into('measurement_type');
    const rpmMeasurementTypeId = measurementTypeCreation[0].id;
    const thrustMeasurementTypeId = measurementTypeCreation[1].id;

    const dataTypeCreation = await knex
      .insert({
        name: 'real',
      })
      .returning('id')
      .into('data_type');

    const valueTypeCreation = await knex
      .insert({
        name: 'nominal',
      })
      .returning('id')
      .into('value_type');

    await knex
      .insert([
        {
          tag: 'RPM',
          asset_template: spacexRaptorTemplateId,
          m_type: rpmMeasurementTypeId,
          data_type: dataTypeCreation[0].id,
          value_type: valueTypeCreation[0].id,
        },
        {
          tag: 'Thrust',
          asset_template: spacexRaptorTemplateId,
          m_type: thrustMeasurementTypeId,
          data_type: dataTypeCreation[0].id,
          value_type: valueTypeCreation[0].id,
        },
        {
          tag: 'RPM',
          asset_template: blueOriginBE4TemplateId,
          m_type: rpmMeasurementTypeId,
          data_type: dataTypeCreation[0].id,
          value_type: valueTypeCreation[0].id,
        },
      ])
      .into('asset_template_measurement');

    await migrator.up({
      to: '20240208193247-update-asset-template-measurement-metrics',
    });
  });

  it('should create metrics', async () => {
    const res = await knex
      .select('id', 'name', 'asset_type')
      .from<{ id: number; name: string; asset_type: number }>('metric');

    expect(res.length).toBe(3);
    expect(res[0].name).toBe('Thrust');
    expect(res[0].asset_type).toBe(engineAssetTypeId);
    expect(res[1].name).toBe('RPM');
    expect(res[1].asset_type).toBe(engineAssetTypeId);
    expect(res[2].name).toBe('RPM');
    expect(res[2].asset_type).toBe(pumpAssetTypeId);
  });

  it('should update asset templates with created metrics', async () => {
    const res = await knex
      .select<{ tag: string; name: string }[]>('tag', 'm.name')
      .leftJoin({ m: 'metric' }, 'metric', 'm.id')
      .from('asset_template_measurement');

    expect(res.find((row) => row.tag !== row.name)).toBeUndefined();
  });
});
