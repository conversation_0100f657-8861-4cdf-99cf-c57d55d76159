import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { AssetHierarchyRepository } from './repository/asset-hierarchy.repository';
import { AssetId } from './domain/asset.entity';
import { UserId } from 'src/users/domain/user.entity';
import { AssetService } from './asset.service';
import { InvalidInputException } from 'src/errors/exceptions';
import { AssetHierarchyFactory } from './domain/asset-hierarchy.entity';

@Injectable()
export class AssetHierarchyService {
  constructor(
    private readonly assetHierarchyRepository: AssetHierarchyRepository,
    @Inject(forwardRef(() => AssetService))
    private readonly assetService: AssetService,
  ) {}

  async addRoot(rootAssetId: AssetId, runById: UserId) {
    const rootAsset = await this.assetService.findById(rootAssetId);

    if (rootAsset === null) {
      throw new InvalidInputException('Root asset does not exist');
    }

    await this.assetHierarchyRepository.add(
      AssetHierarchyFactory.create({
        childId: rootAssetId,
      }),
      runById,
    );
  }

  async addChild(parentId: AssetId, childAssetId: AssetId, runById: UserId) {
    const childAsset = await this.assetService.findById(childAssetId);

    if (childAsset === null) {
      throw new InvalidInputException('Child asset does not exist');
    }

    const parentAsset = await this.assetService.findById(
      parentId,
      childAsset.customerId,
    );

    if (parentAsset === null) {
      throw new InvalidInputException('Parent asset does not exist');
    }

    await this.assetHierarchyRepository.add(
      AssetHierarchyFactory.create({
        parentId,
        childId: childAssetId,
      }),
      runById,
    );
  }

  async removeChild(parentId: AssetId, childAssetId: AssetId, runById: UserId) {
    const childAsset = await this.assetService.findById(childAssetId);

    if (childAsset === null) {
      throw new InvalidInputException('Child asset does not exist');
    }

    const parentAsset = await this.assetService.findById(
      parentId,
      childAsset.customerId,
    );

    if (parentAsset === null) {
      throw new InvalidInputException('Parent asset does not exist');
    }

    await this.assetHierarchyRepository.removeByParentAndChildId(
      parentAsset.id,
      childAsset.id,
      runById,
    );
  }
}
