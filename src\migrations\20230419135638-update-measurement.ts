import { Migration } from '@mikro-orm/migrations';

export class Migration20230419135638 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "measurement" drop constraint "type_fk";');
    this.addSql('alter table "measurement" drop constraint "uom_fk";');
    this.addSql('alter table "measurement" drop constraint "data_type_fk";');
    this.addSql('alter table "measurement" drop constraint "v_type_fk";');
    this.addSql(
      'alter table "measurement" add constraint "measurement_m_type_foreign" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_unit_of_measure_foreign" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_data_type_foreign" foreign key ("data_type") references "data_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_value_type_foreign" foreign key ("value_type") references "value_type" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "measurement" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement" add constraint "measurement_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "measurement" drop column "created";');
    this.addSql('alter table "measurement" drop column "updated";');
    this.addSql('alter table "measurement" drop column "createdby";');
    this.addSql('alter table "measurement" drop column "updatedby";');

    this.addSql(
      'alter table "measurement" alter column "value_type" drop default;',
    );

    this.addSql(
      'alter table "measurement" alter column "value_type" set not null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "measurement" drop constraint "measurement_m_type_foreign";',
    );
    this.addSql(
      'alter table "measurement" drop constraint "measurement_unit_of_measure_foreign";',
    );
    this.addSql(
      'alter table "measurement" drop constraint "measurement_data_type_foreign";',
    );
    this.addSql(
      'alter table "measurement" drop constraint "measurement_value_type_foreign";',
    );
    this.addSql(
      'alter table "measurement" add constraint "type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "uom_fk" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement" add constraint "data_type_fk" foreign key ("data_type") references "data_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "measurement" add constraint "v_type_fk" foreign key ("value_type") references "value_type" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "measurement" alter column "value_type" drop not null;',
    );
    this.addSql(
      'alter table "measurement" alter column "value_type" set default 1;',
    );

    this.addSql(
      'alter table "measurement" drop constraint "measurement_created_by_foreign";',
    );
    this.addSql(
      'alter table "measurement" drop constraint "measurement_updated_by_foreign";',
    );

    this.addSql(
      'alter table "measurement" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "measurement" drop column "created_at";');
    this.addSql('alter table "measurement" drop column "updated_at";');
    this.addSql('alter table "measurement" drop column "created_by";');
    this.addSql('alter table "measurement" drop column "updated_by";');
  }
}
