import { Entity, ManyToOne, Property, Ref } from '@mikro-orm/core';
import { Dashboard } from './dashboard.entity';
import { Customer } from '../../customers/domain/customer.entity';
import { User } from '../../users/domain/user.entity';

@Entity()
export class DashboardDefault {

  @ManyToOne({ fieldName: 'dashboard_id', onDelete: 'cascade' })
  dashboard!: Ref<Dashboard>;

  @ManyToOne({ fieldName: 'customer_id', onDelete: 'cascade', primary: true })
  customer!: Ref<Customer>;

  @ManyToOne({ fieldName: 'user_id', onDelete: 'cascade', primary: true })
  user!: Ref<User>;

  @Property({ columnType: 'date' })
  createdAt!: Date;

  @ManyToOne({ fieldName: 'created_by', onDelete: 'cascade' })
  createdBy!: User;
}