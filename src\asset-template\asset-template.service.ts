import {
  En<PERSON><PERSON><PERSON><PERSON><PERSON>,
  UniqueConstraintViolationException,
} from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { AssetTypeService } from "src/assets/asset-type.service";
import { AssetTypeId } from "src/assets/domain/asset-type.entity";
import { Metric } from "src/assets/domain/metric.entity";
import { MetricService } from "src/assets/metric.service";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";
import { CalculationMetricInput } from "src/calc-metrics-controller/domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";
import { CustomerService } from "src/customers/customer.service";
import { Customer } from "src/customers/domain/customer.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { findOrThrowError, optionallyFindOrThrowError } from "src/errors/utils";
import { DatasourceService } from "src/measurements/datasource.service";
import { DataType } from "src/measurements/domain/data-type.entity";
import { Datasource } from "src/measurements/domain/datasource.entity";
import { Location } from "src/measurements/domain/location.entity";
import { MeasurementType } from "src/measurements/domain/measurement-type.entity";
import { ValueType } from "src/measurements/domain/value-type.entity";
import { MeasurementsBackofficeService } from "src/measurements/measurements-backoffice.service";
import { User, UserId } from "src/users/domain/user.entity";
import { AssetTemplate, AssetTemplateId } from "./domain/asset-template.entity";
import { expressionInstanceDto } from "./dto/asset-template.dto";
import { AssetTemplateRepository } from "./repository/asset-template.repository";

export type AssetTemplateCreationData = {
  manufacturer: string;
  modelNumber: string;
  assetTypeId: number;
  measurements: {
    metricId: number | string;
    measurementTypeId: number;
    dataTypeId: number;
    valueTypeId: number;
    locationId?: number;
    datasourceId?: number;
    description?: string;
    meterFactor?: number;
  }[];
  save_as_global_asset_template?: boolean;
};

export type EditAssetTemplateData = {
  manufacturer: string;
  modelNumber: string;
  assetTypeId: number;
  save_as_global_asset_template?: boolean;
  measurements: {
    id: number | undefined;
    metricId: number | string;
    measurementTypeId: number;
    dataTypeId: number;
    valueTypeId: number;
    locationId?: number;
    datasourceId?: number;
    description?: string;
    meterFactor?: number;
  }[];
};
@Injectable()
export class AssetTemplateService {
  constructor(
    private readonly assetTemplateRepository: AssetTemplateRepository,
    private readonly assetTypeService: AssetTypeService,
    private readonly metricService: MetricService,
    private readonly measurementsBackofficeService: MeasurementsBackofficeService,
    private readonly datasourceService: DatasourceService,
    private readonly customerService: CustomerService,
    private readonly transactionFactory: TransactionFactory,
    @InjectRepository(CalculationTemplate)
    private calculationTemplateRepo: EntityRepository<CalculationTemplate>,
    @InjectRepository(CalculationMetricInstance)
    private calcMetricInstanceRepository: EntityRepository<CalculationMetricInstance>,
    @InjectRepository(CalculationMetricInput)
    private calcMetricsInputRepository: EntityRepository<CalculationMetricInput>
  ) {}

  async findByTemplateAndTypeId(
    assetTemplateId: AssetTemplateId,
    assetTypeId: AssetTypeId
  ) {
    return await this.assetTemplateRepository.findById(assetTemplateId, {
      assetTypeId,
    });
  }

  async create(
    assetTemplate: AssetTemplateCreationData,
    runById: UserId,
    customerId: number
  ): Promise<AssetTemplate> {
    const assetType = await findOrThrowError(
      assetTemplate.assetTypeId,
      (id) => this.assetTypeService.findById(id),
      "Asset type does not exist"
    );

    const measurementParams = await Promise.all(
      assetTemplate.measurements.map(async (measurement) => {
        const {
          metricId,
          measurementTypeId,
          dataTypeId,
          valueTypeId,
          locationId,
          datasourceId,
        } = measurement;
        const metric = await findOrThrowError(
          metricId,
          (id) => this.metricService.findById(id as number),
          "Metric does not exist"
        );

        const measurementType = await findOrThrowError(
          measurementTypeId,
          (id) => this.measurementsBackofficeService.getMeasurementTypeById(id),
          "Measurement type does not exist"
        );

        const dataType = await findOrThrowError(
          dataTypeId,
          (id) => this.measurementsBackofficeService.getDataTypeById(id),
          "Data type does not exist"
        );

        const valueType = await findOrThrowError(
          valueTypeId,
          (id) => this.measurementsBackofficeService.getValueTypeById(id),
          "Value type does not exist"
        );

        const location = await optionallyFindOrThrowError(
          locationId,
          (id) => this.measurementsBackofficeService.getLocationById(id),
          "Location does not exist"
        );

        const datasource = await optionallyFindOrThrowError(
          datasourceId,
          (id) => this.datasourceService.getById(id),
          "Datasource does not exist"
        );

        return {
          ...measurement,
          measurementType,
          metric,
          dataType,
          valueType,
          location,
          datasource,
        };
      })
    );
    let customer: Customer | null = null;
    if (!assetTemplate.save_as_global_asset_template) {
      customer = await this.customerService.findById(customerId);
      if (!customer) {
        throw new Error("Customer does not exist");
      }
    }
    const newAssetTemplate = new AssetTemplate({
      ...assetTemplate,
      assetType,
      manufacturer: assetTemplate.manufacturer,
      measurements: measurementParams,
    });
    newAssetTemplate.customer = customer;
    return this.assetTemplateRepository.add(newAssetTemplate, runById);
  }

  async createMulti(
    metrics: string[],
    assetTemplate: AssetTemplateCreationData,
    runById: User,
    customerId: number, // Promise<AssetTemplate>
    expressionInstance?: expressionInstanceDto[]
  ) {
    return this.transactionFactory.run(async (emLocal) => {
      if (expressionInstance && Array.isArray(expressionInstance) === false) {
        throw new Error("expressionInstance must be an array");
      }
      if (expressionInstance) {
        const expressionInstanceIds = expressionInstance.map(
          (instance) => instance.templateId
        );

        const templateIds = Array.from(
          new Set(expressionInstanceIds.map((cm) => cm))
        );
        const templates = await this.calculationTemplateRepo.find({
          id: { $in: templateIds },
        });
        const foundTemplateIds = templates.map((t) => t.id);
        const missingTemplates = templateIds.filter(
          (id) => !foundTemplateIds.includes(id)
        );
        if (missingTemplates.length > 0) {
          throw new NotFoundException(
            `Templates not found: ${missingTemplates.join(", ")}`
          );
        }
        for (const cm of expressionInstance) {
          const hasMeasurement = cm.variables.some(
            (input) => input?.metric !== undefined
          );
          if (!hasMeasurement) {
            throw new ConflictException(
              `Calculation metric with outputMetric ${cm.metricId} has no measurement input`
            );
          }
        }
      }
      const newMetrics: Metric[] = [];
      try {
        const metricsData = await this.metricService.createMany({
          metrics,
          assetTypeId: assetTemplate.assetTypeId,
          user: runById,
        });
        metricsData.forEach((metric) => {
          newMetrics.push(metric);
        });
      } catch (error) {
        if (error instanceof UniqueConstraintViolationException) {
          throw new ConflictException(
            "One or more metrics already exist for this asset type."
          );
        }
        throw error;
      }
      assetTemplate.measurements = assetTemplate.measurements.map((measure) => {
        // Check if metricId is NaN
        if (isNaN(Number(measure.metricId))) {
          const metric = newMetrics.find(
            (metric) => metric.name === measure.metricId?.toString()
          );
          if (metric) {
            return { ...measure, metricId: metric.id as number }; // Use `metricId` to match naming conventions
          }
          return measure;
        }
        return measure;
      });
      const assetType = await findOrThrowError(
        assetTemplate.assetTypeId,
        (id) => this.assetTypeService.findById(id),
        "Asset type does not exist"
      );
      const measurementParams = await Promise.all(
        assetTemplate.measurements.map(async (measurement) => {
          const {
            metricId,
            measurementTypeId,
            dataTypeId,
            valueTypeId,
            locationId,
            datasourceId,
          } = measurement;
          const metric = await findOrThrowError(
            metricId,
            (id) => this.metricService.findById(Number(id)),
            "Metric does not exist"
          );

          const measurementType = await findOrThrowError(
            measurementTypeId,
            (id) =>
              this.measurementsBackofficeService.getMeasurementTypeById(id),
            "Measurement type does not exist"
          );

          const dataType = await findOrThrowError(
            dataTypeId,
            (id) => this.measurementsBackofficeService.getDataTypeById(id),
            "Data type does not exist"
          );

          const valueType = await findOrThrowError(
            valueTypeId,
            (id) => this.measurementsBackofficeService.getValueTypeById(id),
            "Value type does not exist"
          );

          const location = await optionallyFindOrThrowError(
            locationId,
            (id) => this.measurementsBackofficeService.getLocationById(id),
            "Location does not exist"
          );

          const datasource = await optionallyFindOrThrowError(
            datasourceId,
            (id) => this.datasourceService.getById(id),
            "Datasource does not exist"
          );

          return {
            ...measurement,
            measurementType,
            metric,
            dataType,
            valueType,
            location,
            datasource,
          };
        })
      );
      let customer: Customer | null = null;
      if (!assetTemplate.save_as_global_asset_template) {
        customer = await this.customerService.findById(customerId);
        if (!customer) {
          throw new Error("Customer does not exist");
        }
      }
      const newAssetTemplate = new AssetTemplate({
        ...assetTemplate,
        assetType,
        manufacturer: assetTemplate.manufacturer,
        measurements: measurementParams,
      });
      newAssetTemplate.customer = customer;
      const assetTemplateData = await this.assetTemplateRepository.add(
        newAssetTemplate,
        runById.id
      );
      if (expressionInstance) {
        const allCreatedInputs = [];
        // check expressioninstance metricId and map it to the new metricId if metric is NAN
        expressionInstance = expressionInstance.map((instance) => {
          // Step 1: Fix instance.metricId if it's not a number
          let updatedInstance = { ...instance };

          if (isNaN(Number(instance.metricId))) {
            const matchedMetric = newMetrics.find(
              (metric) => metric.name === instance.metricId?.toString()
            );
            if (matchedMetric) {
              updatedInstance.metricId = matchedMetric.id;
            }
          }

          // Step 2: Fix each variable's metric if it's not a number
          updatedInstance.variables = updatedInstance.variables.map((input) => {
            if (isNaN(Number(input.metric))) {
              const matchedMetric = newMetrics.find(
                (metric) => metric.name === input.metric?.toString()
              );
              if (matchedMetric) {
                return { ...input, metric: matchedMetric.id };
              }
            }
            return input;
          });

          return updatedInstance;
        });
        const outoutMetric = await this.calcMetricInstanceRepository.find({
          assetTemplate: assetTemplateData.id,
          outputMetric: { $in: expressionInstance.map((cm) => cm.metricId) },
        });
        if (outoutMetric.length > 0) {
          throw new ConflictException(
            "One or more metrics already exist for calculation metric."
          );
        }
        for (const calcMetric of expressionInstance) {
          const instance = this.calcMetricInstanceRepository.create({
            calculation: calcMetric.templateId,
            ispersisted: calcMetric.ispersisted,
            outputMetric: calcMetric.metricId,
            pollPeriod: calcMetric.pollPeriod,
            created: new Date(),
            createdby: runById.id,
            assetTemplate: assetTemplateData.id,
          });
          await this.calcMetricInstanceRepository.persistAndFlush(instance);
          const inputsForThisInstance = [];
          for (const input of calcMetric.variables) {
            const calcInput = this.calcMetricsInputRepository.create({
              created: new Date(),
              createdby: runById.id,
              calculationMetricInstance: instance.id,
              inputLabel: input.inputLabel,
              comment: input.comment ?? "",
            });
            if (input.metric) {
              const metric = await this.metricService.findById(input.metric);
              if (!metric) {
                throw new ConflictException(
                  `Measurement with ID ${input.metric} not found`
                );
              }
              calcInput.metric = metric;
            } else {
              calcInput.constantNumber =
                input.constantType === "number" ? input.constantValue : null;
              calcInput.constantString =
                input.constantType === "string" ? input.constantValue : null;
            }
            await this.calcMetricsInputRepository.persistAndFlush(calcInput);
            inputsForThisInstance.push(calcInput);
          }
          allCreatedInputs.push({
            instanceId: instance.id,
            outputMetricId: calcMetric.metricId,
            templateId: calcMetric.templateId,
            inputs: inputsForThisInstance,
          });
        }
      }
      return assetTemplateData;
    });
  }
  async getAllByAssetType(
    assetTypeId: AssetTypeId,
    customerId: number
  ): Promise<AssetTemplate[]> {
    return await this.assetTemplateRepository.getAllByAssetTypeId(
      assetTypeId,
      customerId
    );
  }

  async getAssetTemplate(assetTypeId: AssetTypeId, assetTemplateId: number) {
    const assetTemplate =
      await this.assetTemplateRepository.getAssetTemplateWithAssetTypeAndId(
        assetTypeId,
        assetTemplateId
      );
    const calculatedMetrics = await this.calcMetricInstanceRepository.find({
      assetTemplate,
    });
    const calculatedMetricsInputs = await this.calcMetricsInputRepository.find({
      calculationMetricInstance: {
        $in: calculatedMetrics.map((metrics) => metrics.id),
      },
    });
    return {
      assetTemplate: assetTemplate,
      calculatedMetrics: calculatedMetrics,
      calculatedMetricsInputs: calculatedMetricsInputs,
    };
  }

  async editAssetTemplate(
    assetTypeId: AssetTypeId,
    assetTemplateId: number,
    data: EditAssetTemplateData,
    createdBy: number,
    headers: Request["headers"]
  ) {
    const assetType = await findOrThrowError(
      data.assetTypeId,
      (id) => this.assetTypeService.findById(id),
      "Asset type does not exist"
    );

    const measurementParams = await Promise.all(
      data.measurements.map(async (measurement) => {
        const {
          metricId,
          measurementTypeId,
          dataTypeId,
          valueTypeId,
          locationId,
          datasourceId,
        } = measurement;

        const metric = await findOrThrowError(
          metricId,
          (id) => this.metricService.findById(id as number),
          "Metric does not exist"
        );

        const measurementType = await findOrThrowError(
          measurementTypeId,
          (id) => this.measurementsBackofficeService.getMeasurementTypeById(id),
          "Measurement type does not exist"
        );

        const dataType = await findOrThrowError(
          dataTypeId,
          (id) => this.measurementsBackofficeService.getDataTypeById(id),
          "Data type does not exist"
        );

        const valueType = await findOrThrowError(
          valueTypeId,
          (id) => this.measurementsBackofficeService.getValueTypeById(id),
          "Value type does not exist"
        );

        let location = null;
        if (locationId) {
          location = await optionallyFindOrThrowError(
            locationId,
            (id) => this.measurementsBackofficeService.getLocationById(id),
            "Location does not exist"
          );
        }

        let datasource = null;
        if (datasourceId) {
          datasource = await optionallyFindOrThrowError(
            datasourceId,
            (id) => this.datasourceService.getById(id),
            "Datasource does not exist"
          );
        }

        // Prepare the return object, set location and datasource only if they have values
        const result: {
          measurementType: MeasurementType;
          metric: Metric;
          dataType: DataType;
          valueType: ValueType;
          metricId: number;
          measurementTypeId: number;
          dataTypeId: number;
          valueTypeId: number;
          location?: Location;
          datasource?: Datasource;
          datasourceId?: number;
          description?: string;
          meterFactor?: number;
        } = {
          ...measurement,
          measurementType,
          metricId: metric.id,
          metric,
          dataType,
          valueType,
        };

        // Set location if it's available
        if (location) {
          result.location = location;
        }

        // Set datasource if it's available
        if (datasource) {
          result.datasource = datasource;
        }

        return result;
      })
    );

    // Create update data with new measurements
    const updateData = {
      manufacturer: data.manufacturer,
      modelNumber: data.modelNumber,
      assetTypeId: assetType,
      measurement: measurementParams,
    };

    // Call the updateAssetTemplate method with the updateData
    return await this.assetTemplateRepository.updateAssetTemplate({
      assetTypeId: assetType,
      templateId: assetTemplateId,
      updateData, // Pass the data to be updated
      createdBy,
      headers,
    });
  }

  async editAssetTemplateMulti(
    metrics: string[],
    assetTypeId: AssetTypeId,
    assetTemplateId: number,
    data: EditAssetTemplateData,
    createdBy: User,
    headers: Request["headers"],
    emManager?: EntityManager,
    expressionInstance?: expressionInstanceDto[]
  ) {
    return this.transactionFactory.run(async (emLocal) => {
      const em = emManager ?? emLocal;
      const assetType = await findOrThrowError(
        data.assetTypeId,
        (id) => this.assetTypeService.findById(id),
        "Asset type does not exist"
      );

      const newMetrics: Metric[] = [];
      try {
        const metricsData = await this.metricService.createMany({
          metrics,
          assetTypeId: data.assetTypeId,
          user: createdBy,
        });
        metricsData.forEach((metric) => {
          newMetrics.push(metric);
        });
      } catch (error) {
        if (error instanceof UniqueConstraintViolationException) {
          throw new ConflictException(
            "One or more metrics already exist for this asset type."
          );
        }
        throw error;
      }
      const measurementParams = await Promise.all(
        data.measurements.map(async (measurement) => {
          let {
            metricId,
            measurementTypeId,
            dataTypeId,
            valueTypeId,
            locationId,
            datasourceId,
          } = measurement;
          if (isNaN(Number(metricId))) {
            const metric = newMetrics.find(
              (metric) => metric.name === metricId?.toString()
            );
            if (metric) {
              metricId = metric.id;
            }
          }
          const metric = await findOrThrowError(
            metricId,
            (id) => this.metricService.findById(id as number),
            "Metric does not exist"
          );

          const measurementType = await findOrThrowError(
            measurementTypeId,
            (id) =>
              this.measurementsBackofficeService.getMeasurementTypeById(id),
            "Measurement type does not exist"
          );

          const dataType = await findOrThrowError(
            dataTypeId,
            (id) => this.measurementsBackofficeService.getDataTypeById(id),
            "Data type does not exist"
          );

          const valueType = await findOrThrowError(
            valueTypeId,
            (id) => this.measurementsBackofficeService.getValueTypeById(id),
            "Value type does not exist"
          );

          let location = null;
          if (locationId) {
            location = await optionallyFindOrThrowError(
              locationId,
              (id) => this.measurementsBackofficeService.getLocationById(id),
              "Location does not exist"
            );
          }

          let datasource = null;
          if (datasourceId) {
            datasource = await optionallyFindOrThrowError(
              datasourceId,
              (id) => this.datasourceService.getById(id),
              "Datasource does not exist"
            );
          }

          // Prepare the return object, set location and datasource only if they have values
          const result: {
            measurementType: MeasurementType;
            metric: Metric;
            dataType: DataType;
            valueType: ValueType;
            metricId: number;
            measurementTypeId: number;
            dataTypeId: number;
            valueTypeId: number;
            location?: Location;
            datasource?: Datasource;
            datasourceId?: number;
            description?: string;
            meterFactor?: number;
          } = {
            ...measurement,
            metricId: metric.id,
            measurementType,
            metric,
            dataType,
            valueType,
          };

          // Set location if it's available
          if (location) {
            result.location = location;
          }

          // Set datasource if it's available
          if (datasource) {
            result.datasource = datasource;
          }

          return result;
        })
      );

      // Create update data with new measurements
      const updateData = {
        manufacturer: data.manufacturer,
        modelNumber: data.modelNumber,
        assetTypeId: assetType,
        measurement: measurementParams,
      };

      // Call the updateAssetTemplate method with the updateData
      return await this.assetTemplateRepository.updateAssetTemplateMulti({
        assetTypeId: assetType,
        templateId: assetTemplateId,
        updateData, // Pass the data to be updated
        createdBy: createdBy,
        headers,
        em,
        expressionInstance,
      });
    });
  }
  async getAssociatedAssets(assetTemplateId: number) {
    return await this.assetTemplateRepository.getAssociatedAssets(
      assetTemplateId
    );
  }
}
