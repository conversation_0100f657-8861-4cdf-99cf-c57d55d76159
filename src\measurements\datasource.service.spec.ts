import { Test } from '@nestjs/testing';
import { Datasource } from './domain/datasource.entity';
import { getRepositoryToken } from '@mikro-orm/nestjs';
import { DatasourceService } from './datasource.service';
import { EntityRepository } from '@mikro-orm/postgresql';

describe('Datasource service', () => {
  test('getting datasources should call findAll repository method', async () => {
    const { datasourceService, datasourceRepository } =
      await createDatasourceService();

    datasourceService.getAll();

    expect(datasourceRepository.findAll.mock.calls.length).toBe(1);
  });
});

async function createDatasourceService() {
  const datasourceRepository: jest.Mocked<
    Pick<EntityRepository<Datasource>, 'findAll'>
  > = {
    findAll: jest.fn(),
  };

  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(Datasource),
        useValue: datasourceRepository,
      },
      DatasourceService,
    ],
  }).compile();
  const datasourceService = moduleRef.get(DatasourceService);
  return { datasourceService, datasourceRepository };
}
