import {
  <PERSON><PERSON>ty,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON>ey,
  Property,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { MeasurementType } from './measurement-type.entity';
import { User } from 'src/users/domain/user.entity';

@Entity()
@Unique({
  name: 'unit_of_measure_name_m_type_key',
  properties: ['name', 'measurementType'],
})
export class UnitOfMeasure {
  @PrimaryKey()
  id!: number;

  @Property({ length: 50 })
  name!: string;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;

  @ManyToOne({ serializedName: 'measurement_type_id', joinColumn: 'm_type' })
  measurementType!: Ref<MeasurementType>;
}
