import { Migration } from '@mikro-orm/migrations';

export class Migration20230315150257 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_hier" add column "created_at" timestamptz(6) null, add column "updated_at" timestamptz(6) null, add column "created_by" int null, add column "updated_by" int null;',
    );
    this.addSql(
      'alter table "asset_hier" add constraint "asset_hier_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_hier" add constraint "asset_hier_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql('alter table "asset_hier" drop column "created";');
    this.addSql('alter table "asset_hier" drop column "updated";');
    this.addSql('alter table "asset_hier" drop column "createdby";');
    this.addSql('alter table "asset_hier" drop column "updatedby";');

    this.addSql('alter table "asset_hier" drop constraint "child_fk";');
    this.addSql('alter table "asset_hier" drop constraint "parent_fk";');

    this.addSql(
      'alter table "asset_hier" add constraint "asset_hier_child_foreign" foreign key ("child") references "asset" ("id") on update cascade on delete cascade;',
    );

    this.addSql(
      'alter table "asset_hier" add constraint "asset_hier_parent_foreign" foreign key ("parent") references "asset" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_hier" drop constraint "asset_hier_created_by_foreign";',
    );
    this.addSql(
      'alter table "asset_hier" drop constraint "asset_hier_updated_by_foreign";',
    );

    this.addSql(
      'alter table "asset_hier" add column "created" timestamptz(6) null, add column "updated" timestamptz(6) null, add column "createdby" int null, add column "updatedby" int null;',
    );
    this.addSql('alter table "asset_hier" drop column "created_at";');
    this.addSql('alter table "asset_hier" drop column "updated_at";');
    this.addSql('alter table "asset_hier" drop column "created_by";');
    this.addSql('alter table "asset_hier" drop column "updated_by";');

    this.addSql(
      'alter table "asset_hier" drop constraint "asset_hier_child_foreign";',
    );

    this.addSql(
      'alter table "asset_hier" drop constraint "asset_hier_parent_foreign";',
    );

    this.addSql(
      'alter table "asset_hier" add constraint "child_fk" foreign key ("child") references "asset" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "asset_hier" add constraint "parent_fk" foreign key ("parent") references "asset" ("id") on update cascade on delete set null;',
    );
  }
}
