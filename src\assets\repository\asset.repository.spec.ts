import { Test } from '@nestjs/testing';
import { AssetRepository } from './asset.repository';
import { Asset } from '../domain/asset.entity';
import { getRepositoryToken } from '@mikro-orm/nestjs';
import { EntityManager, EntityRepository } from '@mikro-orm/postgresql';
import { HierarchyGraphService } from '../hierarchy-graph.service';

describe('AssetRepository', () => {
  describe('findById', () => {
    test('querying an asset from a customer should set corresponding filter', async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      await assetRepository.findById(43, { customerId: 84 });

      const filterCondition = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        id: 43,
        customer: 84,
        deletedAt: null,
      });
    });
  });

  describe('getAllByCustomerId', () => {
    test('customer id should be set in find query', async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      await assetRepository.getAllByCustomerId(42);

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
        ],
      });
    });

    test('given ids, customer id and ids should be set in find query', async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { ids: [4, 5, 6] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          { id: { $in: [4, 5, 6] } },
        ],
      });
    });

    test('given parentIds filter with -1, customer id and parent filter should be set in find query', async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { parentIds: [-1] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          {
            $or: [
              { parentHierarchies: { parent: { $eq: null }, deletedAt: null } },
              { parentHierarchies: { parent: { $in: [-1] }, deletedAt: null } },
            ],
          },
        ],
      });
    });

    test('given parentIds filter without -1, customer id and parent filter should be set in find query', async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { parentIds: [5, 665] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          { parentHierarchies: { parent: { $in: [5, 665] }, deletedAt: null } },
        ],
      });
    });
  });
});

const createAssetRepository = async () => {
  const entityRepositoryMock: jest.Mocked<
    Pick<EntityRepository<Asset>, 'findOne' | 'find'>
  > = {
    findOne: jest.fn(),
    find: jest.fn(),
  };
  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(Asset),
        useValue: entityRepositoryMock,
      },
      { provide: EntityManager, useValue: jest.fn() },
      { provide: HierarchyGraphService, useValue: new HierarchyGraphService() },
      AssetRepository,
    ],
  }).compile();
  const assetRepository = moduleRef.get(AssetRepository);

  return { assetRepository, entityRepositoryMock };
};
