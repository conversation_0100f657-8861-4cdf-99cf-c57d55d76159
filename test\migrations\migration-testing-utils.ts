import { AbstractSqlConnection, MikroORM } from '@mikro-orm/postgresql';
import dotenv from 'dotenv';
import { AppModule } from 'src/app.module';
import config from 'src/mikro-orm.config';

export const setupMigrationTest = async () => {
  dotenv.config({
    path: `${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env`,
  });
  process.env.NEW_DB = 'true';
  // force lazy eval of config to load dotenv variables
  const orm = await MikroORM.init((() => config)());

  const generator = orm.getSchemaGenerator();

  await generator.dropSchema({
    dropMigrationsTable: true,
  });

  return {
    migrator: orm.getMigrator(),
    connection: orm.em.getConnection() as AbstractSqlConnection,
  };
};
