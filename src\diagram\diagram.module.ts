import { MikroOrmModule } from "@mikro-orm/nestjs";
import { forwardRef, Lo<PERSON>, Module } from "@nestjs/common";
import { User } from "src/users/domain/user.entity";
import { DiagramController } from "./diagram.controller";
import { Diagram } from "./domain/diagram.entity";
import { DiagramService } from "./diagram.service";
import { DiagramRepository } from "./diagram.repository";
import { SecurityModule } from "src/security/security.module";
import { HttpModule } from "@nestjs/axios";
import { AuthModule } from "src/authentication/auth.module";

@Module({
  imports: [
    MikroOrmModule.forFeature([Diagram, User]),
    SecurityModule,
    HttpModule,
    forwardRef(() => AuthModule),
  ],
  providers: [
    DiagramService,
    DiagramRepository,
    {
      provide: Logger,
      useValue: new Logger(DiagramService.name),
    },
  ],
  controllers: [DiagramController],
  exports: [],
})
export class DiagramModule {}
