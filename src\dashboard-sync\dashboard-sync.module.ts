import { MikroOrmModule } from "@mikro-orm/nestjs";
// import { BullModule } from "@nestjs/bullmq";
import { <PERSON><PERSON>, Module } from "@nestjs/common";
import { Asset } from "src/assets/domain/asset.entity";
import { CustomerService } from "src/customers/customer.service";
import { Customer } from "src/customers/domain/customer.entity";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";
import { DashboardDefault } from "src/dashboards/domain/default-dashboard.entity";
import { DashboardFavorite } from "src/dashboards/domain/favorite-dashboard.entity";
import { DashboardRepository } from "src/dashboards/repository/dashboard.repository";
import { DashboardSyncController } from "./dashboard-sync.controller";
import { DashboardSyncService } from "./dashboard-sync.service";
import { DashboardSyncJob } from "./domain/dashboard-sync.entity";
import { DashboardSyncRepository } from "./repository/dashboard-sync.repository";
import { CustomersModule } from "src/customers/customers.module";
import { AuthModule } from "src/authentication/auth.module";

@Module({
  imports: [
    // BullModule.registerQueue({
    //   name: "dashboard-sync",
    // }),
    MikroOrmModule.forFeature([
      Dashboard,
      DashboardDefault,
      DashboardFavorite,
      Customer,
      DashboardSyncJob,
      Asset,
    ]),
    AuthModule,
    CustomersModule,
  ],
  controllers: [DashboardSyncController],
  providers: [
    DashboardSyncService,
    DashboardSyncRepository,
    DashboardRepository,
    CustomerService,
    {
      provide: Logger,
      useValue: new Logger(DashboardSyncService.name),
    },
  ],
})
export class DashboardSyncModule {}
