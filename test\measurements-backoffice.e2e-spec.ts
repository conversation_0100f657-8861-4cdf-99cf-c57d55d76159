import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  createUnitOfMeasure,
  deleteUnitOfMeasureByName,
  loginUser,
  setupApp,
} from './test-utils';
import request from 'supertest';
import {
  deleteMetadataType,
  createMetadataType,
  measurementFixtureFactory,
} from './fixtures/measurement.fixture';
import { UserFixture, userFixtureFactory } from './fixtures/user.fixture';
import {
  UnitsGroupEuropeFixture,
  UnitsGroupFixtureFactory,
} from './fixtures/units-group.fixture';

describe('/measurements-backoffice', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let userFixture: UserFixture;
  let httpClient: request.SuperAgentTest;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    userFixture = await userFixtureFactory.createSuperUser(testingModule);

    httpClient = await loginUser(
      app.getHttpServer(),
      userFixture.username,
      userFixture.password,
    );
  });

  afterAll(async () => {
    await userFixture.cleanUp();
    await app.close();
  });

  describe('/measurements-backoffice/measurement-types', () => {
    describe('GET', () => {
      let response: request.Response;
      let chargeTypeId: number;
      let volumeTypeId: number;

      beforeAll(async () => {
        chargeTypeId = await createMetadataType(
          testingModule,
          'Charge',
          'measurement',
        );
        volumeTypeId = await createMetadataType(
          testingModule,
          'Volume',
          'measurement',
        );

        response = await httpClient.get(
          '/v0/measurements-backoffice/measurement-types',
        );
      });

      afterAll(async () => {
        await deleteMetadataType(testingModule, 'Charge', 'measurement');
        await deleteMetadataType(testingModule, 'Volume', 'measurement');
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with two elements', () => {
        expect(response.body.total).toBe(2);
        expect(response.body.items.length).toBe(2);
      });

      it('should expose only visible measurement type fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          new Set(['id', 'name']),
        );
      });

      it('should return measurement types with all fields', () => {
        const chargeMeasurementType = response.body.items[0];
        const volumeMeasurementType = response.body.items[1];
        expect(chargeMeasurementType.id).toBe(chargeTypeId);
        expect(chargeMeasurementType.name).toBe('Charge');
        expect(volumeMeasurementType.id).toBe(volumeTypeId);
        expect(volumeMeasurementType.name).toBe('Volume');
      });
    });
  });

  describe('/measurements-backoffice/measurement-types/{measurementTypeId}/units-of-measure', () => {
    describe('GET', () => {
      let response: request.Response;
      let chargeTypeId: number;
      let chargeUnitOfMeasureId: number;

      beforeAll(async () => {
        chargeTypeId = await createMetadataType(
          testingModule,
          'Charge',
          'measurement',
        );

        chargeUnitOfMeasureId = await createUnitOfMeasure(
          testingModule,
          'Coulomb',
          chargeTypeId,
        );

        response = await httpClient.get(
          `/v0/measurements-backoffice/measurement-types/${chargeTypeId}/units-of-measure`,
        );
      });

      afterAll(async () => {
        await deleteUnitOfMeasureByName(testingModule, 'Coulomb');
        await deleteMetadataType(testingModule, 'Charge', 'measurement');
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with one element', () => {
        expect(response.body.total).toBe(1);
        expect(response.body.items.length).toBe(1);
      });

      it('should expose only visible unit of measure fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          new Set(['id', 'name', 'measurement_type_id']),
        );
      });

      it('should return units of measure with all fields', () => {
        const chargeUnitOfMeasure = response.body.items[0];
        expect(chargeUnitOfMeasure.id).toBe(chargeUnitOfMeasureId);
        expect(chargeUnitOfMeasure.name).toBe('Coulomb');
        expect(chargeUnitOfMeasure.measurement_type_id).toBe(chargeTypeId);
      });
    });
  });

  describe('/measurements-backoffice/units-groups', () => {
    let measurementFixture;
    let europeUnitsGroupFixture: UnitsGroupEuropeFixture;

    beforeAll(async () => {
      measurementFixture = await measurementFixtureFactory.createMetadata(
        testingModule,
      );
      europeUnitsGroupFixture = await new UnitsGroupFixtureFactory(
        testingModule,
      ).createEuropeUnitsGroup(measurementFixture.powerTypeId);
    });

    afterAll(async () => {
      await europeUnitsGroupFixture.cleanUp();
      await measurementFixture.cleanUp();
    });

    describe('GET', () => {
      let response: request.Response;

      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/measurements-backoffice/units-groups`,
        );
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with one element', () => {
        expect(response.body.total).toBe(1);
        expect(response.body.items.length).toBe(1);
      });

      it('should expose only visible units group fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          new Set(['id', 'name']),
        );
      });

      it('should return units group with all fields', () => {
        const europeUnitsGroup = response.body.items[0];
        expect(europeUnitsGroup.id).toBe(
          europeUnitsGroupFixture.europeUnitsGroupId,
        );
        expect(europeUnitsGroup.name).toBe('eu');
      });
    });

    describe('GET .../{unitsGroupId}/units-of-measure', () => {
      let response: request.Response;

      beforeAll(async () => {
        response = await httpClient.get(
          `/v0/measurements-backoffice/units-groups/${europeUnitsGroupFixture.europeUnitsGroupId}/units-of-measure`,
        );
      });

      it('should return a 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with one element', () => {
        expect(response.body.total).toBe(2);
        expect(response.body.items.length).toBe(2);
      });

      it('should expose only visible units group unit fields', () => {
        expect(new Set(Object.keys(response.body.items[0]))).toEqual(
          new Set([
            'id',
            'name',
            'is_measurement_type_default',
            'measurement_type_id',
          ]),
        );
      });

      it('should return units group unit with all fields', () => {
        const jouleUnit = response.body.items[0];
        expect(jouleUnit.id).toBe(europeUnitsGroupFixture.wattUnitOfMeasureId);
        expect(jouleUnit.name).toBe('watt');
        expect(jouleUnit.is_measurement_type_default).toBeTruthy();
        expect(jouleUnit.measurement_type_id).toBe(
          measurementFixture.powerTypeId,
        );
      });
    });
  });
});
