import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import {
  ApiConflictResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
} from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { User } from "src/users/domain/user.entity";
import { DashboardService } from "./dashboard.service";
import { Dashboard } from "./domain/dashboard.entity";
import {
  DashboardConflictResDto,
  DashboardCreationDto,
  DashboardSuccessResDto,
  DefaultDashboardCreationDto,
} from "./dto/dashboard.dto";
import { FavoriteDashboardDto } from "./dto/favorite_dashboard.dto";
import {
  GetAllDashboardForbiddenResDto,
  GetAllDashboardSuccessResDto,
} from "./dto/get-all-dashboard.dto";
import {
  GetDashboardByIdForbiddenResDto,
  GetDashboardByIdNotFoundResDto,
  GetDashboardByIdSuccessResDto,
} from "./dto/get-dashboard.dto";
import {
  UpdateDashboardConflictResDto,
  UpdateDashboardDto,
} from "./dto/update-dashboard.dto";

@Controller({ version: "0", path: "customers/:customerId/dashboards" })
@UseGuards(JwtAuthGuard, CsrfGuard)
export class DashboardsApiController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Post()
  @HasCustomerRole(Role.ADMIN)
  @ApiOkResponse({ type: DashboardSuccessResDto })
  @ApiConflictResponse({ type: DashboardConflictResDto })
  async create(
    @AuthUser() authUser: User,
    @Body() newDashboard: DashboardCreationDto,
    @Param("customerId") customerId: number
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    return await this.dashboardService.create(
      { ...newDashboard },
      authUser.id,
      customerId
    );
  }

  @Post("/favorite")
  @HttpCode(204)
  @HasCustomerRole(Role.ADMIN)
  @ApiConflictResponse({ type: DashboardConflictResDto })
  async favoriteDashboard(
    @AuthUser() authUser: User,
    @Body() favoriteDashboard: FavoriteDashboardDto,
    @Param("customerId") customerId: number
  ): Promise<Number> {
    return await this.dashboardService.favoriteDashboard(
      favoriteDashboard.dashboard_id,
      authUser.id,
      customerId,
      favoriteDashboard.status
    );
  }

  @Get("/migrate/:dashboardId")
  async migrateDashboard(
    @Param("customerId") customerId: number,
    @Param("dashboardId") dashboardId: number
  ) {
    return await this.dashboardService.migrateDashboard(
      customerId,
      dashboardId
    );
  }

  @Get("/sync/:dashboardId")
  async asyncDashboard(
    @Param("customerId") customerId: number,
    @Param("dashboardId") dashboardId: number
  ) {
    return await this.dashboardService.syncDashboard(customerId, dashboardId);
  }

  @Post("/default")
  @HttpCode(204)
  @HasCustomerRole(Role.ADMIN)
  async makeItDefaultDashboard(
    @AuthUser() authUser: User,
    @Body() defaultDashboard: DefaultDashboardCreationDto,
    @Param("customerId") customerId: number
  ) {
    return await this.dashboardService.defaultDashboard(
      defaultDashboard.dashboard_id,
      authUser.id,
      customerId,
      defaultDashboard.status
    );
  }

  @Patch("/:dashboardId")
  @HasCustomerRole(Role.ADMIN)
  @ApiConflictResponse({ type: UpdateDashboardConflictResDto })
  async update(
    @AuthUser() authUser: User,
    @Body() newDashboard: UpdateDashboardDto,
    @Param("dashboardId") dashboardId: number,
    @Param("customerId") customerId: number
  ) {
    let d: Dashboard = (await this.dashboardService.findById(
      dashboardId
    )) as Dashboard;
    if (
      d &&
      d.customer &&
      (!authUser.hasCustomerScope(Number(d.customer.id)) ||
        Number(d.customer.id) != customerId)
    ) {
      throw new ForbiddenException();
    }

    return await this.dashboardService.update(
      { ...newDashboard },
      authUser.id,
      dashboardId,
      customerId
    );
  }

  @Get("/:dashboardId")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasCustomerRole(Role.USER)
  @ApiOkResponse({ type: GetDashboardByIdSuccessResDto })
  @ApiNotFoundResponse({ type: GetDashboardByIdNotFoundResDto })
  @ApiForbiddenResponse({ type: GetDashboardByIdForbiddenResDto })
  async getById(
    @AuthUser() authUser: User,
    @Param("dashboardId") dashboardId: number,
    @Param("customerId") customerId: number
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }
    const d = await this.dashboardService.findDefaultById(
      customerId,
      dashboardId,
      authUser.id
    );
    if (d === null) {
      throw new NotFoundException("Dashboard Not Found.");
    }

    return d;
  }

  @Get()
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @ApiOkResponse({ type: GetAllDashboardSuccessResDto })
  @ApiForbiddenResponse({ type: GetAllDashboardForbiddenResDto })
  @HasCustomerRole(Role.USER)
  async getAllByCustomerId(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: number,
    @Query("search") search: string
  ) {
    const dashboards = await this.dashboardService.findAllByCustomerId(
      customerId,
      authUser.id
    );
    if (dashboards.length > 0 && search) {
      search = search.toLowerCase();
      const filteredDashboards = dashboards.filter((d) => {
        return d.title.toLowerCase().includes(search);
      });
      return { items: filteredDashboards, total: filteredDashboards.length };
    }
    return { items: dashboards, total: dashboards.length };
  }

  @Delete("/:dashboardId")
  @HasCustomerRole(Role.POWER_USER)
  async delete(
    @Param("dashboardId") dashboardId: number,
    @AuthUser() authUser: User,
    @Param("customerId") customerId: number
  ) {
    return this.dashboardService.delete(dashboardId, authUser.id);
  }
}
