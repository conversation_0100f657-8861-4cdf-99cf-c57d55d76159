import { TestingModule } from '@nestjs/testing';
import { assetFixtureFactory } from './asset.fixture';
import {
  AssetTemplateCreationData,
  AssetTemplateService,
} from 'src/asset-template/asset-template.service';
import { MikroORM, RequestContext } from '@mikro-orm/core';
import { UserId } from 'src/users/domain/user.entity';
import { measurementFixtureFactory } from './measurement.fixture';
import { AssetTemplate } from 'src/asset-template/domain/asset-template.entity';

export async function createAssetTemplate(
  testingModule: TestingModule,
  assetTemplateCreationData: AssetTemplateCreationData,
  userId: number,
) {
  const assetTemplateService = testingModule.get(AssetTemplateService);
  const orm = await testingModule.resolve(MikroORM);

  return await RequestContext.createAsync(orm.em, async () => {
    return await assetTemplateService.create(assetTemplateCreationData, userId);
  });
}

export async function deleteAssetTemplateByManufacturer(
  testingModule: TestingModule,
  manufacturer: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(
      AssetTemplate,
      {
        manufacturer,
      },
      { filters: false },
    );
  });
}

export class AssetTemplateFixtureFactory {
  constructor(private readonly testingModule: TestingModule) {}

  async createBikeEngineTemplate(createdById: UserId) {
    const measurementFixture = await measurementFixtureFactory.createMetadata(
      this.testingModule,
    );

    const powerMetricFixture =
      await assetFixtureFactory.createEnginePowerMetric(this.testingModule);

    const measurement: AssetTemplateCreationData['measurements'][0] = {
      metricId: powerMetricFixture.powerMetricId,
      dataTypeId: measurementFixture.realDataTypeId,
      valueTypeId: measurementFixture.nominalValueTypeId,
      locationId: measurementFixture.outputLocationId,
      measurementTypeId: measurementFixture.powerTypeId,
    };

    const bikeEngineTemplate = await createAssetTemplate(
      this.testingModule,
      {
        manufacturer: 'Harvey Davidson',
        modelNumber: 'HD-34',
        assetTypeId: powerMetricFixture.engineAssetTypeId,
        measurements: [measurement],
      },
      createdById,
    );

    return {
      realDataTypeId: measurementFixture.realDataTypeId,
      nominalValueDataTypeId: measurementFixture.nominalValueTypeId,
      outputLocationId: measurementFixture.outputLocationId,
      powerTypeId: measurementFixture.powerTypeId,
      powerMetricId: powerMetricFixture.powerMetricId,
      engineAssetTypeId: powerMetricFixture.engineAssetTypeId,
      bikeEngineTemplateId: bikeEngineTemplate.id,
      cleanUp: async () => {
        await deleteAssetTemplateByManufacturer(
          this.testingModule,
          'Harvey Davidson',
        );
        await powerMetricFixture.cleanUp();
        await measurementFixture.cleanUp();
      },
    };
  }
}
