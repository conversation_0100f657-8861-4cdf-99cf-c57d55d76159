import {
  <PERSON>,
  Controller,
  Get,
  Logger,
  Param,
  <PERSON>,
  Post,
  UseGuards,
} from "@nestjs/common";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { User } from "src/users/domain/user.entity";
import { DiagramService } from "./diagram.service";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { Role } from "src/authorization/domain/customer-user-role.entity";

@Controller({ path: "diagram", version: "0" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class DiagramController {
  constructor(
    private readonly diagramTemplate: DiagramService,
    private readonly logger: Logger
  ) {}
  @Get("/")
  @HasRole(Role.USER)
  async getAll() {
    return this.diagramTemplate.findAll();
  }

  @Get("/:diagramId")
  @HasRole(Role.USER)
  async getById(@Param() { diagramId }: { diagramId: number }) {
    this.logger.log(diagramId);
    return await this.diagramTemplate.findById(diagramId);
  }

  @Post("/")
  @HasRole(Role.POWER_USER)
  async create(
    @Body() { data, name }: { name: string; data: string },
    @AuthUser() authUser: User
  ) {
    this.logger.log(data, name, authUser);
    return this.diagramTemplate.create(
      {
        data,
        name,
      },
      authUser
    );
  }

  @Patch("/:diagramId")
  @HasRole(Role.POWER_USER)
  async update(
    @Param() { diagramId }: { diagramId: number },
    @Body() { data, name }: { name: string; data: string },
    @AuthUser() authUser: User
  ) {
    return await this.diagramTemplate.update(
      { id: diagramId, name, data },
      authUser
    );
  }
}
