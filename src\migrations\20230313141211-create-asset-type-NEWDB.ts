import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230313141211 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset_type" ("id" serial primary key, "created" timestamptz(6) null, \
      "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, \
      "parent_type" int null, "name" varchar(50) null);',
    );
    this.addSql(
      'alter table "asset_type" add constraint "asset_type_name_parent_type_key" unique ("name", "parent_type");',
    );

    this.addSql(
      'alter table "asset_type" add constraint "parent_fk" foreign key ("parent_type") references "asset_type" ("id") on update cascade on delete set null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "asset_type" drop constraint "parent_fk";');

    this.addSql('drop table if exists "asset_type" cascade;');
  }
}
