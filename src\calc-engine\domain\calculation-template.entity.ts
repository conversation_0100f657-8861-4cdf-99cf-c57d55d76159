
import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { DataType } from '../../measurements/domain/data-type.entity';

@Entity()
export class CalculationTemplate {

  @PrimaryKey()
  id!: number;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;

  @Unique({ name: 'calculation_name_unique' })
  @Property({ length: 50 })
  name!: string;

  @Property({ length: 150 })
  expression!: string;

  @Property({ length: 150, nullable: true })
  description?: string;

  @Property({ length: 150, nullable: true })
  importsList?: string;

  @ManyToOne({ entity: () => DataType, fieldName: 'data_type' })
  dataType!: DataType;

}