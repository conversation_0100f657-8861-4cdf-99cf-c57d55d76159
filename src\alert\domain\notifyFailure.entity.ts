import {<PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property} from "@mikro-orm/core";
import {User} from "../../users/domain/user.entity";
import {Alerts} from "./alert.entity";

@Entity()
export class NotifyFailure {

    @PrimaryKey()
    id!: number;

    @ManyToOne({entity: () => Alerts, fieldName: 'alert', onDelete: 'cascade', nullable: true})
    alert?: Alerts;

    @ManyToOne({entity: () => User, fieldName: 'user', nullable: true})
    user?: User;

    @Property({nullable: true})
    alertState?: boolean;

    @Property({length: 20, nullable: true})
    value?: string;

    @Property({nullable: true})
    type?: number;

    @Property({nullable: true})
    reason?: number;

}
