import { Injectable } from "@nestjs/common";
import mail from "@sendgrid/mail";
import { TwilioService } from "nestjs-twilio";
import { Twi<PERSON> } from "twilio";
@Injectable()
export class TwillioService {
  private twillioClient: Twilio;
  public constructor(private readonly twilioService: TwilioService) {
    this.twillioClient = new Twilio(
      process.env.TWILLIO_ACCOUNT_SID,
      process.env.TWILLIO_AUTH_TOKEN
    );
  }
  public setMessage(message: string) {
    return `Limit Alert
    At: 2023-10-05T14:30:00 UTC
    Asset: AssetTag123
    Measurement: MeasurementTag456
    CurrentValue: 75.0
    Settings:
    Threshold: GE 70.0
    Return to Normal: LE 65.0`;
  }
  async sendSMS(message: string, toPhoneNumber: string) {
    const msg = this.twilioService.client.messages.create({
      body: message || this.setMessage(message),
      from: process.env.SMS_FROM_NO,
      to: toPhone<PERSON>umber,
    });
  }
  async sendEmail(
    emailAddresses: string[],
    message: string,
    subject: string,
    html: string
  ) {
    mail.setApiKey(process.env.TWILLIO_EMAIL_API_TOKEN);
    const email = await mail.send({
      to: emailAddresses,
      from: process.env.EMAIL_FROM,
      subject: subject,
      text: message,
      html: html,
    });
  }
}
