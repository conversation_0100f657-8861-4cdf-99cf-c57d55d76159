import { HierarchyGraphService } from './hierarchy-graph.service';

const expectArraysToHaveSameItems = <T>(params: {
  expected: T[];
  received: T[];
}) => {
  const { expected, received } = params;

  expect(Array.isArray(received)).toBeTruthy();
  expect(Array.isArray(expected)).toBeTruthy();
  expect(received.length).toBe(expected.length);
  expect(received).toEqual(expect.arrayContaining(expected));
};

describe('HierarchyGraphService', () => {
  let hierarchyGraphService: HierarchyGraphService;
  beforeEach(() => {
    hierarchyGraphService = new HierarchyGraphService();
  });

  describe('mapDbRowsToHierarchy', () => {
    let result: ReturnType<typeof hierarchyGraphService.mapDbRowsToHierarchy>;

    describe('given single null parent root row', () => {
      beforeAll(() => {
        const hierarchyGraphService = new HierarchyGraphService();
        result = hierarchyGraphService.mapDbRowsToHierarchy([
          { id: 44, parent: null, child: 7 },
        ]);
      });

      it('should return parent map with -1', () => {
        expect(result.parentChildrenMap.size).toBe(1);
        expect(result.parentChildrenMap.has(-1)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [{ linkId: 44, nodeId: 7 }],
          received: result.parentChildrenMap.get(-1),
        });
      });

      it('should return child map with root', () => {
        expect(result.childParentsMap.size).toBe(1);
        expect(result.childParentsMap.has(7)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [-1],
          received: result.childParentsMap.get(7),
        });
      });
    });

    describe('given multiple parent root rows', () => {
      const parentAndLinkIds = [
        [70, 44],
        [80, 45],
        [90, 46],
      ];
      beforeAll(() => {
        const hierarchyGraphService = new HierarchyGraphService();
        result = hierarchyGraphService.mapDbRowsToHierarchy([
          { id: 44, parent: 70, child: 7 },
          { id: 45, parent: 80, child: 7 },
          { id: 46, parent: 90, child: 7 },
        ]);
      });

      it('should return parent map with each root parent', () => {
        expect(result.parentChildrenMap.size).toBe(3);
        for (const [parentId, linkId] of parentAndLinkIds) {
          expect(result.parentChildrenMap.has(parentId)).toBeTruthy();
          expectArraysToHaveSameItems({
            expected: [{ linkId: linkId, nodeId: 7 }],
            received: result.parentChildrenMap.get(parentId),
          });
        }
      });

      it('should return child map with root containing all parents', () => {
        expect(result.childParentsMap.size).toBe(1);
        expect(result.childParentsMap.has(7)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [70, 80, 90],
          received: result.childParentsMap.get(7),
        });
      });
    });

    describe('given root -> children rows', () => {
      beforeAll(() => {
        const hierarchyGraphService = new HierarchyGraphService();
        result = hierarchyGraphService.mapDbRowsToHierarchy([
          { id: 44, parent: null, child: 7 },
          { id: 45, parent: 7, child: 8 },
          { id: 46, parent: 7, child: 9 },
        ]);
      });

      it('should return parent map with -1 and root', () => {
        expect(result.parentChildrenMap.size).toBe(2);
        expect(result.parentChildrenMap.has(-1)).toBeTruthy();
        expect(result.parentChildrenMap.has(7)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [
            { linkId: 45, nodeId: 8 },
            { linkId: 46, nodeId: 9 },
          ],
          received: result.parentChildrenMap.get(7),
        });
      });

      it('should return child map with root children containing it', () => {
        expect(result.childParentsMap.size).toBe(3);
        expect(result.childParentsMap.has(7)).toBeTruthy();
        expect(result.childParentsMap.has(8)).toBeTruthy();
        expect(result.childParentsMap.has(9)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [7],
          received: result.childParentsMap.get(8),
        });
        expectArraysToHaveSameItems({
          expected: [7],
          received: result.childParentsMap.get(9),
        });
      });
    });

    describe('given root -> child <- other parent', () => {
      beforeAll(() => {
        const hierarchyGraphService = new HierarchyGraphService();
        result = hierarchyGraphService.mapDbRowsToHierarchy([
          { id: 44, parent: null, child: 7 },
          { id: 45, parent: 7, child: 8 },
          { id: 46, parent: 70, child: 8 },
        ]);
      });

      it('should return parent map with -1 and both parents', () => {
        expect(result.parentChildrenMap.size).toBe(3);
        expect(result.parentChildrenMap.has(-1)).toBeTruthy();
        expect(result.parentChildrenMap.has(7)).toBeTruthy();
        expect(result.parentChildrenMap.has(70)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [{ linkId: 45, nodeId: 8 }],
          received: result.parentChildrenMap.get(7),
        });
        expectArraysToHaveSameItems({
          expected: [{ linkId: 46, nodeId: 8 }],
          received: result.parentChildrenMap.get(70),
        });
      });

      it('should return child map with root and child', () => {
        expect(result.childParentsMap.size).toBe(2);
        expect(result.childParentsMap.has(7)).toBeTruthy();
        expect(result.childParentsMap.has(8)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [-1],
          received: result.childParentsMap.get(7),
        });
        expectArraysToHaveSameItems({
          expected: [7, 70],
          received: result.childParentsMap.get(8),
        });
      });
    });

    describe('given root -> child -> grandchild', () => {
      beforeAll(() => {
        const hierarchyGraphService = new HierarchyGraphService();
        result = hierarchyGraphService.mapDbRowsToHierarchy([
          { id: 44, parent: null, child: 7 },
          { id: 45, parent: 7, child: 8 },
          { id: 46, parent: 8, child: 9 },
        ]);
      });

      it('should return parent map with root and child', () => {
        expect(result.parentChildrenMap.size).toBe(3);
        expect(result.parentChildrenMap.has(-1)).toBeTruthy();
        expect(result.parentChildrenMap.has(7)).toBeTruthy();
        expect(result.parentChildrenMap.has(8)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [{ linkId: 45, nodeId: 8 }],
          received: result.parentChildrenMap.get(7),
        });
        expectArraysToHaveSameItems({
          expected: [{ linkId: 46, nodeId: 9 }],
          received: result.parentChildrenMap.get(8),
        });
      });

      it('should return child map with root, child and grandchild', () => {
        expect(result.childParentsMap.size).toBe(3);
        expect(result.childParentsMap.has(7)).toBeTruthy();
        expect(result.childParentsMap.has(8)).toBeTruthy();
        expect(result.childParentsMap.has(9)).toBeTruthy();
        expectArraysToHaveSameItems({
          expected: [-1],
          received: result.childParentsMap.get(7),
        });
        expectArraysToHaveSameItems({
          expected: [7],
          received: result.childParentsMap.get(8),
        });
        expectArraysToHaveSameItems({
          expected: [8],
          received: result.childParentsMap.get(9),
        });
      });
    });
  });

  describe('removeHierarchy', () => {
    test('given single root node it should return node and root link id', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([[-1, [{ linkId: 43, nodeId: 7 }]]]),
        new Map([[7, [-1]]]),
      );

      expectArraysToHaveSameItems({ expected: [43], received: result.linkIds });
      expectArraysToHaveSameItems({ expected: [7], received: result.nodeIds });
    });

    test('given single root node with multiple parents it should return node and all root link ids', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [6, [{ linkId: 43, nodeId: 7 }]],
          [5, [{ linkId: 44, nodeId: 7 }]],
        ]),
        new Map([[7, [5, 6]]]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({ expected: [7], received: result.nodeIds });
    });

    test('given root node with single child it should return root, child node and link ids', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [7, [{ linkId: 44, nodeId: 8 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7, 8],
        received: result.nodeIds,
      });
    });

    test('given root node with child having unreachable parent it should return root and child link ids but only root asset id', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [7, [{ linkId: 44, nodeId: 8 }]],
          // unreachable parent
          [70, [{ linkId: 440, nodeId: 8 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7, 70]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7],
        received: result.nodeIds,
      });
    });

    test('given root with grandchild, it should return all node and link ids down to the grandchild', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [7, [{ linkId: 44, nodeId: 8 }]],
          [8, [{ linkId: 45, nodeId: 9 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7]],
          [9, [8]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44, 45],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7, 8, 9],
        received: result.nodeIds,
      });
    });

    test('given diamond hierarchy, it should return all node and link ids down to the grandchild', () => {
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [
            7,
            [
              { linkId: 44, nodeId: 8 },
              { linkId: 45, nodeId: 9 },
            ],
          ],
          [8, [{ linkId: 46, nodeId: 10 }]],
          [9, [{ linkId: 46, nodeId: 10 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7]],
          [9, [7]],
          [10, [8, 9]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44, 45, 46],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7, 8, 9, 10],
        received: result.nodeIds,
      });
    });

    test('given diamond hierarchy with un-even right parent, it should return all node and link ids down to the grandchild', () => {
      //    7
      //   / \
      //  /   9
      // 8     \
      // \     10
      //  \    /
      //   \  /
      //    11
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [
            7,
            [
              { linkId: 44, nodeId: 8 },
              { linkId: 45, nodeId: 9 },
            ],
          ],
          [8, [{ linkId: 46, nodeId: 11 }]],
          [9, [{ linkId: 47, nodeId: 10 }]],
          [10, [{ linkId: 48, nodeId: 11 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7]],
          [9, [7]],
          [10, [9]],
          [11, [8, 10]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44, 45, 46, 47, 48],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7, 8, 9, 10, 11],
        received: result.nodeIds,
      });
    });

    test('given diamond hierarchy with un-even left parent, it should return all node and link ids down to the grandchild', () => {
      //     7
      //    / \
      //   8   \
      //  /     \
      // 10      9
      // \      /
      //  \    /
      //   \  /
      //    11
      const result = hierarchyGraphService.findCascadeRemoveIds(
        7,
        new Map([
          [-1, [{ linkId: 43, nodeId: 7 }]],
          [
            7,
            [
              { linkId: 44, nodeId: 8 },
              { linkId: 45, nodeId: 9 },
            ],
          ],
          [8, [{ linkId: 46, nodeId: 10 }]],
          [9, [{ linkId: 47, nodeId: 11 }]],
          [10, [{ linkId: 48, nodeId: 11 }]],
        ]),
        new Map([
          [7, [-1]],
          [8, [7]],
          [9, [7]],
          [10, [8]],
          [11, [9, 10]],
        ]),
      );

      expectArraysToHaveSameItems({
        expected: [43, 44, 45, 46, 47, 48],
        received: result.linkIds,
      });
      expectArraysToHaveSameItems({
        expected: [7, 8, 9, 10, 11],
        received: result.nodeIds,
      });
    });
  });
});
