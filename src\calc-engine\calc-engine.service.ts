import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { CustomerId } from "src/customers/domain/customer.entity";
import { User } from "src/users/domain/user.entity";
import { CalculationTemplate } from "./domain/calculation-template.entity";
import {
  CalcGetAllInstanceDto,
  CalcInstanceDto,
} from "./dto/calc-inputs-create.dto";
import { CalcEngineRepository } from "./repository/calc-engine.repository";

@Injectable()
export class CalcEngineService {
  constructor(private readonly calEngineRepository: CalcEngineRepository) {}

  async getAllCalcTemplates() {
    return await this.calEngineRepository.findAllCalculationTemplates();
  }

  async createCalcTemplate(
    expressionTemplate: CalculationTemplate,
    authUser: User
  ) {
    if (
      !(await this.calEngineRepository.findCalcTemplateByName(
        expressionTemplate.name
      ))
    )
      return await this.calEngineRepository.createCalcTemplate(
        expressionTemplate,
        authUser
      );
  }

  async updateCalcTemplate(
    templateId: number,
    expressionTemplate: CalculationTemplate,
    authUser: User
  ) {
    return await this.calEngineRepository.updateCalcTemplate(
      templateId,
      expressionTemplate,
      authUser
    );
  }

  async getAllDataTypes() {
    return await this.calEngineRepository.getAllDataTypes();
  }

  async getAllTimePeriods() {
    return await this.calEngineRepository.findAllCalculationTimePeriods();
  }

  async createCalcInputs(
    constantInputs: CalcInstanceDto,
    authUser: User,
    headers: Request["headers"],
    customerId
  ) {
    return await this.calEngineRepository.createCalcInputs(
      constantInputs,
      authUser,
      headers,
      customerId
    );
  }

  async getAllInstances(
    customerId: CustomerId,
    calcInstanceDto: CalcGetAllInstanceDto
  ) {
    return await this.calEngineRepository.findAllCalcInstances(
      customerId,
      calcInstanceDto
    );
  }
  async updateCalcInputs(
    instanceId: number,
    constantInputs: CalcInstanceDto,
    authUser: User,
    customerId:CustomerId,
    headers: Request["headers"]
  ) {
    return await this.calEngineRepository.updateCalcInputs(
      instanceId,
      constantInputs,
      authUser,
      customerId,
      headers
    );
  }
  async getCalculationByMesurementId(id: number) {
    try {
      const calcInstance =
        await this.calEngineRepository.findCalcInstanceByMesurementId(id);
      if (!calcInstance) throw new Error("Calculation not found");
      let calcTemplate = await this.calEngineRepository.findCalcTemplateById(
        calcInstance.calculation.id
      );
      if (!calcTemplate) throw new Error("Calculation template not found");
      const calcInputs =
        await this.calEngineRepository.findCalcInputsByCalcInstancetId(
          calcInstance.id
        );
      return {
        calcInstance,
        calcTemplate,
        calcInputs,
      };
    } catch (error: any) {
      console.log(error);
      throw new InternalServerErrorException(error.message);
    }
  }
}
