import { TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { setupApp, loginUser } from './test-utils';
import {
  UserFixture,
  deleteUser,
  userFixtureFactory,
} from './fixtures/user.fixture';
import { customerFixtureFactory } from './fixtures/customer.fixture';

describe('User API endpoints', () => {
  let app: INestApplication;
  let testingModule: TestingModule;
  let userFixture: UserFixture;

  beforeAll(async () => {
    ({ testingModule, app } = await setupApp());

    userFixture = await userFixtureFactory.createSuperUser(testingModule);
  });

  afterAll(async () => {
    await userFixture.cleanUp();
    await app.close();
  });

  describe('/users', () => {
    describe('POST new global admin user', () => {
      describe('with correct credentials', () => {
        let response: request.Response;

        beforeAll(async () => {
          await deleteUser(testingModule, 'new_user');

          const httpClient = await loginUser(
            app.getHttpServer(),
            'testuser',
            'testpassword',
          );

          response = await httpClient.post('/v0/users').send({
            username: 'new_user',
            first_name: 'New',
            last_name: 'User',
            email: '<EMAIL>',
            global_role: 'ADMIN',
            password: 'herpderp',
          });
        });

        afterAll(async () => {
          await deleteUser(testingModule, 'new_user');
        });

        it('should return a 201', () => {
          expect(response.statusCode).toBe(201);
        });

        it('should return new user with an id', () => {
          expect(response.body.id).not.toBeUndefined();
          expect(response.body.id).not.toBeNull();
        });

        it('should return new user with given fields', () => {
          expect(response.body).toMatchObject({
            username: 'new_user',
            first_name: 'New',
            last_name: 'User',
            email: '<EMAIL>',
            global_role: 'ADMIN',
          });
        });

        it('should return only visible fields', () =>
          expect(new Set(Object.keys(response.body))).toEqual(
            new Set([
              'id',
              'username',
              'first_name',
              'last_name',
              'email',
              'global_role',
              'scoped_roles',
              'enabled',
            ]),
          ));
      });
    });

    describe('POST new scoped admin user', () => {
      let response: request.Response;
      let customerFixture;

      beforeAll(async () => {
        customerFixture = await customerFixtureFactory.createKawasaki(
          testingModule,
          userFixture.superUserId,
        );

        const httpClient = await loginUser(
          app.getHttpServer(),
          'testuser',
          'testpassword',
        );

        response = await httpClient.post('/v0/users').send({
          username: 'new_scoped_admin',
          first_name: 'New',
          last_name: 'User',
          email: '<EMAIL>',
          password: 'herpderp',
          scoped_roles: [
            { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
          ],
        });
      });

      afterAll(async () => {
        await deleteUser(testingModule, 'new_scoped_admin');
        await customerFixture.cleanUp();
      });

      it('should return a 201', () => {
        expect(response.statusCode).toBe(201);
      });

      it('should return new user with given fields', () => {
        expect(response.body).toMatchObject({
          username: 'new_scoped_admin',
          first_name: 'New',
          last_name: 'User',
          email: '<EMAIL>',
          global_role: null,
          scoped_roles: [
            { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
          ],
        });
      });
    });

    describe('/me', () => {
      describe('GET', () => {
        test('with no credentials should return unauthorized', () => {
          return request(app.getHttpServer()).get('/v0/users/me').expect(401);
        });

        test('with credentials but no CSRF token should return unauthorized', async () => {
          const httpClient = request.agent(app.getHttpServer());
          await httpClient
            .post('/v0/sessions')
            .send({ username: 'testuser', password: 'testpassword' });

          return httpClient.get('/v0/users/me').expect(401);
        });

        describe('with correct credentials', () => {
          let response: request.Response;

          beforeAll(async () => {
            const httpClient = await loginUser(
              app.getHttpServer(),
              'testuser',
              'testpassword',
            );
            response = await httpClient.get('/v0/users/me');
          });

          it('should return 200', () => {
            expect(response.statusCode).toBe(200);
          });

          it('should return user details', () => {
            expect(response.body).toMatchObject({
              username: 'testuser',
              first_name: 'f',
              last_name: 'l',
              global_role: 'ADMIN',
              id: userFixture.superUserId,
              email: '<EMAIL>',
              scoped_roles: [],
            });
          });
        });
      });

      describe('PATCH', () => {
        let httpClient: request.SuperAgentTest;
        let response: request.Response;
        let customerFixture;

        beforeAll(async () => {
          httpClient = await loginUser(
            app.getHttpServer(),
            'testuser',
            'testpassword',
          );

          customerFixture = await customerFixtureFactory.createKawasaki(
            testingModule,
            userFixture.superUserId,
          );

          await httpClient.post('/v0/users').send({
            username: 'admin_to_patch',
            first_name: 'New',
            last_name: 'User',
            email: '<EMAIL>',
            password: 'herpderp',
            scoped_roles: [
              { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
            ],
          });

          httpClient = await loginUser(
            app.getHttpServer(),
            'admin_to_patch',
            'herpderp',
          );

          response = await httpClient.patch(`/v0/users/me`).send({
            first_name: 'Pablo',
            last_name: 'Honey',
          });
        });

        afterAll(async () => {
          await deleteUser(testingModule, 'admin_to_patch');
          await customerFixture.cleanUp();
        });

        it('should return 204', () => {
          expect(response.statusCode).toBe(204);
        });

        it('should update user', async () => {
          const user = (await httpClient.get(`/v0/users/me`)).body;

          expect(user.first_name).toBe('Pablo');
          expect(user.last_name).toBe('Honey');
        });
      });
    });
  });

  describe('/customers/:customerId/users', () => {
    let response: request.Response;
    let customerFixture;

    beforeAll(async () => {
      customerFixture = await customerFixtureFactory.createKawasaki(
        testingModule,
        userFixture.superUserId,
      );

      const httpClient = await loginUser(
        app.getHttpServer(),
        'testuser',
        'testpassword',
      );

      await httpClient.post('/v0/users').send({
        username: 'new_scoped_admin',
        first_name: 'New',
        last_name: 'User',
        email: '<EMAIL>',
        password: 'herpderp',
        scoped_roles: [
          { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
        ],
      });

      response = await httpClient.get(
        `/v0/customers/${customerFixture.customerId}/users`,
      );
    });

    afterAll(async () => {
      await deleteUser(testingModule, 'new_scoped_admin');
      await customerFixture.cleanUp();
    });

    describe('GET', () => {
      it('should return 200', () => {
        expect(response.statusCode).toBe(200);
      });

      it('should return a collection with one element', () => {
        // default super user, fixture super user and scoped admin
        expect(response.body.total).toBe(1);
        expect(response.body.items.length).toBe(1);
      });

      describe('logged as scoped admin', () => {
        beforeAll(async () => {
          const httpClient = await loginUser(
            app.getHttpServer(),
            'new_scoped_admin',
            'herpderp',
          );

          response = await httpClient.get(
            `/v0/customers/${customerFixture.customerId}/users`,
          );
        });

        it('should return 200', () => {
          expect(response.statusCode).toBe(200);
        });

        it('should just return a collection with one element', () => {
          expect(response.body.total).toBe(1);
          expect(response.body.items.length).toBe(1);
        });

        it('should return scoped admin details', () => {
          expect(response.body.items[0]).toMatchObject({
            username: 'new_scoped_admin',
            first_name: 'New',
            last_name: 'User',
            email: '<EMAIL>',
            scoped_roles: [
              { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
            ],
          });
        });
      });
    });

    describe('PATCH', () => {
      let httpClient: request.SuperAgentTest;

      beforeAll(async () => {
        httpClient = await loginUser(
          app.getHttpServer(),
          'testuser',
          'testpassword',
        );

        const scopedAdminId = (
          await httpClient.post('/v0/users').send({
            username: 'admin_to_patch',
            first_name: 'New',
            last_name: 'User',
            email: '<EMAIL>',
            password: 'herpderp',
            scoped_roles: [
              { role: 'ADMIN', customer_ids: [customerFixture.customerId] },
            ],
          })
        ).body.id;

        response = await httpClient
          .patch(
            `/v0/customers/${customerFixture.customerId}/users/${scopedAdminId}`,
          )
          .send({
            first_name: 'Pablo',
            last_name: 'Honey',
            scoped_roles: [
              {
                role: 'USER',
                customer_ids: [customerFixture.customerId],
              },
            ],
          });
      });

      afterAll(async () => {
        await deleteUser(testingModule, 'admin_to_patch');
      });

      it('should return 204', () => {
        expect(response.statusCode).toBe(204);
      });

      it('should update user', async () => {
        const user = (
          await httpClient.get(
            `/v0/customers/${customerFixture.customerId}/users`,
          )
        ).body.items.find((user) => user.username === 'admin_to_patch');

        expect(user.first_name).toBe('Pablo');
        expect(user.last_name).toBe('Honey');
        expect(user.global_role).toBeUndefined();
        expect(user.scoped_roles).toStrictEqual([
          { role: 'USER', customer_ids: [customerFixture.customerId] },
        ]);
      });
    });
  });
});
