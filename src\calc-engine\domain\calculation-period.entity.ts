
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Property } from '@mikro-orm/core';

@Entity()
export class CalculationPeriod {

  @PrimaryKey()
  id!: number;

  @Property({ length: 20 })
  value!: string;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;

}