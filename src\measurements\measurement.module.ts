import { MikroOrmModule } from "@mikro-orm/nestjs";
import { HttpModule } from "@nestjs/axios";
import { Logger, Module } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { AssetModule } from "src/assets/asset.module";
import { AuthModule } from "src/authentication/auth.module";
import { CustomersModule } from "src/customers/customers.module";
import { TransactionFactory } from "src/db/TransactionFactory";
import {
  TIMESERIES_CONFIG,
  TimeSeriesConfig,
} from "src/timeseries/timeseries.config";
import { TimeseriesModule } from "src/timeseries/timeseries.module";
import { AssetMeasurementApiController } from "./asset-measurement.controller";
import { AssetMeasurementService } from "./asset-measurement.service";
import { AssetMeasurementSubscriber } from "./asset-measurement.subscriber";
import { AssetMeasurementsListController } from "./asset-measurements-list.controller";
import { DatasourceService } from "./datasource.service";
import { AssetMeasurement } from "./domain/asset-measurement.entity";
import { DataType } from "./domain/data-type.entity";
import { Datasource } from "./domain/datasource.entity";
import { Location } from "./domain/location.entity";
import { MeasurementType } from "./domain/measurement-type.entity";
import { Measurement } from "./domain/measurement.entity";
import { UnitOfMeasure } from "./domain/unit-of-measure.entity";
import { UnitsGroupUnit } from "./domain/units-group-unit.entity";
import { UnitsGroup } from "./domain/units-group.entity";
import { ValueType } from "./domain/value-type.entity";
import { MeasurementsMetricsController } from "./measurement-metrics.controller";
import { MeasurementsBackofficeApiController } from "./measurements-backoffice.controller";
import { MeasurementsBackofficeService } from "./measurements-backoffice.service";
import { AssetMeasurementRepository } from "./repository/asset-measurement.repository";
import { UnitsGroupUnitRepository } from "./repository/units-group-unit.repository";
import { UnitsGroupRepository } from "./repository/units-group.repository";
import { TimeSeriesService } from "./time-series.service";
import { UnitOfMeasureService } from "./unit-of-measure.service";
import { UnitsGroupApiController } from "./units-group.controller";
import { UnitsGroupService } from "./units-group.service";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      DataType,
      ValueType,
      MeasurementType,
      UnitOfMeasure,
      UnitsGroup,
      UnitsGroupUnit,
      Measurement,
      Location,
      AssetMeasurement,
      Datasource,
    ]),
    AuthModule,
    CustomersModule,
    AssetModule,
    HttpModule,
    TimeseriesModule,
  ],
  providers: [
    MeasurementsBackofficeService,
    DatasourceService,
    UnitOfMeasureService,
    AssetMeasurementService,
    TimeSeriesService,
    AssetMeasurementRepository,
    AssetMeasurementSubscriber,
    UnitsGroupService,
    UnitsGroupRepository,
    UnitsGroupUnitRepository,
    TransactionFactory,
    {
      provide: Logger,
      useValue: new Logger(AssetMeasurementService.name),
    },
    {
      provide: TIMESERIES_CONFIG,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.get<TimeSeriesConfig>(TIMESERIES_CONFIG),
    },
  ],
  controllers: [
    MeasurementsBackofficeApiController,
    UnitsGroupApiController,
    AssetMeasurementsListController,
    AssetMeasurementApiController,
    MeasurementsMetricsController,
  ],
  exports: [
    AssetMeasurementService,
    MeasurementsBackofficeService,
    UnitsGroupService,
    DatasourceService,
    TransactionFactory,
    MikroOrmModule.forFeature([DataType, MeasurementType, ValueType]),
  ],
})
export class MeasurementModule {}
