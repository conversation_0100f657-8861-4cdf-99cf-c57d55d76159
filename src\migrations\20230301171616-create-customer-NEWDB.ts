import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230301171616NewDb extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "customer" ("id" serial primary key, "name" varchar(50) not null, \
      "address" varchar(150) not null, "enabled" boolean null, "created" timestamptz(6) null, \
      "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "customer" add constraint "customer_name_key" unique ("name");',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "customer" cascade;');
  }
}
