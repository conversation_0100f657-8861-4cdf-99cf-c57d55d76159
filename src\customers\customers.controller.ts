import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  HttpException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { User } from "src/users/domain/user.entity";
import { CustomerService } from "./customer.service";
import { Customer, CustomerId } from "./domain/customer.entity";
import {
  CustomerCreationDto,
  CustomerUpdateDto,
  GetAllCustomersDto,
} from "./dto/customer-creation.dto";

@Controller({ version: "0", path: "customers" })
@UseGuards(JwtAuthGuard, CsrfGuard)
export class CustomersApiController {
  constructor(private readonly customerService: CustomerService) {}

  @Post()
  @HasCustomerRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Body() newCustomer: CustomerCreationDto
  ) {
    return await this.customerService.create(
      { ...newCustomer, nameId: newCustomer.name_id },
      authUser.id
    );
  }

  @Patch("/:customerId")
  @HttpCode(204)
  @HasCustomerRole(Role.POWER_USER)
  async updateCustomer(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: CustomerId,
    @Body() customer: CustomerUpdateDto
  ) {
    return await this.customerService.update(
      customerId,
      { ...customer },
      authUser.id
    );
  }

  @Get()
  async getAll(@AuthUser() authUser: User, @Query() query: GetAllCustomersDto) {
    let customers: Customer[];

    if (authUser.hasGlobalRole()) {
      customers = await this.customerService.getAll(query);
    } else {
      customers = await this.customerService.getAllById(
        authUser.customerScope(),
        query
      );
    }

    return { items: customers, total: customers.length };
  }

  @Get("/:customerNameId")
  async getByNameId(
    @AuthUser() authUser: User,
    @Param("customerNameId") customerNameId: string
  ) {
    const customer = await this.customerService.findByNameId(customerNameId);

    if (customer === null) {
      throw new NotFoundException();
    } else if (!authUser.hasCustomerScope(customer.id)) {
      throw new ForbiddenException();
    }

    return customer;
  }

  @Get("/:customerId/logo")
  async getLogoById(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: number
  ) {
    if (!customerId) {
      throw new NotFoundException();
    } else if (!authUser.hasCustomerScope(customerId)) {
      throw new ForbiddenException();
    }
    const c = await this.customerService.getCustomerWithLogo(customerId);
    if (c === null) {
      throw new NotFoundException();
    }
    return { logo: c.logo };
  }
}
