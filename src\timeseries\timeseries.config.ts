import { registerAs } from "@nestjs/config";

export const TIMESERIES_CONFIG = "timeseries";

export const timeSeriesConfig = registerAs(TIMESERIES_CONFIG, () => ({
  host: process.env.TS_API_HOST,
  port: process.env.TS_API_PORT ? parseInt(process.env.TS_API_PORT) : 8000,
  ssl: process.env.TS_API_SSL?.toLowerCase() === "true",
  version: process.env.TS_API_VERSION,
  version_v2: process.env.TS_API_VERSION_V2,
  fast_api: process.env.FAST_API,
}));

export type TimeSeriesConfig = ReturnType<typeof timeSeriesConfig>;
