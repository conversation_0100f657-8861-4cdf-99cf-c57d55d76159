import { ApiProperty } from '@nestjs/swagger';
import { CustomerCreationData } from '../customer.service';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

type CustomerCreationDtoType = Omit<CustomerCreationData, 'nameId'> & {
  name_id: string;
};

export class CustomerCreationDto implements CustomerCreationDtoType {
  name_id!: string;
  name!: string;
  address!: string;
  logo?: string;
}

export class GetAllCustomersDto {
  @ApiProperty({ required: true, type: 'string', example: 'true' })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  is_logo?: string;
}


export class CustomerUpdateDto {
  @ApiProperty({ required: true, type: 'string', example: 'Brompton Energy Inc.' })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({ required: true, type: 'string', example: 'Houston, Tx' })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  address?: string;

  @ApiProperty({ required: true, type: 'string', example: 'logo' })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  logo?: string;
}
