import { MikroORM, RequestContext } from '@mikro-orm/core';
import { TestingModule } from '@nestjs/testing';
import { UnitsGroupUnit } from 'src/measurements/domain/units-group-unit.entity';
import { UnitsGroup } from 'src/measurements/domain/units-group.entity';
import { MeasurementTypeId } from 'src/measurements/domain/measurement-type.entity';
import { UnitOfMeasure } from 'src/measurements/domain/unit-of-measure.entity';

export const createUnitOfMeasure = async (
  testingModule: TestingModule,
  name: string,
  measurementTypeId: MeasurementTypeId,
) => {
  const orm = await testingModule.resolve(MikroORM);
  return await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();
    if (!em) {
      throw new Error('No entity manager available');
    }
    const newUnitOfMeasure = em.create(UnitOfMeasure, {
      name,
      measurementType: measurementTypeId,
    });
    await em.persistAndFlush(newUnitOfMeasure);
    return newUnitOfMeasure.id;
  });
};

export async function deleteUnitOfMeasureByName(
  testingModule: TestingModule,
  name: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(
      UnitOfMeasure,
      {
        name,
      },
      { filters: false },
    );
  });
}

export const createUnitsGroupWithUnits = async (
  testingModule: TestingModule,
  name: string,
  unitOfMeasureIds: number[],
) => {
  const orm = await testingModule.resolve(MikroORM);
  return await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();
    if (!em) {
      throw new Error('No entity manager available');
    }
    const newUnitsGroup = em.create(UnitsGroup, { name, isBase: false });

    await Promise.all(
      unitOfMeasureIds.map(async (unitOfMeasureId, index) => {
        const newUnitsGroupUnit = em.create(UnitsGroupUnit, {
          unitOfMeasure: unitOfMeasureId,
          unitsGroup: newUnitsGroup,
        });

        if (index === 0) {
          newUnitsGroupUnit.unitsGroupDefaultUnit.add(newUnitsGroup);
        }

        await em.persistAndFlush(newUnitsGroupUnit);
      }),
    );
    return newUnitsGroup.id;
  });
};

export async function deleteUnitsGroupByName(
  testingModule: TestingModule,
  name: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(
      UnitsGroup,
      {
        name,
      },
      { filters: false },
    );
  });
}

export type UnitsGroupEuropeFixture = Awaited<
  ReturnType<UnitsGroupFixtureFactory['createEuropeUnitsGroup']>
>;

export class UnitsGroupFixtureFactory {
  constructor(private readonly testingModule: TestingModule) {}

  async createEuropeUnitsGroup(powerMeasurementTypeId: number) {
    const wattUnitOfMeasureId = await createUnitOfMeasure(
      this.testingModule,
      'watt',
      powerMeasurementTypeId,
    );

    const jouleUnitOfMeasureId = await createUnitOfMeasure(
      this.testingModule,
      'joule',
      powerMeasurementTypeId,
    );

    const europeUnitsGroupId = await createUnitsGroupWithUnits(
      this.testingModule,
      'eu',
      [wattUnitOfMeasureId, jouleUnitOfMeasureId],
    );

    return {
      europeUnitsGroupId,
      wattUnitOfMeasureId,
      jouleUnitOfMeasureId,
      cleanUp: async () => {
        await deleteUnitsGroupByName(this.testingModule, 'eu');
        await deleteUnitOfMeasureByName(this.testingModule, 'watt');
        await deleteUnitOfMeasureByName(this.testingModule, 'joule');
      },
    };
  }
}
