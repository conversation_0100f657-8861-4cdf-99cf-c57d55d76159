import { Migration } from '@mikro-orm/migrations';

export class Migration20250116112107_updateDashboardDataField extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "diagram" alter column "data" type text using ("data"::text);');

    this.addSql('alter table "dashboard" alter column "data" type text using ("data"::text);');
  }

  async down(): Promise<void> {
    this.addSql('alter table "diagram" alter column "data" type varchar(65536) using ("data"::varchar(65536));');

    this.addSql('alter table "dashboard" alter column "data" type varchar(65536) using ("data"::varchar(65536));');
  }

}
