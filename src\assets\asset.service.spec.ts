import { Test } from '@nestjs/testing';
import { CustomerService } from 'src/customers/customer.service';
import { AssetTypeService } from './asset-type.service';
import { AssetService } from './asset.service';
import { TimeZoneService } from './timezone.service';
import { AssetRepository } from './repository/asset.repository';
import { AssetHierarchyService } from './asset-hierarchy.service';
import { TransactionFactory } from 'src/db/TransactionFactory';
import { Asset } from './domain/asset.entity';
import { EntityManager } from '@mikro-orm/core';
import { assetFactory } from './__tests__/factories';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AssetBatchRemoveEvent } from './domain/events/asset-batch-removed.event';
import { AssetType } from './domain/asset-type.entity';
import { AssetHierarchy } from './domain/asset-hierarchy.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { AssetUpdateEvent } from './domain/events/asset-update.event';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';

const userId = 9;

describe('AssetService', () => {
  describe('create', () => {
    test('valid root asset should be created successfully', async () => {
      const { assetService, assetRepositoryMock, assetHierarchyServiceMock } =
        await createAssetService();

      await assetService.create(
        {
          tag: 'Fan',
          assetTypeId: 42,
          customerId: 84,
          parentIds: [],
          timeZone: 'Asia/Beirut',
        },
        userId,
      );

      const createdAsset = assetRepositoryMock.add.mock.calls[0][0] as Asset;
      expect(createdAsset.tag).toBe('Fan');
      expect(createdAsset.enabled).toBeTruthy();
      expect(createdAsset.timeZone).toBe('Asia/Beirut');
      const assetHierarchyServieCallArgs =
        assetHierarchyServiceMock.addRoot.mock.calls[0];
      expect(assetHierarchyServieCallArgs[0]).toBe(7);
    });

    test('valid child asset should be created successfully', async () => {
      const { assetService, assetRepositoryMock, assetHierarchyServiceMock } =
        await createAssetService();

      await assetService.create(
        {
          tag: 'Fan',
          assetTypeId: 42,
          customerId: 84,
          parentIds: [3],
        },
        userId,
      );

      const createdAsset = assetRepositoryMock.add.mock.calls[0][0] as Asset;
      expect(createdAsset.tag).toBe('Fan');
      expect(createdAsset.enabled).toBeTruthy();
      const assetHierarchyServieCallArgs =
        assetHierarchyServiceMock.addChild.mock.calls[0];
      expect(assetHierarchyServieCallArgs[0]).toBe(3);
      expect(assetHierarchyServieCallArgs[1]).toBe(7);
    });

    test('given a non existing customer an exception should be thrown', async () => {
      const { assetService, customerServiceMock } = await createAssetService();
      customerServiceMock.findById = () => null;

      await expect(
        assetService.create(
          {
            tag: 'Fan',
            assetTypeId: 42,
            customerId: 404,
            parentIds: [],
          },
          userId,
        ),
      ).rejects.toThrowError('Customer does not exist');
    });

    test('given a non existing asset type an exception should be thrown', async () => {
      const { assetService } = await createAssetService();

      await expect(
        assetService.create(
          {
            tag: 'Fan',
            assetTypeId: 404,
            customerId: 84,
            parentIds: [],
          },
          userId,
        ),
      ).rejects.toThrowError('Asset type does not exist');
    });

    test('given a non existing timezone an exception should be thrown', async () => {
      const { assetService } = await createAssetService();

      await expect(
        assetService.create(
          {
            tag: 'Fan',
            assetTypeId: 42,
            customerId: 84,
            parentIds: [],
            timeZone: 'NOT VALID TZ',
          },
          userId,
        ),
      ).rejects.toThrowError('Time zone does not exist');
    });
  });

  describe('remove', () => {
    test('given a non existing asset id an exception should be thrown', async () => {
      const { assetService } = await createAssetService();

      await expect(assetService.remove(404, 4, userId)).rejects.toThrowError(
        'Asset does not exist',
      );
    });

    describe('given an asset with no children', () => {
      let assetToRemove: Asset;
      let assetRepositoryMock: AssetTestingModule['assetRepositoryMock'];
      let eventEmitterMock: AssetTestingModule['eventEmitterMock'];
      beforeAll(async () => {
        const assetTestingModule = await createAssetService();
        assetRepositoryMock = assetTestingModule.assetRepositoryMock;
        eventEmitterMock = assetTestingModule.eventEmitterMock;
        const assetService = assetTestingModule.assetService;

        assetToRemove = assetFactory.createWaterPump(42);

        assetRepositoryMock.findById = jest.fn(async (id) =>
          id === 42 ? assetToRemove : null,
        );
        assetRepositoryMock.removeHierarchy = jest.fn(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          async (rootAsset, runById) => [42],
        );

        await assetService.remove(42, 9, userId);
      });

      it('should be removed', async () => {
        const removeCall = assetRepositoryMock.removeHierarchy.mock.calls[0];
        expect(removeCall[0]).toStrictEqual(assetToRemove);
        expect(removeCall[1]).toBe(userId);
      });

      it('should emit an asset batch remove event with its id', async () => {
        const emitCall = eventEmitterMock.emitAsync.mock.calls[0];
        expect(emitCall[0]).toBe('asset.batch-remove');
        expect((emitCall[1] as AssetBatchRemoveEvent).assetIds).toStrictEqual([
          42,
        ]);
      });
    });

    test('when batch remove event fails an exception should be thrown', async () => {
      const { assetService, eventEmitterMock, assetRepositoryMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      eventEmitterMock.emitAsync = jest.fn(async (_) => [false]);

      await expect(assetService.remove(42, 9, userId)).rejects.toThrowError(
        'Failed to apply asset batch remove event',
      );
    });
  });

  describe('update', () => {
    test('description to existing asset should change it', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(
        4,
        42,
        { description: 'new description' },
        userId,
      );

      const updatedAsset = assetRepositoryMock.update.mock.calls[0][0] as Asset;
      expect(updatedAsset.description).toBe('new description');
    });

    test('description to non existing asset an exception should be thrown', async () => {
      const { assetService } = await createAssetService();

      await expect(
        assetService.update(4, 404, { description: 'new description' }, userId),
      ).rejects.toThrowError('Asset does not exist');
    });

    test('multiple property update to existing asset should change them', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(
        4,
        42,
        {
          description: 'new description',
          latitude: 34,
          longitude: 54,
          tag: 'Water-Pump',
        },
        userId,
      );

      const updatedAsset = assetRepositoryMock.update.mock.calls[0][0] as Asset;
      expect(updatedAsset.description).toBe('new description');
      expect(updatedAsset.latitude).toBe(34);
      expect(updatedAsset.longitude).toBe(54);
      expect(updatedAsset.tag).toBe('Water-Pump');
    });

    test('non existing timezone to existing asset should throw an exception', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      expect(() =>
        assetService.update(4, 42, { timeZone: 'non-existing-tz' }, userId),
      ).rejects.toThrow('Time zone does not exist');
    });

    test('existing time zone to existing asset should change', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(4, 42, { timeZone: 'Asia/Beirut' }, userId);

      const updatedAsset = assetRepositoryMock.update.mock.calls[0][0] as Asset;
      expect(updatedAsset.timeZone).toBe('Asia/Beirut');
    });

    test('non existing asset type to existing asset should throw an exception', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      expect(() =>
        assetService.update(4, 42, { assetTypeId: 404 }, userId),
      ).rejects.toThrow('Asset type does not exist');
    });

    test('existing asset type to existing asset should change', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(4, 42, { assetTypeId: 42 }, userId);

      const updatedAsset = assetRepositoryMock.update.mock.calls[0][0] as Asset;
      expect(updatedAsset.assetTypeId).toBe(42);
    });

    test('adding new parent should call add child on hierarchy service', async () => {
      const { assetService, assetRepositoryMock, assetHierarchyServiceMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(4, 42, { parentIds: [4] }, userId);

      expect(assetHierarchyServiceMock.addChild.mock.calls[0][0]).toBe(4);
    });

    test('adding new parent and keeping existing one should call add child on hierarchy service', async () => {
      const { assetService, assetRepositoryMock, assetHierarchyServiceMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) => {
        if (id === 6) {
          const waterPump = assetFactory.createWaterPump(42);
          const waterPumpMotor = assetFactory.createWaterPumpMotor(6);

          const assetHier = new AssetHierarchy(waterPumpMotor.id, waterPump.id);

          waterPumpMotor.parentHierarchies.add(assetHier);

          return waterPumpMotor;
        }
        return null;
      });

      await assetService.update(4, 6, { parentIds: [42, 5] }, userId);

      expect(assetHierarchyServiceMock.addChild.mock.calls.length).toBe(1);
      expect(assetHierarchyServiceMock.addChild.mock.calls[0][0]).toBe(5);
    });

    test('replacing with empty parent list should call remove child and add root on hierarchy service', async () => {
      const { assetService, assetRepositoryMock, assetHierarchyServiceMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) => {
        if (id === 6) {
          const waterPump = assetFactory.createWaterPump(42);
          const waterPumpMotor = assetFactory.createWaterPumpMotor(6);

          const assetHier = new AssetHierarchy(waterPumpMotor.id, waterPump.id);

          waterPumpMotor.parentHierarchies.add(assetHier);

          return waterPumpMotor;
        }
        return null;
      });

      await assetService.update(4, 6, { parentIds: [] }, userId);

      expect(assetHierarchyServiceMock.addChild.mock.calls.length).toBe(0);
      expect(assetHierarchyServiceMock.removeChild.mock.calls.length).toBe(1);
      expect(assetHierarchyServiceMock.removeChild.mock.calls[0][0]).toBe(42);
      expect(assetHierarchyServiceMock.addRoot.mock.calls.length).toBe(1);
      expect(assetHierarchyServiceMock.addRoot.mock.calls[0][0]).toBe(6);
    });

    test('adding sub tree node as parent should throw a cycle detection error', async () => {
      const { assetService, assetRepositoryMock } = await createAssetService();
      assetRepositoryMock.countHierarchyNodes = jest
        .fn()
        .mockImplementation(async () => 1);
      assetRepositoryMock.findById = jest.fn(async (id) => {
        if (id === 6) {
          const waterPump = assetFactory.createWaterPump(42);
          const waterPumpMotor = assetFactory.createWaterPumpMotor(6);

          const assetHier = new AssetHierarchy(waterPumpMotor.id, waterPump.id);

          waterPumpMotor.parentHierarchies.add(assetHier);

          return waterPumpMotor;
        }
        return null;
      });

      expect(
        assetService.update(4, 6, { parentIds: [7] }, userId),
      ).rejects.toThrow('New parents create a cycle');
    });

    test('should emit an asset update event with its id', async () => {
      const { assetService, eventEmitterMock, assetRepositoryMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );

      await assetService.update(4, 42, { tag: 'new-tag' }, userId);

      const emitCall = eventEmitterMock.emitAsync.mock.calls[0];
      expect(emitCall[0]).toBe('asset.update');
      expect((emitCall[1] as AssetUpdateEvent).id).toBe(42);
    });

    test('when update event fails an exception should be thrown', async () => {
      const { assetService, eventEmitterMock, assetRepositoryMock } =
        await createAssetService();
      assetRepositoryMock.findById = jest.fn(async (id) =>
        id === 42 ? assetFactory.createWaterPump(42) : null,
      );
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      eventEmitterMock.emitAsync = jest.fn(async (_) => [false]);

      await expect(
        assetService.update(4, 42, { tag: 'new-tag' }, userId),
      ).rejects.toThrowError('Failed to apply asset update event');
    });
  });
});

type AssetTestingModule = Awaited<ReturnType<typeof createAssetService>>;

const createAssetService = async () => {
  const customerServiceMock = {
    findById: (id) => id,
  };

  const assetTypeServiceMock: jest.Mocked<Pick<AssetTypeService, 'findById'>> =
    {
      findById: jest.fn(async (id) =>
        id !== 404 ? new AssetType({ name: 'Pump' }) : null,
      ),
    };

  const timeZonesServiceMock: jest.Mocked<Pick<TimeZoneService, 'exists'>> = {
    exists: jest.fn(async (timeZone) =>
      timeZone === 'Asia/Beirut' ? true : false,
    ),
  };

  const assetRepositoryMock: jest.Mocked<
    Pick<
      AssetRepository,
      | 'findById'
      | 'getAllByCustomerId'
      | 'add'
      | 'removeHierarchy'
      | 'update'
      | 'countHierarchyNodes'
    >
  > = {
    findById: jest.fn(async (id) =>
      id === 404 ? null : assetFactory.createWaterPump(3),
    ),
    getAllByCustomerId: jest.fn(),
    removeHierarchy: jest.fn(),
    add: jest.fn(async (newAsset) => {
      newAsset.id = 7;
      return newAsset;
    }),
    update: jest.fn(),
    countHierarchyNodes: jest.fn(),
  };

  const entityManagerMock = {} as EntityManager;

  const transactionFactoryMock: Pick<TransactionFactory, 'run'> = {
    run: jest.fn(
      async (transactionBlock) => await transactionBlock(entityManagerMock),
    ),
  };

  const assetHierarchyServiceMock: jest.Mocked<
    Pick<AssetHierarchyService, 'addRoot' | 'addChild' | 'removeChild'>
  > = {
    addRoot: jest.fn(),
    addChild: jest.fn(),
    removeChild: jest.fn(),
  };

  const eventEmitterMock: jest.Mocked<Pick<EventEmitter2, 'emitAsync'>> = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    emitAsync: jest.fn(async (_) => [true]),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [
      createMikroOrmTestModule([Asset, AssetType, Customer, AssetHierarchy]),
    ],
    providers: [
      {
        provide: CustomerService,
        useValue: customerServiceMock,
      },
      {
        provide: AssetTypeService,
        useValue: assetTypeServiceMock,
      },
      {
        provide: AssetRepository,
        useValue: assetRepositoryMock,
      },
      {
        provide: AssetHierarchyService,
        useValue: assetHierarchyServiceMock,
      },
      {
        provide: TransactionFactory,
        useValue: transactionFactoryMock,
      },
      {
        provide: TimeZoneService,
        useValue: timeZonesServiceMock,
      },
      {
        provide: EventEmitter2,
        useValue: eventEmitterMock,
      },
      AssetService,
    ],
  }).compile();
  const assetService = moduleRef.get(AssetService);
  return {
    assetService,
    assetRepositoryMock,
    assetHierarchyServiceMock,
    customerServiceMock,
    assetTypeServiceMock,
    timeZonesServiceMock,
    eventEmitterMock,
  };
};
