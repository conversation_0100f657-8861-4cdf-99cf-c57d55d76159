import {
  Entity,
  ManyToOne,
  OneToOne,
  OptionalProps,
  PrimaryKey,
  Property,
} from "@mikro-orm/core";
import { FactorType } from "./factor-type.entity";
import { Measurement } from "../../measurements/domain/measurement.entity";
import { User } from "../../users/domain/user.entity";

@Entity()
export class TimeVaryingFactor {
  [OptionalProps]?: "seasonal";

  @PrimaryKey()
  id!: number;

  @OneToOne({
    entity: () => Measurement,
    fieldName: "measurement",
    nullable: true,
    unique: "tv_factors_meas_unique",
  })
  measurement?: Measurement;

  @ManyToOne({
    entity: () => FactorType,
    fieldName: "factor_type",
    nullable: true,
    index: "time_var_factor_factor_type_idx",
  })
  factorType?: FactorType;

  @Property({ length: 6, nullable: true })
  createdAt?: Date;

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @ManyToOne({
    entity: () => User,
    fieldName: "created_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  createdBy?: User;

  @ManyToOne({
    entity: () => User,
    fieldName: "updated_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  updatedBy?: User;

  @Property({ default: false })
  seasonal: boolean = false;
}
