export class NotificationDTO {
    userId: number;
    message: string;
    createdAt: Date;
    isRead: boolean;
    retryCount: number;
    notifyType: 'sms' | 'email';
    to: string;
    subject: string;
    constructor(userId: number, message: string, createdAt: Date, isRead: boolean, retryCount: number, notifyType: 'sms' | 'email', to: string, subject: string) {
        this.userId = userId;
        this.message = message;
        this.createdAt = createdAt;
        this.isRead = isRead;
        this.retryCount = retryCount;
        this.notifyType = notifyType;
        this.to = to;
        this.subject = subject;
    }
}