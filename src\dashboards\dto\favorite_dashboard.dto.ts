import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsN<PERSON>ber } from 'class-validator';

export class FavoriteDashboardDto {
  @ApiProperty({ required: true, example: 1 })
  @IsNumber()
  @IsNotEmpty()
  dashboard_id: number;

  @ApiProperty({ required: true, example: true })
  @IsBoolean()
  @IsNotEmpty()
  status: boolean;
}

export class DashboardConflictResDto {
  @ApiProperty({ required: true, example: 409 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Dashboard id :dashboardId is already favorite for this user.' })
  message: string;

  @ApiProperty({ required: true, example: 'Conflict' })
  error: string;
}