{"info": {"_postman_id": "7a254acf-ca7e-45ef-a206-cade984cdb14", "name": "BromptonEnergy Admin API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "31861689"}, "item": [{"name": "Measurements Backoffice", "item": [{"name": "Get All Data Types", "event": [{"listen": "prerequest", "script": {"exec": ["const cookieJar = pm.cookies.jar();", "cookieJar.get(\"localhost:3030\", \"BE_CSRFToken\", ((error, cookie) => {", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/metadata/data-types", "host": ["{{appUrl}}"], "path": ["v0", "metadata", "data-types"]}}, "response": []}, {"name": "Get All Value Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/value-types", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "value-types"]}}, "response": []}, {"name": "Get All Measurement Types", "event": [{"listen": "prerequest", "script": {"exec": ["const cookieJar = pm.cookies.jar();", "cookieJar.get(\"localhost:3030\", \"BE_CSRFToken\", ((error, cookie) => {", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/metadata/measurement-types", "host": ["{{appUrl}}"], "path": ["v0", "metadata", "measurement-types"]}}, "response": []}, {"name": "Get All Unit of Measure Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/measurement-types/2/units-of-measure", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "measurement-types", "2", "units-of-measure"]}}, "response": []}, {"name": "Get All Asset Type Metrics", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/metrics", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "metrics"]}}, "response": []}, {"name": "Get All Asset Time zones", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/time-zones", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "time-zones"]}}, "response": []}, {"name": "Get All Units Groups", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/units-groups", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "units-groups"]}}, "response": []}, {"name": "Get All Datasources", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/datasources", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "datasources"]}}, "response": []}, {"name": "Get All Units Group Units", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/units-groups", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "units-groups"]}}, "response": []}]}, {"name": "Timeseries", "item": [{"name": "Get Current", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/current/2?", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "current", "2"], "query": [{"key": "", "value": null}]}}, "response": []}, {"name": "Get History", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/history/2?start=0&meas_id=2", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "history", "2"], "query": [{"key": "start", "value": "0"}, {"key": "meas_id", "value": "2"}]}}, "response": []}, {"name": "Get Aggregation", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/timeseries/agg/2?start=0&meas_id=2", "host": ["{{appUrl}}"], "path": ["v0", "timeseries", "agg", "2"], "query": [{"key": "start", "value": "0"}, {"key": "meas_id", "value": "2"}]}}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Default Dashboard", "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dashboard_id\": 42,\n    \"user_id\": 2,\n    \"status\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/default", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "default"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Edit Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Apple edited 2\",\n    \"data\": \"{apple1:ioioiio1}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/8/dashboards/118", "host": ["{{appUrl}}"], "path": ["v0", "customers", "8", "dashboards", "118"]}}, "response": []}, {"name": "Create Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Apple today\",\n    \"data\": \"{apple:ioioiio}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}:3030/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "port": "3030", "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "9"}]}}, "response": []}, {"name": "Delete Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/128", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "128"], "variable": [{"key": "customerId", "value": ""}]}}, "response": []}, {"name": "Get All Dashboard", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Get Dashboard by customer", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards"], "variable": [{"key": "customerId", "value": "9"}]}}, "response": []}, {"name": "Get Dashboard details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/dashboards/118", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "dashboards", "118"], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Alert Aggregation periods", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/periods", "host": ["{{appUrl}}"], "path": ["v0", "alert", "periods"]}}, "response": []}, {"name": "Alert Aggregation periods", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{appUrl}}/v0/alert/periods", "host": ["{{appUrl}}"], "path": ["v0", "alert", "periods"]}}, "response": []}, {"name": "Alert Aggregation threshholds", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/thresholdTypes", "host": ["{{appUrl}}"], "path": ["v0", "alert", "thresholdTypes"]}}, "response": []}, {"name": "Alert Aggregation conditions", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/conditions", "host": ["{{appUrl}}"], "path": ["v0", "alert", "conditions"]}}, "response": []}, {"name": "Alert Aggregation Create", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"agg\": 1,\r\n    \"asset\": 257,\r\n    \"condition\": 1,\r\n    \"measurement\": 11288,\r\n    \"period\": 3,\r\n    \"resetDeadband\": 23,\r\n    \"thresholdType\": 1,\r\n    \"notificationType\": 1,\r\n    \"thresholdValue\": 23,\r\n    \"users\": [\r\n        1,\r\n        2\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/alert/", "host": ["{{appUrl}}"], "path": ["v0", "alert", ""]}}, "response": []}, {"name": "get all alerts by measure", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/measurements/11284", "host": ["{{appUrl}}"], "path": ["v0", "alert", "measurements", "11284"]}}, "response": []}, {"name": "Get all alerts", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/", "host": ["{{appUrl}}"], "path": ["v0", "alert", ""]}}, "response": []}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "PUT", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"agg\": 1,\r\n    \"asset\": 257,\r\n    \"condition\": 1,\r\n    \"measurement\": 18580,\r\n    \"period\": 3,\r\n    \"resetDeadband\": 23,\r\n    \"thresholdType\": 1,\r\n    \"thresholdValue\": 23,\r\n    \"users\": [\r\n        {\r\n            \"id\": 9\r\n            // \"notificationtype\": \r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/alert/3", "host": ["{{appUrl}}"], "path": ["v0", "alert", "3"]}}, "response": []}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/details/3", "host": ["{{appUrl}}"], "path": ["v0", "alert", "details", "3"]}}, "response": []}, {"name": "Delete Alert", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "DELETE", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/9", "host": ["{{appUrl}}"], "path": ["v0", "alert", "9"]}}, "response": []}, {"name": "Get all alert events", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/alert/event/9", "host": ["{{appUrl}}"], "path": ["v0", "alert", "event", "9"]}}, "response": []}]}, {"name": "Diagram", "item": [{"name": "create Diagram", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{appUrl}}/v0/sessions", "host": ["{{appUrl}}"], "path": ["v0", "sessions"]}}, "response": []}, {"name": "update Diagram", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"name\":\"test\",\n   \"data\":\"s\"\n}"}, "url": {"raw": "{{appUrl}}/v0/diagram/1", "host": ["{{appUrl}}"], "path": ["v0", "diagram", "1"]}}, "response": []}, {"name": "get all diagrams", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/diagram", "host": ["{{appUrl}}"], "path": ["v0", "diagram"]}}, "response": []}, {"name": "get diagram details", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/diagram/1", "host": ["{{appUrl}}"], "path": ["v0", "diagram", "1"]}}, "response": []}]}, {"name": "Calculation Engine", "item": [{"name": "get Calculation Engine templates", "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}"}], "url": {"raw": "{{appUrl}}/v0/calc-engine/data-types", "host": ["{{appUrl}}"], "path": ["v0", "calc-engine", "data-types"]}}, "response": []}, {"name": "Create Calculation Engine templates", "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"test\",\r\n    \"expression\": \"$A*$B\",\r\n    \"description\": \"Multiply of A over B\",\r\n    \"dataType\":1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/calc-engine/templates", "host": ["{{appUrl}}"], "path": ["v0", "calc-engine", "templates"]}}, "response": []}, {"name": "calc engine templates", "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}"}], "url": {"raw": "{{appUrl}}/v0/calc-engine/templates/", "host": ["{{appUrl}}"], "path": ["v0", "calc-engine", "templates", ""]}}, "response": []}, {"name": "calc engine templates by Measurement", "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}"}], "url": {"raw": "{{appUrl}}/v0/calc-engine/measurement/25493", "host": ["{{appUrl}}"], "path": ["v0", "calc-engine", "measurement", "25493"]}}, "response": []}, {"name": "edit calc engine template", "request": {"method": "PUT", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}"}], "body": {"mode": "raw", "raw": "// {\r\n//     \"calcInstance\": {\r\n//         \"id\": 20,\r\n//         \"created\": \"2023-08-10T19:18:18.358Z\",\r\n//         \"updated\": \"2023-08-10T19:18:18.358Z\",\r\n//         \"createdby\": 0,\r\n//         \"updatedby\": 0,\r\n//         \"calculation\": 8,\r\n//         \"outputMesurementId\": 22643,\r\n//         \"ispersisted\": false,\r\n//         \"pollPeriod\": null\r\n//     },\r\n//     \"calcTemplate\": {\r\n//         \"id\": 8,\r\n//         \"created\": \"2023-08-03T18:10:13.081Z\",\r\n//         \"updated\": \"2024-07-09T06:16:04.507Z\",\r\n//         \"createdby\": 0,\r\n//         \"updatedby\": 2,\r\n//         \"name\": \"Non-negative Only Difference\",\r\n//         \"expression\": \"$A>=$B?$A-$B:0\",\r\n//         \"description\": \"Difference A-B but only if A>=B\",\r\n//         \"dataType\": 2,\r\n//         \"importsList\": null\r\n//     },\r\n//     \"calcInputs\": [\r\n//         {\r\n//             \"id\": 92,\r\n//             \"created\": \"2023-08-10T22:47:40.062Z\",\r\n//             \"updated\": \"2023-08-10T22:47:40.062Z\",\r\n//             \"createdby\": 0,\r\n//             \"updatedby\": 0,\r\n//             \"inputLabel\": \"$A \",\r\n//             \"calculationInstance\": 20,\r\n//             \"measurement\": 22637,\r\n//             \"constantNumber\": null,\r\n//             \"constantString\": null,\r\n//             \"comment\": \"power demand\"\r\n//         },\r\n//         {\r\n//             \"id\": 102,\r\n//             \"created\": \"2023-08-11T15:57:07.248Z\",\r\n//             \"updated\": \"2023-08-11T15:57:07.248Z\",\r\n//             \"createdby\": 0,\r\n//             \"updatedby\": 0,\r\n//             \"inputLabel\": \"$B \",\r\n//             \"calculationInstance\": 20,\r\n//             \"measurement\": 12457,\r\n//             \"constantNumber\": null,\r\n//             \"constantString\": null,\r\n//             \"comment\": \"input active power\"\r\n//         }\r\n//     ]\r\n// }\r\n{\r\n    \"customerId\": 8,\r\n    \"templateId\": 11,\r\n    \"pollPeriod\": 1,\r\n    \"ispersisted\": true,\r\n    \"outputMesurementId\": 25418,\r\n    \"inputs\": [\r\n        {\r\n            \"inputLabel\": \"$B\",\r\n            \"constantType\": \"number\",\r\n            \"measurementId\": 24610,\r\n            \"comment\": \"B\",\r\n            \"inputId\": 201\r\n        },\r\n        {\r\n            \"inputLabel\": \"$A \",\r\n            \"constantType\": \"string\",\r\n            // \"measurementId\": 24609,\r\n            \"constantValue\": \"20\",\r\n            \"comment\": \"COmment\",\r\n            \"inputId\": 200\r\n        }\r\n    ]\r\n}\r\n// {\r\n//     \"customerId\": 8,\r\n//     \"templateId\": 11,\r\n//     \"pollPeriod\": 9,\r\n//     \"ispersisted\": true,\r\n//     \"outputMesurementId\": 25418,\r\n//     \"inputs\": [\r\n//         {\r\n//             \"inputLabel\": \"$A \",\r\n//             \"constantType\": \"number\",\r\n//             \"measurementId\": 24609,\r\n//             \"comment\": \"A\",\r\n//             \"inputId\": 200\r\n//         },\r\n//         {\r\n//             \"inputLabel\": \"$B \",\r\n//             \"constantType\": \"number\",\r\n//             \"constantValue\": \"20\",\r\n//             \"comment\": \"V\",\r\n//             \"inputId\": 201\r\n//         }\r\n//     ]\r\n// }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/calc-engine/templates/instance/90", "host": ["{{appUrl}}"], "path": ["v0", "calc-engine", "templates", "instance", "90"]}}, "response": []}]}, {"name": "Measure Factors", "item": [{"name": "Measure Factors type", "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/factors/types/", "host": ["{{appUrl}}"], "path": ["v0", "factors", "types", ""]}}, "response": []}, {"name": "Measure Factors by Measure", "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/factors/timeVariangFactor/25336", "host": ["{{appUrl}}"], "path": ["v0", "factors", "timeVariangFactor", "25336"]}}, "response": []}, {"name": "get Factor schedules", "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/factors/schedules/2", "host": ["{{appUrl}}"], "path": ["v0", "factors", "schedules", "2"]}}, "response": []}, {"name": "get Factor schedule - time of days", "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/factors/schedules/3/weekTime/3", "host": ["{{appUrl}}"], "path": ["v0", "factors", "schedules", "3", "weekTime", "3"]}}, "response": []}, {"name": "create factor", "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"measurement\": 22436,\r\n    \"seasonal\": false,\r\n    \"factorType\": 2,\r\n    \"factorSchedule\": [\r\n        {\r\n            \"effectiveDate\": \"2023-06-01\",\r\n            \"factorTimeOfDayValue\": [\r\n                {\r\n                    \"timeOfDay\": \"10:00\",\r\n                    \"weekday\": 0,\r\n                    \"value\": 30\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"effectiveDate\": \"2023-06-02\",\r\n            \"factorTimeOfDayValue\": [\r\n                {\r\n                    \"timeOfDay\": \"12:00\",\r\n                    \"weekday\": 0,\r\n                    \"value\": 30\r\n                },\r\n                {\r\n                    \"timeOfDay\": \"15:00\",\r\n                    \"weekday\": 1,\r\n                    \"value\": 50\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/factors/create-factor", "host": ["{{appUrl}}"], "path": ["v0", "factors", "create-factor"]}}, "response": []}, {"name": "Edit Time Varing factor", "request": {"method": "PUT", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"timeVaryingFactor\": {\r\n        // \"measurement\": 22436,\r\n        \"seasonal\": true,\r\n        \"factorType\": 3\r\n    },\r\n    \"factorSchedule\": [\r\n        {\r\n            \"effectiveDate\": \"2023-06-07\"\r\n        }\r\n        // {\r\n        //     \"effectiveDate\": \"2023-06-10\"\r\n        // }\r\n    ],\r\n    \"factorTimeOfDayValue\": [\r\n        {\r\n            \"effectiveDate\": \"2023-06-07\",\r\n            \"timeOfDay\": \"10:00\",\r\n            \"weekday\": 0,\r\n            \"value\": 30\r\n        }\r\n        // {\r\n        //     \"effectiveDate\": \"2023-06-10\",\r\n        //     \"timeOfDay\": \"12:00\",\r\n        //     \"weekday\": 0,\r\n        //     \"value\": 30\r\n        // },\r\n        // {\r\n        //     \"effectiveDate\": \"2023-06-10\",\r\n        //     \"timeOfDay\": \"15:00\",\r\n        //     \"weekday\": 1,\r\n        //     \"value\": 50\r\n        // }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/factors/timeVariangFactor/30", "host": ["{{appUrl}}"], "path": ["v0", "factors", "timeVariangFactor", "30"]}}, "response": []}, {"name": "create factor new request", "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/time-varying-factor/2", "host": ["{{appUrl}}"], "path": ["v0", "time-varying-factor", "2"]}}, "response": []}, {"name": "create factor new request - new request", "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"measurement\": 22440,\r\n    \"seasonal\": false,\r\n    \"factorType\": 2,\r\n    \"factorSchedule\": [\r\n        {\r\n            \"effectiveDate\": \"2023-06-01\",\r\n            \"factorTimeOfDayValue\": [\r\n                {\r\n                    \"timeOfDay\": \"10:00\",\r\n                    \"weekday\": 0,\r\n                    \"value\": 30\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"effectiveDate\": \"2023-06-02\",\r\n            \"factorTimeOfDayValue\": [\r\n                {\r\n                    \"timeOfDay\": \"12:00\",\r\n                    \"weekday\": 0,\r\n                    \"value\": 30\r\n                },\r\n                {\r\n                    \"timeOfDay\": \"15:00\",\r\n                    \"weekday\": 1,\r\n                    \"value\": 50\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/time-varying-factor", "host": ["{{appUrl}}"], "path": ["v0", "time-varying-factor"]}}, "response": []}]}, {"name": "Dashboard templates", "item": [{"name": "Get Dashboard templates", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{appUrl}}/v0/dashboard-templates", "host": ["{{appUrl}}"], "path": ["v0", "dashboard-templates"]}}, "response": []}, {"name": "Create Dashboard templates", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{appUrl}}/v0/dashboard-templates", "host": ["{{appUrl}}"], "path": ["v0", "dashboard-templates"]}}, "response": []}, {"name": "Get Dashboard templates Details", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/dashboard-templates", "host": ["{{appUrl}}"], "path": ["v0", "dashboard-templates"]}}, "response": []}]}, {"name": "User", "item": [{"name": "Create Scoped User", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"scoped_user\",\n    \"password\": \"asdfasdf\",\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"User\",\n    \"scoped_roles\": [{\"role\": \"ADMIN\", \"customer_ids\": [1, 2]}],\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users", "host": ["{{appUrl}}"], "path": ["v0", "users"]}}, "response": []}, {"name": "Get User Details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/users/me", "host": ["{{appUrl}}"], "path": ["v0", "users", "me"]}}, "response": []}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{testPassword}}\"\n}"}, "url": {"raw": "{{appUrl}}/v0/sessions", "host": ["{{appUrl}}"], "path": ["v0", "sessions"]}}, "response": []}, {"name": "Create User", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"customer_user\",\n    \"password\": \"asdfasdf\",\n    \"first_name\": \"Just\",\n    \"last_name\": \"Customer\",\n    \"scoped_roles\": [{\"role\":\"USER\", \"cusotmer_ids\":[1]}],\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users", "host": ["{{appUrl}}"], "path": ["v0", "users"]}}, "response": []}, {"name": "Update User Preference", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"preferences\": {\n        \"DATE_FORMAT\": \"DD-MM-YYYY\",\n        \"CURRENCY\": \"INR\",\n        \"DEFAULT_CUSTOMER\": \"1\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users/preference", "host": ["{{appUrl}}"], "path": ["v0", "users", "preference"]}}, "response": []}, {"name": "Get User Preference", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/users/preference", "host": ["{{appUrl}}"], "path": ["v0", "users", "preference"]}}, "response": []}, {"name": "Patch User Details", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CSRFToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/users/me", "host": ["{{appUrl}}"], "path": ["v0", "users", "me"]}}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user\":\"<EMAIL>\"\r\n    // \"user\":\"tset\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/forgot/email-username", "host": ["{{appUrl}}"], "path": ["v0", "forgot", "email-username"]}}, "response": []}, {"name": "Reset forgotton password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"97f4f4e395c9d8d3508f7ea710b1ff15e10ed6c4209ff\", // this token valid for 2 hrs\r\n    \"password\": \"new_password\",\r\n    \"reset_password\":\"new_password\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/forgot/reset", "host": ["{{appUrl}}"], "path": ["v0", "forgot", "reset"]}}, "response": []}, {"name": "Invalidate Session/ Logout user", "request": {"method": "POST", "header": [], "url": {"raw": "{{appUrl}}/v0/sessions/invalidate", "host": ["{{appUrl}}"], "path": ["v0", "sessions", "invalidate"]}}, "response": []}]}, {"name": "Customer", "item": [{"name": "Get All Customers by NameId", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/apple", "host": ["{{appUrl}}"], "path": ["v0", "customers", "apple"]}}, "response": []}, {"name": "Get customer logo", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/85/logo", "host": ["{{appUrl}}"], "path": ["v0", "customers", "85", "logo"]}}, "response": []}, {"name": "Create Customer", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"name_id\": \"apple\",\n    \"address\": \"Palo Altro\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers", "host": ["{{appUrl}}"], "path": ["v0", "customers"]}}, "response": []}, {"name": "Get All Customers", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers", "host": ["{{appUrl}}"], "path": ["v0", "customers"]}}, "response": []}, {"name": "Get All Customer Users", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/:customerId/users", "host": ["{{appUrl}}"], "path": ["v0", "customers", ":customerId", "users"], "query": [{"key": "user_name", "value": "demo_sc_admin", "disabled": true}, {"key": "email", "value": "demo_sc_admin", "disabled": true}, {"key": "customer_name", "value": "Test_DemoCustomer", "disabled": true}], "variable": [{"key": "customerId", "value": "8"}]}}, "response": []}, {"name": "Patch Customer User by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/users/5", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "users", "5"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Create Asset Type", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Rocket Engine\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types"]}}, "response": []}, {"name": "Create Asset", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tag\": \"Sub Sub Compressor\",\n    \"type_id\": 2,\n    \"description\": \"Heavy duty compressor\",\n    \"parent_ids\": [],\n    \"is_customer_primary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/2/assets", "host": ["{{appUrl}}"], "path": ["v0", "customers", "2", "assets"]}}, "response": []}, {"name": "Get All Asset Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types"]}}, "response": []}, {"name": "Get Asset by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Patch Asset by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"dubid<PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Delete Asset", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2"]}}, "response": []}, {"name": "Get All Assets by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/82/assets?parentIds=-1,318", "host": ["{{appUrl}}"], "path": ["v0", "customers", "82", "assets"], "query": [{"key": "parentIds", "value": "-1,318"}]}}, "response": []}]}, {"name": "Asset Measurements", "item": [{"name": "Create Asset Measurement", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tag\": \"Espresso Pressure\",\n    \"type_id\": 6,\n    \"data_type_id\": 2,\n    \"value_type_id\": 1,\n    \"description\": \"Coffee pressure\",\n    \"location_id\":10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements"]}}, "response": []}, {"name": "Get Asset Measurement by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements/14661", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements", "14661"]}}, "response": []}, {"name": "Get All Asset Measurements", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2/measurements", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2", "measurements"]}}, "response": []}, {"name": "Delete Asset Measurement", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/2/measurements/9500", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "2", "measurements", "9500"]}}, "response": []}, {"name": "Get Asset Measurement by Id Location", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/location-by-measure/22329", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "location-by-measure", "22329"]}}, "response": []}, {"name": "Get All Measurement Types", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"otroadmin\",\n    \"password\": \"unpassword\",\n    \"role\": \"SUPER_USER\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/measurements-backoffice/measurement-types", "host": ["{{appUrl}}"], "path": ["v0", "measurements-backoffice", "measurement-types"]}}, "response": []}, {"name": "Patch Asset Measurement by Id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"meter_factor\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/customers/1/assets/50/measurements/14664", "host": ["{{appUrl}}"], "path": ["v0", "customers", "1", "assets", "50", "measurements", "14664"]}}, "response": []}, {"name": "Get Asset Measurement by Id Location", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "url": {"raw": "{{appUrl}}/v0/customers/8/assets/348/measurements/22329/location", "host": ["{{appUrl}}"], "path": ["v0", "customers", "8", "assets", "348", "measurements", "22329", "location"]}}, "response": []}]}, {"name": "<PERSON><PERSON> Template", "item": [{"name": "Create As<PERSON> Template", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"manufacturer\": \"SpaceX\",\n    \"model_number\": \"Raptor\",\n    \"measurements\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3030/v0/assets-backoffice/asset-types/9/asset-templates", "host": ["localhost"], "port": "3030", "path": ["v0", "assets-backoffice", "asset-types", "9", "asset-templates"]}}, "response": []}, {"name": "Create As<PERSON> Templates Instance", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"units_group_id\": 40,\n    \"asset\": {\n        \"tag\": \"Testing Toyota Generator\",\n        \"description\": \"Generator used for testing\",\n        \"parent_ids\": [],\n        \"time_zone\": \"America/Argentina/Buenos_Aires\",\n        \"customer_id\": 8\n    },\n    \"measurements\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/asset-templates/11/instances", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "asset-templates", "11", "instances"]}}, "response": []}, {"name": "Get All Asset Templates", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "BE-CsrfToken", "value": "{{csrfToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{appUrl}}/v0/assets-backoffice/asset-types/5/asset-templates", "host": ["{{appUrl}}"], "path": ["v0", "assets-backoffice", "asset-types", "5", "asset-templates"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const cookieJar = pm.cookies.jar();", "const appCookieDomain = pm.environment.get(\"appCookieDomain\");", "cookieJar.get(appCookieDomain, \"BE-CSRFToken\", ((error, cookie) => {", "    console.log('error', error)", "    pm.collectionVariables.set(\"csrfToken\", cookie);", "}))"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "password", "value": "3e7daccab1060e5a36b1c04891087c9a", "type": "string"}, {"key": "username", "value": "", "type": "string"}, {"key": "csrfToken", "value": ""}, {"key": "testPassword", "value": "hapschile", "type": "string"}]}