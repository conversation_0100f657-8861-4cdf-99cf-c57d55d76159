import { EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import { AssetType, AssetTypeId } from "./domain/asset-type.entity";
import { Metric } from "./domain/metric.entity";
import { User } from "src/users/domain/user.entity";
import { TransactionFactory } from "src/db/TransactionFactory";

@Injectable()
export class MetricService {
  constructor(
    @InjectRepository(Metric)
    private readonly metricRepository: EntityRepository<Metric>,
    @InjectRepository(AssetType)
    private readonly assetTypeRepository: EntityRepository<AssetType>,
    private readonly transactionFactory: TransactionFactory
  ) {}

  async findById(id: number): Promise<Metric | null> {
    return await this.metricRepository.findOne({ id });
  }

  async getAllByAssetTypeId(assetTypeId: AssetTypeId) {
    return await this.metricRepository.find({ assetType: { id: assetTypeId } });
  }
  async getAllAssetTypes() {
    return await this.metricRepository.findAll();
  }
  async create({
    name,
    assetTypeId,
    user,
  }: {
    name: string;
    assetTypeId: number;
    user: User;
  }) {
    const assetType = await this.assetTypeRepository.findOneOrFail({
      id: assetTypeId,
    });
    const metric = new Metric({
      name,
      assetType: assetType,
    });
    metric.createdAt = new Date();
    metric.createdBy = Reference.create(user);
    // await this.metricRepository.persistAndFlush(metric);
    return metric;
  }

  async createMany({
    metrics,
    assetTypeId,
    user,
  }: {
    metrics: string[];
    assetTypeId: number;
    user: User;
  }) {
    return await this.transactionFactory.run(async () => {
      const assetType = await this.assetTypeRepository.findOneOrFail({
        id: assetTypeId,
      });
      const newMetrics = metrics.map(
        (metric) =>
          new Metric({
            name: metric,
            assetType: assetType,
          })
      );
      newMetrics.forEach((metric) => {
        metric.createdAt = new Date();
        metric.createdBy = Reference.create(user);
      });
      await this.metricRepository.persistAndFlush(newMetrics);
      return newMetrics;
    });
  }
}
