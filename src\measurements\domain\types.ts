export type MeasurementBackofficeType =
  | "data"
  | "measurement"
  | "value"
  | "location";

export type AssetMeasurementCreationData = {
  tag: string;
  metricId?: number;
  assetId: number;
  typeId: number;
  dataTypeId: number;
  valueTypeId: number;
  meterFactor?: number;
  unitOfMeasureId?: number;
  datasourceId?: number;
  description?: string;
  locationId?: number;
  writeback?: boolean;
};
