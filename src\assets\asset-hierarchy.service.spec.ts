import { Test } from '@nestjs/testing';
import { AssetHierarchyService } from './asset-hierarchy.service';
import { AssetHierarchyRepository } from './repository/asset-hierarchy.repository';
import { AssetService } from './asset.service';
import { assetFactory } from './__tests__/factories';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { AssetHierarchy } from './domain/asset-hierarchy.entity';
import { Asset } from './domain/asset.entity';
import { AssetType } from './domain/asset-type.entity';
import { Customer } from 'src/customers/domain/customer.entity';

describe('AssetHierarchyService', () => {
  test('given a non existing parent asset an exception should be thrown', async () => {
    const assetServiceMock: jest.Mocked<Pick<AssetService, 'findById'>> = {
      findById: jest.fn(async (id) =>
        id === 404 ? null : assetFactory.createWaterPump(id),
      ),
    };
    const assetHierarchyRepositoryMock: jest.Mocked<
      Pick<AssetHierarchyRepository, 'add'>
    > = {
      add: jest.fn(),
    };
    const moduleRef = await Test.createTestingModule({
      imports: [
        createMikroOrmTestModule([Asset, AssetType, AssetHierarchy, Customer]),
      ],
      providers: [
        {
          provide: AssetHierarchyRepository,
          useValue: assetHierarchyRepositoryMock,
        },
        { provide: AssetService, useValue: assetServiceMock },
        AssetHierarchyService,
      ],
    }).compile();
    const assetHierarchyService = moduleRef.get(AssetHierarchyService);

    await expect(
      assetHierarchyService.addChild(404, 42, 9),
    ).rejects.toThrowError('Parent asset does not exist');
  });
});
