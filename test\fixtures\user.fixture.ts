import { MikroORM, RequestContext } from '@mikro-orm/core';
import { TestingModule } from '@nestjs/testing';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { User } from 'src/users/domain/user.entity';
import { UserService } from 'src/users/user.service';

export async function createSuperUser(testingModule) {
  const userService: UserService = await testingModule.resolve(UserService);
  const orm = await testingModule.resolve(MikroORM);

  return await RequestContext.createAsync(orm.em, async () => {
    const { id } = await userService.create(
      {
        username: 'testuser',
        password: 'testpassword',
        firstName: 'f',
        lastName: 'l',
        email: '<EMAIL>',
        globalRole: Role.ADMIN,
      },
      null,
    );
    return id;
  });
}

export async function deleteUser(
  testingModule: TestingModule,
  username: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    const em = RequestContext.getEntityManager();
    if (!em) {
      throw new Error('No entity manager available');
    }
    const user = await em.findOne(User, { username });
    if (user !== null) {
      user.customerRoles
        .getItems()
        .forEach((customerRole) => em.remove(customerRole));

      await em.removeAndFlush(user);
    }
  });
}

export type UserFixture = Awaited<
  ReturnType<typeof userFixtureFactory.createSuperUser>
>;

export const userFixtureFactory = {
  createSuperUser: async (testingModule: TestingModule) => {
    const superUserId = await createSuperUser(testingModule);

    return {
      superUserId,
      username: 'testuser',
      password: 'testpassword',
      cleanUp: async () => {
        await deleteUser(testingModule, 'testuser');
      },
    };
  },
};
