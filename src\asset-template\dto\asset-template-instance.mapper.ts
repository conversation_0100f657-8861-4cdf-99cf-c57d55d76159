import { Injectable } from "@nestjs/common";
import {
  AssetMeasurementOverride,
  AssetTemplateInstance,
} from "../domain/asset-template-instance";
import {
  AssetTemplateInstanceDto,
  MeasurementInstanceCreationDto,
} from "./asset-template-instance.dto";

@Injectable()
export class AssetTemplateInstanceMapper {
  toDto(
    assetTemplateInstance: AssetTemplateInstance
  ): AssetTemplateInstanceDto {
    const {
      id,
      parentIds: parent_ids,
      childrenIds: children_ids,
      customerId: customer_id,
      tag,
      assetTypeId: type_id,
      timeZone: time_zone,
      latitude,
      longitude,
      description,
    } = assetTemplateInstance.asset;
    return {
      units_group_id: assetTemplateInstance.unitsGroupId,
      asset: {
        id,
        parent_ids,
        children_ids,
        customer_id,
        tag,
        type_id,
        time_zone: time_zone ?? null,
        latitude: latitude ?? null,
        longitude: longitude ?? null,
        description: description ?? null,
      },
      measurements: assetTemplateInstance.measurements.map(
        (assetMeasurement) => {
          const {
            id,
            measurementId: measurement_id,
            tag,
            typeId: type_id,
            dataTypeId: data_type_id,
            valueTypeId: value_type_id,
            description,
            metricId: metric_id,
            meterFactor: meter_factor,
            unitOfMeasureId: unit_of_measure_id,
            locationId: location_id,
            datasourceId: datasource_id,
            writeback,
          } = assetMeasurement;
          return {
            id,
            measurement_id,
            tag,
            type_id,
            data_type_id,
            value_type_id,
            description: description ?? null,
            metric_id,
            meter_factor,
            unit_of_measure_id,
            location_id,
            datasource_id,
            writeback,
          };
        }
      ),
    };
  }

  measurementOverridesToDomain(
    measurementOverride: MeasurementInstanceCreationDto
  ): AssetMeasurementOverride {
    return {
      ...measurementOverride,
      locationId: measurementOverride.location_id,
      datasourceId: measurementOverride.datasource_id,
      metricId: measurementOverride.metric_id,
      unitOfMeasureId: measurementOverride.unit_of_measure_id,
      meterFactor: measurementOverride.meter_factor,
    };
  }
}
