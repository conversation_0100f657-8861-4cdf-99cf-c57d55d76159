import { Migration } from '@mikro-orm/migrations';

export class Migration20240214151551 extends Migration {
  async up(): Promise<void> {
    if (!this.ctx) {
      throw new Error('No transaction context found');
    }

    await this.getKnex()
      .update('is_base', true)
      .from('units_group')
      .whereRaw("lower(name) = 'base'")
      .transacting(this.ctx);
  }

  async down(): Promise<void> {
    // do nothing
  }
}
