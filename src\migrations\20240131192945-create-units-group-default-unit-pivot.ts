import { Migration } from '@mikro-orm/migrations';

export class Migration20240131192945 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'create table "units_group_default_unit" ("units_group_id" int not null, "unit_group_unit_id" int not null, constraint "units_group_default_unit_pkey" primary key ("units_group_id", "unit_group_unit_id"));',
    );

    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_units_group_id_foreign" foreign key ("units_group_id") references "units_group" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_unit_group_unit_id_foreign" foreign key ("unit_group_unit_id") references "units_group_unit" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "units_group_default_unit" cascade;');
  }
}
