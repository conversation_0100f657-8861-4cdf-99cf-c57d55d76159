import { UserId } from "src/users/domain/user.entity";
import { AssetId } from "../asset.entity";
import { CustomerId } from "src/customers/domain/customer.entity";
import { EntityManager } from "@mikro-orm/core";

export const ASSET_UPDATE_EVENT_KEY = "asset.update";
export class AssetUpdateEvent {
  constructor(
    readonly id: AssetId,
    readonly occuredAt: Date,
    readonly runById: UserId,
    readonly cust_id: CustomerId,
    readonly headers: Request["headers"] | undefined,
    readonly emManager?: EntityManager
  ) {}

  readonly key = ASSET_UPDATE_EVENT_KEY;
}
