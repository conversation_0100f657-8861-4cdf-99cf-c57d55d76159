import { <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, <PERSON><PERSON>ey, Property } from "@mikro-orm/core";
import { User } from "src/users/domain/user.entity";

@Entity()
export class ForgotPassword {
  @PrimaryKey()
  id: number;

  @ManyToOne({ entity: () => User, fieldName: "user" })
  user!: User;

  @Property()
  token!: string;

  @Property({ length: 6, fieldName: "expire" })
  expire: Date;
}
