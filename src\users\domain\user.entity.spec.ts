import { Test } from '@nestjs/testing';
import { User, UserFactory } from './user.entity';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';

describe('User', () => {
  const commonUserData = {
    username: 'test_user',
    password: 'somepass',
    email: '<EMAIL>',
    firstName: 'f',
    lastName: 'l',
  };

  beforeAll(async () => {
    await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer])],
    }).compile();
  });

  describe('creation', () => {
    test('admin with global role should only have global role', () => {
      const admin = UserFactory.create({
        ...commonUserData,
        globalRole: Role.ADMIN,
      });

      expect(admin.globalRole).toBe(Role.ADMIN);
      expect(admin.customerRoles.length).toBe(0);
    });

    test('user with global role should only have global role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(user.globalRole).toBe(Role.USER);
      expect(user.customerRoles.length).toBe(0);
    });

    test('single customer scoped admin should only have scoped role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [2] }],
      });

      expect(user.globalRole).toBeUndefined();
      expect(user.scopedRole(Role.ADMIN)).toStrictEqual([2]);
    });

    test('multiple customer scoped admin should only have scoped role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [2, 7, 8] }],
      });

      expect(user.globalRole).toBeUndefined();
      expect(user.scopedRole(Role.ADMIN)).toStrictEqual([2, 7, 8]);
    });

    test('multiple customer scoped admin and user should only have scoped role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [
          { role: Role.ADMIN, customerIds: [2, 7] },
          { role: Role.USER, customerIds: [6] },
        ],
      });

      expect(user.globalRole).toBeUndefined();
      expect(user.scopedRole(Role.ADMIN)).toStrictEqual([2, 7]);
      expect(user.scopedRole(Role.USER)).toStrictEqual([6]);
    });

    test('multiple customer scoped user should only have merged scoped role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [
          { role: Role.USER, customerIds: [34] },
          { role: Role.USER, customerIds: [54] },
        ],
      });

      expect(user.globalRole).toBeUndefined();
      expect(user.scopedRole(Role.USER)).toStrictEqual([34, 54]);
    });

    test('scoped user with same customer in different roles should throw an error', () => {
      expect(() =>
        UserFactory.create({
          ...commonUserData,
          scopedRoles: [
            { role: Role.USER, customerIds: [34] },
            { role: Role.ADMIN, customerIds: [34, 43] },
          ],
        }),
      ).toThrow('Customer can only belong to one scope');
    });

    test('scoped user with multiple customer in different roles should return scoped roles object', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [
          { role: Role.USER, customerIds: [34] },
          { role: Role.ADMIN, customerIds: [43, 56] },
        ],
      });

      expect(user.scopedRoles).toStrictEqual([
        { role: Role.USER, customerIds: [34] },
        { role: Role.ADMIN, customerIds: [43, 56] },
      ]);
    });
  });

  describe('querying hasGlobalScope', () => {
    test('on user with global scope should return true', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(user.hasGlobalRole()).toBeTruthy();
    });

    test('on scoped user should return false', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.USER, customerIds: [4] }],
      });

      expect(user.hasGlobalRole()).toBeFalsy();
    });
  });

  describe('querying hasCustomerScope', () => {
    test('on user with global scope should always return true', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(user.hasCustomerScope(43)).toBeTruthy();
    });

    test('on scoped user without customer should return false', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.USER, customerIds: [4] }],
      });

      expect(user.hasCustomerScope(43)).toBeFalsy();
    });

    test('on scoped user with customer should return true', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [
          { role: Role.USER, customerIds: [4] },
          { role: Role.ADMIN, customerIds: [43] },
        ],
      });

      expect(user.hasCustomerScope(43)).toBeTruthy();
    });
  });

  describe('querying hasCustomerScopeWithRole', () => {
    test('on global scope user should return true for user role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(user.hasCustomerScopeWithRole(43, Role.USER)).toBeTruthy();
    });

    test('on global scope user should return false for admin role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(user.hasCustomerScopeWithRole(43, Role.ADMIN)).toBeFalsy();
    });

    test('on scoped admin user should return true for customer in scope with role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [3] }],
      });

      expect(user.hasCustomerScopeWithRole(3, Role.ADMIN)).toBeTruthy();
    });

    test('on scoped admin user should return false for customer outside scope', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [43] }],
      });

      expect(user.hasCustomerScopeWithRole(3, Role.ADMIN)).toBeFalsy();
    });

    test('on scoped admin user should return false for customer in scope with other role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.USER, customerIds: [3] }],
      });

      expect(user.hasCustomerScopeWithRole(3, Role.ADMIN)).toBeFalsy();
    });
  });

  describe('querying hasCustomerScopeWithAnyAllowedRole', () => {
    test('on global scope user should return true for user role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(43, [Role.USER]),
      ).toBeTruthy();
    });

    test('on global scope user should return true for user or admin role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(43, [Role.ADMIN, Role.USER]),
      ).toBeTruthy();
    });

    test('on global scope user should return false for admin role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        globalRole: Role.USER,
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(43, [Role.ADMIN]),
      ).toBeFalsy();
    });

    test('on scoped admin user should return true for customer in scope with role', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [3] }],
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(3, [Role.ADMIN]),
      ).toBeTruthy();
    });

    test('on scoped power user should return true for customer in scope with role admin or power user', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [3] }],
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(3, [
          Role.ADMIN,
          Role.POWER_USER,
        ]),
      ).toBeTruthy();
    });

    test('on scoped admin user should return false for customer outside scope', () => {
      const user = UserFactory.create({
        ...commonUserData,
        scopedRoles: [{ role: Role.ADMIN, customerIds: [43] }],
      });

      expect(
        user.hasCustomerScopeWithAnyAllowedRole(3, [Role.ADMIN]),
      ).toBeFalsy();
    });
  });
});
