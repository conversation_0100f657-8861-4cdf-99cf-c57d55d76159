import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { User } from "src/users/domain/user.entity";
import { ForgotPassword } from "../domain/forgot-password.entity";
import { NotFoundException } from "@nestjs/common";
import { NotificationService } from "src/notification/notification.service";
import * as crypto from "crypto";
import { InvalidInputException } from "src/errors/exceptions";
import { PasswordEncoder } from "src/security/password-encoder.service";
import { ConfigService } from "@nestjs/config";

export class ForgotPasswordRepository {
  private expireTimeInHr = 2;
  private frontEndUrl;
  constructor(
    @InjectRepository(ForgotPassword)
    private readonly forgotPassword: EntityRepository<ForgotPassword>,
    @InjectRepository(User)
    private readonly user: EntityRepository<User>,
    private readonly notification: NotificationService,
    private readonly passwordEncoder: PasswordEncoder,
    private configService: ConfigService
  ) {
    const { frontendUrl } = configService.get("kafka");
    this.frontEndUrl = frontendUrl;
  }

  async findUserByEmailUserNameSendEmail(userNameEmail: string) {
    const user = await this.user.findOne({
      $or: [
        {
          email: userNameEmail,
        },
        {
          username: userNameEmail,
        },
      ],
    });

    if (!user) {
      throw new NotFoundException("User not found");
    }

    const forgot = new ForgotPassword();
    forgot.user = user;
    forgot.expire = this.generateExpirationTime(this.expireTimeInHr);
    forgot.token = this.generateToken(45);
    await this.forgotPassword.persistAndFlush(forgot);

    await this.notification.sendForgotPasswordEmail(
      [user.email],
      "Forgot Password",
      this.forgotPasswordBody(forgot),
      this.forgotPasswordBodyHTML(forgot)
    );
  }
  forgotPasswordBody(forgotPassword: ForgotPassword): string {
    const resetLink = `${this.frontEndUrl}/forgot-password/reset?token=${forgotPassword.token}`;
    return `
      Hi ${forgotPassword.user.username || forgotPassword.user.email},
  
      You requested to reset your password. Please use the following link to reset your password:
      
      Reset Password Link: ${resetLink}
      
      This link will expire in ${this.expireTimeInHr} hours.
  
      If you did not request this password reset, please ignore this email or contact support if you have any concerns.
  
      Best regards,

    `;
  }

  forgotPasswordBodyHTML(forgotPassword: ForgotPassword): string {
    const resetLink = `${this.frontEndUrl}/forgot-password/reset?token=${forgotPassword.token}`;
    return `
      <div style="font-family: Arial, sans-serif; line-height: 1.6;">
        <p>Hi ${forgotPassword.user.username || forgotPassword.user.email},</p>
        
        <p>You requested to reset your password. Please click the link below to reset your password:</p>
        
        <p>
          <a href="${resetLink}" style="color: #00a76f; text-decoration: none;">
            <strong>Reset Password</strong>
          </a>
        </p>
        
        <p>This link will expire in 2 hours.</p>
        
        <p>If you did not request this password reset, please ignore this email or contact support if you have any concerns.</p>
        
        <p>Best regards,</p>
      </div>
    `;
  }

  private generateToken(length: number): string {
    return crypto
      .randomBytes(Math.ceil(length / 2))
      .toString("hex")
      .slice(0, length);
  }

  private generateExpirationTime(hours: number): Date {
    const expirationTime = new Date();
    expirationTime.setHours(expirationTime.getHours() + hours);
    return expirationTime;
  }
  async findUserByTokenAndResetPassowrd(token: string) {
    const userWithToken = await this.forgotPassword.findOne({
      token,
    });
    if (!userWithToken) {
      return new NotFoundException("Token is invalid or expired");
    }
    const currentTime = new Date();
    if (userWithToken.expire < currentTime) {
      throw new NotFoundException("Token has expired");
    }
    return { token };
  }

  async resetPassword({
    token,
    password,
  }: {
    token: string;
    password: string;
  }) {
    const userWithToken = await this.forgotPassword.findOne({
      token,
    });
    if (!userWithToken) {
      return new NotFoundException("Token is invalid or expired");
    }
    const currentTime = new Date();
    const expireTimeInMs = this.expireTimeInHr * 60 * 60 * 1000;
    if (
      currentTime.getTime() - userWithToken.expire.getTime() >
      expireTimeInMs
    ) {
      throw new NotFoundException("Token has expired");
    }
    userWithToken.expire = new Date();
    await this.forgotPassword.persistAndFlush(userWithToken);
    const user = await this.user.findOne({
      id: userWithToken.user.id,
    });
    if (!user) {
      throw new InvalidInputException("Invalid password");
    }
    user.password = await this.passwordEncoder.hash(password);
    user.updatedAt = new Date();
    await this.user.persistAndFlush(user);
  }
}
