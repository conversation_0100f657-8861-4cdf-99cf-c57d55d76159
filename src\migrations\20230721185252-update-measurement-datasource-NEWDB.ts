import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230721185252 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql('alter table "measurement" add column "datasource" int null;');
    this.addSql(
      'alter table "measurement" add constraint "datasource_fk" foreign key ("datasource") references "datasource" ("id") on update cascade on delete set null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "measurement" drop constraint "datasource_fk";');

    this.addSql('alter table "measurement" drop column "datasource";');
  }
}
