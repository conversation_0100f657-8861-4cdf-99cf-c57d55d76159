import { Migration } from '@mikro-orm/migrations';

export class Migration20240208193247 extends Migration {
  async up(): Promise<void> {
    if (!this.ctx) {
      throw new Error('No transaction context found');
    }

    const knex = this.getKnex();

    const assetTemplateTags = await knex
      .select<{ id: number; tag: string; asset_type: number }[]>(
        'atm.id',
        'atm.tag',
        'at.asset_type',
      )
      .from({ atm: 'asset_template_measurement' })
      .leftJoin({ at: 'asset_template' }, 'atm.asset_template', 'at.id')
      .transacting(this.ctx);

    const newMetrics = Array.from(
      assetTemplateTags
        .map((metricTag) => ({
          name: metricTag.tag,
          asset_type: metricTag.asset_type,
        }))
        // remove duplicates by creating a map with metric name and asset type as key
        .reduce((prev, newMetric) => {
          prev.set(`${newMetric.name};${newMetric.asset_type}`, newMetric);
          return prev;
        }, new Map<string, { name: string; asset_type: number }>())
        .values(),
    );

    // if no metrics need to be added, skip migration
    if (newMetrics.length === 0) {
      return;
    }

    const createdMetrics = await knex
      .insert(newMetrics)
      .returning<{ id: number; name: string; asset_type: number }[]>([
        'id',
        'name',
        'asset_type',
      ])
      .into('metric')
      .transacting(this.ctx);

    const metricsIdMap = createdMetrics.reduce((prev, metricTag) => {
      prev.set(`${metricTag.name};${metricTag.asset_type}`, metricTag.id);
      return prev;
    }, new Map<string, number>());

    const assetTemplateNewMetrics = assetTemplateTags
      .map(
        (assetTemplateTag) =>
          `(${assetTemplateTag.id}, ${metricsIdMap.get(
            `${assetTemplateTag.tag};${assetTemplateTag.asset_type}`,
          )})`,
      )
      .join();

    await knex
      .raw(
        `update asset_template_measurement atm set metric = v.metric from (values ${assetTemplateNewMetrics}) as v(id, metric) where v.id = atm.id`,
      )
      .transacting(this.ctx);
  }

  async down(): Promise<void> {
    // do nothing
  }
}
