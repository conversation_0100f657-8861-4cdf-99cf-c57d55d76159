import {
  <PERSON><PERSON>ty,
  ManyTo<PERSON>ne,
  PrimaryKey,
  Property,
  Unique,
} from "@mikro-orm/core";
import { User } from "src/users/domain/user.entity";

@Entity()
export class Diagram {
  @PrimaryKey()
  id!: number;

  @Unique({ name: "diagram_unique" })
  @Property({ length: 50 })
  name!: string;

  @Property({ length: 65536, type: "text" })
  data!: string;

  @ManyToOne({ entity: () => User, nullable: true, fieldName: "created_by" })
  created_by?: User;

  @ManyToOne({ entity: () => User, nullable: true, fieldName: "updated_by" })
  updated_by?: User;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;
}
