import { Migration } from '@mikro-orm/migrations';
import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20240222111400 extends NewDbMigration {

  async conditionalUp(): Promise<void> {
    this.addSql('create table "dashboard" ("id" serial primary key, "title" varchar(50) not null, "data" varchar(65536) not null, "created_by" int null, "updated_by" int null, "customer_id" int null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null);');

    this.addSql('alter table "dashboard" add constraint "dashboard_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "dashboard" add constraint "dashboard_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "dashboard" add constraint "dashboard_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade on delete set null;');

    this.addSql('alter table "customer" add column "logo" text null;');
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "dashboard" cascade;');

    this.addSql('alter table "customer" drop column "logo";');
  }

}
