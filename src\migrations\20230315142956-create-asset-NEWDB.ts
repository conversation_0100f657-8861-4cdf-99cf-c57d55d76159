import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230315142956 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "asset" ("id" serial primary key, "tag" varchar(50) not null, \
      "enabled" boolean null, "latitude" float4 null, "longitude" float4 null, \
      "a_type" int not null, "customer" int not null, "is_cust_primary" boolean null, \
      "description" varchar(100) null, "timezone" text null, \
      "created" timestamptz(6) null, \
      "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );

    this.addSql('create index "asset_a_type_idx" on "asset" ("a_type");');

    this.addSql(
      'alter table "asset" add constraint "type_fk" foreign key ("a_type") references "asset_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset" add constraint "cust_fk" foreign key ("customer") references "customer" ("id") on update cascade;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "asset" cascade;');
  }
}
