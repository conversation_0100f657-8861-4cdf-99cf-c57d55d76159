import { Test } from '@nestjs/testing';
import { AssetTypeService } from 'src/assets/asset-type.service';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Metric } from 'src/assets/domain/metric.entity';
import { MetricService } from 'src/assets/metric.service';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { DatasourceService } from 'src/measurements/datasource.service';
import { DataType } from 'src/measurements/domain/data-type.entity';
import { Datasource } from 'src/measurements/domain/datasource.entity';
import { Location } from 'src/measurements/domain/location.entity';
import { MeasurementType } from 'src/measurements/domain/measurement-type.entity';
import { ValueType } from 'src/measurements/domain/value-type.entity';
import { MeasurementsBackofficeService } from 'src/measurements/measurements-backoffice.service';
import { User } from 'src/users/domain/user.entity';
import {
  AssetTemplateCreationData,
  AssetTemplateService,
} from './asset-template.service';
import { AssetTemplateRepository } from './repository/asset-template.repository';

describe('AssetTemplateService', () => {
  describe('create', () => {
    test('non existing asset type should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingAssetTypeId,
      } = await createAssetTemplateService();

      expect(
        assetTemplateService.create(
          {
            ...toyotaEngineTemplate,
            assetTypeId: nonExistingAssetTypeId,
          },
          4,
        ),
      ).rejects.toThrow('Asset type does not exist');
    });

    test('non existing metric should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingMetricId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].metricId = nonExistingMetricId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Metric does not exist');
    });

    test('non existing measurement type should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingMeasurementTypeId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].measurementTypeId =
        nonExistingMeasurementTypeId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Measurement type does not exist');
    });

    test('non existing data type should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingDataTypeId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].dataTypeId = nonExistingDataTypeId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Data type does not exist');
    });

    test('non existing value type should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingValueTypeId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].valueTypeId = nonExistingValueTypeId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Value type does not exist');
    });

    test('non existing location should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingLocationId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].locationId = nonExistingLocationId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Location does not exist');
    });

    test('non existing datasource should throw an exception', async () => {
      const {
        assetTemplateService,
        toyotaEngineTemplate,
        nonExistingDatasourceId,
      } = await createAssetTemplateService();

      toyotaEngineTemplate.measurements[0].datasourceId =
        nonExistingDatasourceId;

      expect(
        assetTemplateService.create(toyotaEngineTemplate, 4),
      ).rejects.toThrow('Datasource does not exist');
    });
  });
});

const createAssetTemplateService = async () => {
  const nonExistingAssetTypeId = 404;
  const assetTypeServiceMock: Pick<AssetTypeService, 'findById'> = {
    findById: jest.fn(async (id) => {
      if (id === nonExistingAssetTypeId) {
        return null;
      } else {
        const engineAssetType = new AssetType({ name: 'Engine' });
        return engineAssetType;
      }
    }),
  };

  const nonExistingMetricId = 404;
  const metricServiceMock: Pick<MetricService, 'findById'> = {
    findById: jest.fn(async (id) => {
      if (id === nonExistingMetricId) {
        return null;
      } else {
        return new Metric({
          name: 'Pump',
          assetType: new AssetType({ name: 'Pump' }),
        });
      }
    }),
  };

  const nonExistingMeasurementTypeId = 404;
  const nonExistingDataTypeId = 404;
  const nonExistingValueTypeId = 404;
  const nonExistingLocationId = 404;
  const measurementsBackofficeServiceMock: Pick<
    MeasurementsBackofficeService,
    | 'getMeasurementTypeById'
    | 'getDataTypeById'
    | 'getValueTypeById'
    | 'getLocationById'
  > = {
    getMeasurementTypeById: jest.fn(async (id) => {
      if (id === nonExistingMeasurementTypeId) {
        return null;
      } else {
        return new MeasurementType();
      }
    }),
    getDataTypeById: jest.fn(async (id) => {
      if (id === nonExistingDataTypeId) {
        return null;
      } else {
        return new DataType();
      }
    }),
    getValueTypeById: jest.fn(async (id) => {
      if (id === nonExistingValueTypeId) {
        return null;
      } else {
        return new ValueType();
      }
    }),
    getLocationById: jest.fn(async (id) => {
      if (id === nonExistingLocationId) {
        return null;
      } else {
        return new Location();
      }
    }),
  };

  const nonExistingDatasourceId = 404;
  const datasourceServiceMock: Pick<DatasourceService, 'getById'> = {
    getById: jest.fn(async (id) => {
      if (id === nonExistingDatasourceId) {
        return null;
      } else {
        return new Datasource();
      }
    }),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [createMikroOrmTestModule([AssetType, User, Customer])],
    providers: [
      {
        provide: AssetTemplateRepository,
        useValue: {},
      },
      {
        provide: AssetTypeService,
        useValue: assetTypeServiceMock,
      },
      {
        provide: MetricService,
        useValue: metricServiceMock,
      },
      {
        provide: MeasurementsBackofficeService,
        useValue: measurementsBackofficeServiceMock,
      },
      {
        provide: DatasourceService,
        useValue: datasourceServiceMock,
      },
      AssetTemplateService,
    ],
  }).compile();
  const assetTemplateService = moduleRef.get(AssetTemplateService);

  const toyotaEngineTemplate: AssetTemplateCreationData = {
    manufacturer: 'Toyota',
    modelNumber: 'Hilux',
    assetTypeId: 43,
    measurements: [
      {
        metricId: 5,
        dataTypeId: 6,
        valueTypeId: 8,
        measurementTypeId: 7,
      },
    ],
  };

  return {
    assetTemplateService,
    toyotaEngineTemplate,
    nonExistingAssetTypeId,
    nonExistingMetricId,
    nonExistingMeasurementTypeId,
    nonExistingDataTypeId,
    nonExistingValueTypeId,
    nonExistingLocationId,
    nonExistingDatasourceId,
  };
};
