import { ApiProperty } from "@nestjs/swagger";
import { DashboardCreationData } from "../dashboard.service";
import { Dashboard } from "../domain/dashboard.entity";
import { IsBoolean, IsNotEmpty, IsNumber } from "class-validator";

type DashboardCreationDtoType = Omit<DashboardCreationData, ""> & {
  customer_id: number;
};

export class DashboardCreationDto implements DashboardCreationDtoType {
  customer_id!: number;
  title!: string;
  data!: string;
  asset_id?: number;
  dashboard_template_id?: number;
}
export class DashboardUpdateDto extends DashboardCreationDto {
  id!: number;
}

export class DefaultDashboardCreationDto {
  @ApiProperty({ required: true, example: 1 })
  @IsNumber()
  @IsNotEmpty()
  dashboard_id: number;

  @ApiProperty({ required: true, example: true })
  @IsBoolean()
  @IsNotEmpty()
  status: boolean;
}

export class DefaultDashboardType extends Dashboard {
  default: boolean;
}

export class DashboardSuccessResDto {
  @ApiProperty({ required: true, example: 1 })
  id: number;

  @ApiProperty({ required: true, example: "dashboard-123" })
  title: string;

  @ApiProperty({ required: true, example: "{apple:ioioiio}" })
  data: string;
}

export class DashboardConflictResDto {
  @ApiProperty({ required: true, example: 409 })
  statusCode: number;

  @ApiProperty({ required: true, example: "Dashboard already exists" })
  message: string;

  @ApiProperty({ required: true, example: "Conflict" })
  error: string;
}
