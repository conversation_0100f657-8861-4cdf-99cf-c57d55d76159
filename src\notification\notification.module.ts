import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { NotificationService } from "./notification.service";
import { NotificationController } from "./notification.controller";
import { TwillioModule } from "src/twillio/twillio.module";
import { ConfigModule } from "@nestjs/config";

@Module({
  imports: [ConfigModule, TwillioModule],
  providers: [
    NotificationService,
    {
      provide: Logger,
      useValue: new Logger(NotificationService.name),
    },
  ],
  controllers: [NotificationController],
})
export class NotificationModule {}
