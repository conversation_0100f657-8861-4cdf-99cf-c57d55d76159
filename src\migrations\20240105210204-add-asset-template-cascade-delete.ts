import { Migration } from '@mikro-orm/migrations';

export class Migration20240105210204 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_foreign" foreign key ("meter_template") references "asset_template" ("id") on update cascade on delete cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_meter_template_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_meter_template_foreign" foreign key ("meter_template") references "asset_template" ("id") on update cascade;',
    );
  }
}
