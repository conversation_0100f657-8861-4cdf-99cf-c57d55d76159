# #frontend
# FRONTEND_URL=https://test.brompton.ai

# #server
# SERVER_PORT=3030

# #db
# DB_NAME=dataloggertest
# DB_HOST=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com
# DB_PORT=5432
# DB_USER=postgres
# DB_PASSWORD=Br0mpt0n!0T
# DB_SSL=false

# #redis
# REDIS_HOST=abc3caac4d9a04442aae84e98732761a-9c7af11e803ab993.elb.us-east-1.amazonaws.com
# #REDIS_PORT=6379
# REDIS_PORT=26379
# REDIS_PASSWORD=test@123
# REDIS_SENTINEL_MASTER=mymaster

# #auth
# AUTH_JWT_SECRET="this is a very secret secret"
# AUTH_SESSION_DURATION_MIN=43200

# #security
# SECURITY_CORS_ORIGIN_URL=https://test.brompton.ai
# AUTH_COOKIE_DOMAIN=brompton.ai

# #timeseries-api
# TS_API_HOST=af1d2a7a722c247aa8aeeecba8d1d245-191a79e48c6c3408.elb.us-east-1.amazonaws.com
# TS_API_PORT=443
# TS_API_SSL=false
# TS_API_VERSION=v1_0

# MQTT_URL=mqtt://bromptonenergy.io:8883
# MQTT_USERNAME=
# MQTT_PASSWORD=
# MQTT_TOPIC_ALERT=spBv1.0/Brenes_Gateway/NDATA/#
# MQTT_TOPIC_NOTIFICATION=be-notification-dev-new
# MQTT_CLIENT_ID=jfien
# MQTT_ROLE=publisher
# MQTT_QOS=2

# #Twillio_API
# SEND_NOTIFICATIONS=true
# TWILLIO_ACCOUNT_SID=**********************************
# TWILLIO_AUTH_TOKEN=46910f888ea461bcde388a682d069592
# SMS_FROM_NO=+***********
# TWILLIO_EMAIL_API_TOKEN=*********************************************************************
# EMAIL_FROM=<EMAIL>

# #alert-schduler-api
# SCHEDULER_ENDPOINT=https://test.brompton.ai/api/scheduler-api/



# #AWS-SES
# REGION=us-east-1
# EMAIL_FROM=<EMAIL>
# ACCESS_KEY_ID=********************
# SECRET_ACCESS_KEY=hyBYiQYDuRnIyxTsVYpwXBb6Z1baoSZQeZe1iqdV


# #KAFKA_CONFIG
# KAFKA_BROKER=kafka-0.kafka.kafka.svc.cluster.local:9092
# KAFKA_CLIENT_ID=nestjs-client
# KAFKA_TOPIC_ALERT=be-alert-dev-new
# KAFKA_TOPIC_NOTIFICATION=be-notification-dev-new
# KAFKA_GROUP_ID=nestjs-consumer
# KAFKA_GROUP_ID_INSTANCE=nestjs-consumer-instance

# KAFKA_NOTIFICATION_GROUP_ID=notification-consumer
# KAFKA_NOTIFICATIONP_ID_INSTANCE=notification-consumer-instance