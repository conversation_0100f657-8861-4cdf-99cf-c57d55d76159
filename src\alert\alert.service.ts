import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NotFoundError, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { HttpService } from "@nestjs/axios";
import {
  ConflictException,
  HttpException,
  Injectable,
  Logger,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { catchError, firstValueFrom } from "rxjs";
import { Customer } from "src/customers/domain/customer.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { InvalidInputException } from "src/errors/exceptions";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Asset } from "../assets/domain/asset.entity";
import { Measurement } from "../measurements/domain/measurement.entity";
import { User } from "../users/domain/user.entity";
import { Aggregates } from "./domain/aggregates.entity";
import { Alerts } from "./domain/alert.entity";
import { AlertCondition } from "./domain/alertCondition.entity";
import { AlertThresholdType } from "./domain/alertThresholdType.entity";
import { AlertUsers } from "./domain/alertUser.entity";
import { AnomalyModel } from "./domain/anomalyModel.entity";
import { AnomalyParameter } from "./domain/anomalyParameter.entity";
import { Events } from "./domain/events.entity";
import { Periods } from "./domain/periods.entity";
import { AlertDTO } from "./dto/alert.dto";
import { AlertRepository } from "./repository/alert.repository";
import { DataType } from "src/measurements/domain/data-type.entity";
import { MeasurementType } from "src/measurements/domain/measurement-type.entity";
import { ValueType } from "src/measurements/domain/value-type.entity";

@Injectable()
export class AlertService {
  private alertScheduleURL: string;
  private fast_api: string;
  constructor(
    @InjectRepository(Alerts)
    private readonly alert: EntityRepository<Alerts>,
    @InjectRepository(AlertCondition)
    private readonly alertCondition: EntityRepository<AlertCondition>,
    @InjectRepository(Aggregates)
    private readonly aggregates: EntityRepository<Aggregates>,
    @InjectRepository(AlertThresholdType)
    private readonly alertThresholdType: EntityRepository<AlertThresholdType>,
    @InjectRepository(Measurement)
    private readonly measurement: EntityRepository<Measurement>,
    @InjectRepository(Asset)
    private readonly asset: EntityRepository<Asset>,
    @InjectRepository(Periods)
    private readonly periods: EntityRepository<Periods>,
    @InjectRepository(User)
    private readonly user: EntityRepository<User>,
    @InjectRepository(AlertUsers)
    private readonly alertUsers: EntityRepository<AlertUsers>,
    @InjectRepository(Customer)
    private readonly customer: EntityRepository<Customer>,
    private readonly alertRepository: AlertRepository,
    private configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly transactionFactory: TransactionFactory,
    @InjectRepository(Events)
    private readonly events: EntityRepository<Events>,
    @InjectRepository(AssetMeasurement)
    private readonly assetMeasurementRepository: EntityRepository<AssetMeasurement>,
    @InjectRepository(AnomalyModel)
    private readonly anomalyModel: EntityRepository<AnomalyModel>,
    private readonly logger: Logger,
    private readonly em: EntityManager, // Injecting EntityManager here
    @InjectRepository(DataType)
    private readonly dataTypeRepo: EntityRepository<DataType>,
    @InjectRepository(MeasurementType)
    private readonly measurementTypeRepo: EntityRepository<MeasurementType>,
    @InjectRepository(ValueType)
    private readonly valueTypeRepo: EntityRepository<ValueType>
  ) {}
  async onModuleInit(): Promise<void> {
    this.alertScheduleURL = this.configService.get("alert_scheduler_endpoint");
    this.fast_api =
      this.configService.get("fastApi") ?? "http://localhost:8000";
  }

  async getEvents(id: number) {
    const events = await this.events.findOne(id, {
      populate: [
        "asset_id",
        "measurement_id",
        "aggregate",
        "period",
        "comparator",
        "alert_id",
      ],
    });
    if (!events) throw new HttpException("Event not found", 404);
    const measurement = JSON.parse(JSON.stringify(events.measurement_id));
    const asset_measurement = await this.assetMeasurementRepository.findOne(
      { measurement: measurement.id },
      { populate: ["asset", "measurement"] }
    );
    return { ...events, asset_measurement };
  }

  async getEventsByAlertId(alert_id: number) {
    const events = await this.events.find(
      { alert_id: alert_id },
      {
        orderBy: { id: "ASC" },
        populate: ["aggregate", "period", "comparator"],
      }
    );
    return {
      total: events.length,
      items: events,
    };
  }
  async getAlerts({ customerId }: { customerId: number }) {
    const alerts = await this.alert.find(
      { customerId, deletedAt: null },
      {
        populate: [
          "agg",
          "period",
          "thresholdType",
          "condition",
          "asset",
          "measurement._tag",
          "asset.tag",
          "measurement.metric",
          "alertUsers",
        ],
      }
    );
    // optimizing the query to fetch events for all alerts in one go

    return {
      total: alerts.length,
      items: alerts.map((alert) => ({
        ...alert,
        deletedBy: alert.deletedBy?.id ?? null,
        customerId: alert.customerId?.toString() ?? "",
        measurement: {
          id: alert.measurement.id,
          tag: alert.measurement._tag,
          metric: alert.measurement.metric?.id ?? null,
          metricName: alert.measurement.metric?.name ?? null,
          measurementType: alert.measurement.typeId ?? null,
        },
      })),
    };
  }

  async getAllEvents({
    customerId,
    start,
    end,
  }: {
    customerId: number;
    start: number;
    end: number;
  }) {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const events = await this.events.find(
      {
        alert_id: {
          customerId: customerId,
        },
        timestamp: {
          $gte: new Date(start),
          $lte: new Date(end),
        },
      },
      {
        orderBy: { timestamp: "DESC" },
        populate: [
          "input_value",
          "aggregate",
          "period",
          "comparator",
          "measurement_id._tag",
          "measurement_id.measurementType",
          "measurement_id",
          "asset_id",
          "alert_id.description",
        ],
      }
    );
    return {
      total: events.length,
      items: events.map((event) => ({
        ...event,
        measurement: {
          id: event.alert_id.measurement.id,
          tag: event.alert_id.measurement._tag,
        },
        asset: {
          id: event.alert_id.asset.id,
          tag: event.alert_id.asset.tag,
        },
      })),
    };
  }
  async getAlert(id: number, requireAsset: boolean = false) {
    const alert = await this.alert.findOne(id, {
      populate: requireAsset
        ? ["measurement", "alertUsers", "asset", "anomalyParameter"]
        : ["measurement", "alertUsers", "anomalyParameter"],
    });

    if (!alert) throw new NotFoundError("Alert not found");
    return {
      id: alert.id,
      asset: alert.asset?.id?.toString() ?? "",
      measurement: {
        id: alert.measurement?.id ?? null,
        tag: alert.measurement?._tag ?? "",
      },
      customerId: alert.customerId?.toString() ?? "",
      agg: alert.agg?.id?.toString() ?? "",
      period: alert.period?.id?.toString() ?? "",
      thresholdType: alert.thresholdType?.id?.toString() ?? "",
      condition: alert.condition?.id ?? null,
      thresholdValue: alert.thresholdValue ?? null,
      resetDeadband: alert.resetDeadband ?? null,
      notificationType: alert.notificationType ?? 0,
      description: alert.description ?? "",

      // Populate anomaly parameter fields directly
      learningPeriod: alert.anomalyParameter?.learningPeriod ?? null,
      includeVelocity: alert.anomalyParameter?.includeVelocity ?? false,
      includeMomentum: alert.anomalyParameter?.includeMomentum ?? false,
      alertUsers:
        alert.alertUsers?.map((user) => ({
          id: user?.id ?? null,
          user: user?.user?.id ?? null,
          notificationtype: user?.notificationtype ?? 0,
        })) ?? [],
    };
  }

  async getAlertUsers(id: number) {
    const user = await this.em.fork().findOne(User, { id: id });
    return user;
  }
  async getAlertNew(id: number, requireAsset: boolean = false) {
    const em = this.em.fork(); // Forking EntityManager for scoped usage
    const alert = await em.findOne(Alerts, id, {
      populate: requireAsset
        ? ["measurement", "alertUsers", "asset", "thresholdType"]
        : ["measurement", "alertUsers"],
    });
    if (!alert) throw new NotFoundError("Alert not found");

    const alertDetails = JSON.parse(JSON.stringify(alert));
    alertDetails.measurement = {
      id: alert.measurement.id,
      tag: alert.measurement._tag,
    };
    return alertDetails;
  }

  async getAggregates(): Promise<{ total: number; items: Aggregates[] }> {
    const items = await this.aggregates.findAll();
    return { total: items.length, items };
  }

  async getThresholdTypes(): Promise<{
    total: number;
    items: AlertThresholdType[];
  }> {
    const itmes = await this.alertThresholdType.findAll();
    return {
      total: itmes.length,
      items: itmes,
    };
  }

  async getAlertConditions(): Promise<{
    total: number;
    items: AlertCondition[];
  }> {
    const items = await this.alertCondition.findAll();
    return { total: items.length, items };
  }

  async createAlert(alert: AlertDTO, headers: Request["headers"], user: any) {
    return this.transactionFactory.run(async (em) => {
      const agg = await this.aggregates.findOne(alert.agg);
      if (!agg) throw new NotFoundError("Aggregates not found");
      const customer = await this.customer.findOne(alert.customerId);
      if (!customer) throw new NotFoundError("Customer not found");
      let period = null;
      if (alert.period) {
        period = await this.periods.findOne({
          id: alert.period,
        });
        if (!period) throw new NotFoundError("Period not found");
      }
      const thresholdType = await this.alertThresholdType.findOne({
        id: alert.thresholdType,
      });
      if (!thresholdType) throw new NotFoundError("Threshold type not found");
      const condition = alert.condition
        ? await this.alertCondition.findOne({ id: alert.condition })
        : null;
      if (alert.condition && !condition)
        throw new NotFoundError("Condition not found");
      const measurement = await this.measurement.findOne({
        id: alert.measurement,
      });
      if (!measurement) throw new NotFoundError("Measurement not found");
      const asset = await this.asset.findOne({
        id: alert.asset,
      });
      if (!asset) throw new NotFoundError("Asset not found");
      const uniqueUsers = new Set(alert.users.map((user) => user.id));
      if (uniqueUsers.size !== alert.users.length) {
        throw new Error("Duplicate users not allowed");
      }
      const users = await this.user.find({
        id: alert.users.map((user) => user.id),
      });
      if (users.length !== alert.users.length) {
        throw new NotFoundError("User not found");
      }

      const createAlert = new Alerts();
      createAlert.agg = agg;
      createAlert.period = period;
      createAlert.thresholdType = thresholdType;
      createAlert.condition = condition ?? null;

      createAlert.thresholdValue =
        alert.thresholdValue && alert.thresholdValue !== 0
          ? alert.thresholdValue
          : null;

      createAlert.resetDeadband =
        alert.resetDeadband && alert.resetDeadband !== 0
          ? alert.resetDeadband
          : null;
      createAlert.measurement = measurement;
      createAlert.asset = asset;
      createAlert.description = alert.description;
      createAlert.customerId = customer.id;
      createAlert.enabled = true;
      createAlert.alertUsers = users.map((user) => {
        const alertUser = new AlertUsers();
        alertUser.user = user;
        alertUser.notificationtype =
          alert.users.find((u) => u.id === user.id).notificationType ?? 0;
        return alertUser;
      });
      createAlert.createdAt = new Date();
      const loggedInUserId = user?.id ?? null;

      if (thresholdType.id === 2) {
        const [dataType, measurementType, valueType] = await Promise.all([
          this.dataTypeRepo.findOneOrFail({ name: "INT" }),
          this.measurementTypeRepo.findOneOrFail({ name: "Status" }),
          this.valueTypeRepo.findOneOrFail({ name: "nominal" }),
        ]);
        // ApparentEnergyAnomalyMIN5min
        const anomalyMeasurement = new Measurement({
          tag: `${measurement.tag}Anomaly${agg.label}${period.label}`,
          dataType,
          measurementType,
          valueType,
          description: `Anomaly output for ${measurement.tag}`,
          datasource: measurement.datasource ?? null,
          metric: measurement.metric ?? null,
          writeback: true,
        });

        await this.measurement.persistAndFlush(anomalyMeasurement);

        const assetMeasurement = new AssetMeasurement({
          tag: anomalyMeasurement.tag,
          metric: anomalyMeasurement.metric ?? null,
          assetId: asset.id,
          measurementType,
          dataType,
          valueType,
          meterFactor: anomalyMeasurement.meterFactor ?? null,
          unitOfMeasure: anomalyMeasurement.unitOfMeasure ?? null,
          datasource: anomalyMeasurement.datasource ?? null,
          description: anomalyMeasurement.description ?? "",
          writeback: true,
        });

        assetMeasurement.measurement = anomalyMeasurement;
        assetMeasurement.createdById = loggedInUserId;

        await this.assetMeasurementRepository.persistAndFlush(assetMeasurement);

        // ✅ Call TimeSeries API for anomaly measurement
        const timeseriesUrl = `${this.fast_api}/api/v1_0/timeseries/anomaly_measurement/${customer.id}/${anomalyMeasurement.id}`;

        try {
          const tsResponse = await this.httpProxy(
            timeseriesUrl,
            {},
            {
              "be-csrftoken": headers["be-csrftoken"],
              Cookie: headers["cookie"],
            }
          );
        } catch (error) {
          this.logger.error(
            "Failed to register anomaly measurement in TimeSeries API",
            (error as any).message
          );
        }

        const anomalyModel = new AnomalyModel();
        anomalyModel.alert = createAlert;
        anomalyModel.anomalyMeasurement = anomalyMeasurement.id;
        anomalyModel.createdAt = new Date();
        anomalyModel.updatedAt = new Date();
        anomalyModel.createdBy = loggedInUserId;
        anomalyModel.updatedBy = loggedInUserId;
        createAlert.anomalyModel = anomalyModel;

        const anomalyParameter = new AnomalyParameter();
        anomalyParameter.alert = createAlert.id;
        anomalyParameter.learningPeriod = alert.learningPeriod ?? null;
        anomalyParameter.includeVelocity = alert.includeVelocity ?? null;
        anomalyParameter.includeMomentum = alert.includeMomentum ?? null;
        anomalyParameter.createdAt = new Date();
        anomalyParameter.updatedAt = new Date();
        anomalyParameter.createdBy = loggedInUserId;
        anomalyParameter.updatedBy = loggedInUserId;
        createAlert.anomalyParameter = anomalyParameter;
      }

      await this.alert.persistAndFlush(createAlert);
      await em.commit();
      const data = await this.httpProxy(
        this.alertScheduleURL + "alert",
        {
          alert_id: createAlert.id,
          customer_id: customer.id,
          aggregate: agg.value,
          ...(period && { aggregate_period: period.label }),
        },
        {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        }
      );
    });
  }

  private async httpProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .post(url, body, {
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            this.logger.error(
              "alert error",
              url,
              body,
              error.message,
              error.response
            );
            throw new InvalidInputException(
              error.message ?? "Error creating alert"
            );
          })
        )
    );
    this.logger.log("alert response", data);
    return data;
  }
  private async httpUpdateAlertProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .put(url, body, {
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            this.logger.error(
              "Update alert error",
              url,
              body,
              error.message,
              error.response
            );
            throw new InvalidInputException(
              error.message ?? "Error updating alert"
            );
          })
        )
    );
    this.logger.log("Update alert response", data);
    return data;
  }
  private async httpDeleteAlertProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .delete(url, {
          data: body,
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            this.logger.error(
              "Delete alert error",
              url,
              body,
              error.message,
              error.response
            );
            throw new InvalidInputException("Error deleting alert");
          })
        )
    );
    this.logger.log("Delete alert response", data);
    return data;
  }
  async getPeriods(): Promise<{ total: number; items: Periods[] }> {
    const items = await this.periods.findAll({
      orderBy: { sort_order: "ASC" },
    });
    return { total: items.length, items };
  }

  async updateAlert(
    id: number,
    alert: AlertDTO,
    headers: Request["headers"],
    user: any
  ) {
    return await this.transactionFactory.run(async (em) => {
      const updateAlert = await this.alert.findOne(id, {
        populate: ["anomalyParameter", "anomalyModel"],
      });
      if (!updateAlert) throw new NotFoundError("Alert not found");

      const customer = await this.customer.findOne(alert.customerId);
      if (!customer) throw new NotFoundError("Customer not found");

      const uniqueUsers = new Set(alert.users.map((user) => user.id));
      if (uniqueUsers.size !== alert.users.length) {
        throw new Error("Duplicate users not allowed");
      }

      const users = await this.user.find({
        id: alert.users.map((user) => user.id),
      });
      if (users.length !== alert.users.length) {
        throw new NotFoundError("User not found");
      }

      const oldAgg = await this.aggregates.findOne(updateAlert.agg.id);
      const olderAgg = oldAgg.value;
      // const oldPeriod = await this.periods.findOne(updateAlert.period.id);
      // const olderPeriod = oldPeriod.label;
      let olderPeriod = null;
      let oldPeriod = null;
      if (updateAlert.period) {
        oldPeriod = await this.periods.findOne(updateAlert.period.id);
        olderPeriod = oldPeriod?.label ?? null;
      }

      // const agg = await this.aggregates.findOne(alert.agg);
      // updateAlert.agg = agg;
      // const period = await this.periods.findOne(alert.period);
      // updateAlert.period = period;
      const agg = await this.aggregates.findOne(alert.agg);
      updateAlert.agg = agg;

      let newPeriod = null;
      if (alert.period) {
        newPeriod = await this.periods.findOne(alert.period);
      }

      updateAlert.period = newPeriod;
      updateAlert.thresholdType.id = alert.thresholdType;
      updateAlert.condition =
        alert.condition && alert.condition !== 0
          ? await this.alertCondition.findOne({ id: alert.condition })
          : null;

      updateAlert.thresholdValue =
        alert.thresholdValue && alert.thresholdValue !== 0
          ? alert.thresholdValue
          : null;

      updateAlert.resetDeadband =
        alert.resetDeadband && alert.resetDeadband !== 0
          ? alert.resetDeadband
          : null;
      updateAlert.measurement.id = alert.measurement;
      updateAlert.asset.id = alert.asset;
      updateAlert.description = alert.description;
      updateAlert.updatedAt = new Date();
      const loggedInUserId = user?.id ?? null;

      if (String(alert.thresholdType) === "2") {
        // Update Anomaly Model
        let anomalyModel = updateAlert.anomalyModel;
        if (!anomalyModel) {
          anomalyModel = new AnomalyModel();
          anomalyModel.alert = updateAlert;
          anomalyModel.createdAt = new Date();
          anomalyModel.createdBy = loggedInUserId ?? null;
        }

        // Check if Anomaly Model Fields are Modified
        const anomalyModelChanged =
          alert.measurement !== anomalyModel.anomalyMeasurement;

        if (anomalyModelChanged) {
          anomalyModel.anomalyMeasurement = alert.measurement;
          anomalyModel.updatedAt = new Date();
          anomalyModel.updatedBy = loggedInUserId ?? null;
        }

        updateAlert.anomalyModel = anomalyModel;
        await em.persistAndFlush(anomalyModel);

        // Update Anomaly Parameter
        let anomalyParameter = updateAlert.anomalyParameter;
        if (!anomalyParameter) {
          anomalyParameter = new AnomalyParameter();
          anomalyParameter.alertRelation = updateAlert;
          anomalyParameter.createdAt = new Date();
          anomalyParameter.createdBy = loggedInUserId ?? null;
        }

        // Check if Anomaly Parameter Fields are Modified
        const anomalyParamChanged =
          alert.learningPeriod !== anomalyParameter.learningPeriod ||
          alert.includeVelocity !== anomalyParameter.includeVelocity ||
          alert.includeMomentum !== anomalyParameter.includeMomentum;

        if (anomalyParamChanged) {
          anomalyParameter.learningPeriod = alert.learningPeriod ?? null;
          anomalyParameter.includeVelocity = alert.includeVelocity ?? false;
          anomalyParameter.includeMomentum = alert.includeMomentum ?? false;
        }

        anomalyParameter.updatedAt = new Date();
        anomalyParameter.updatedBy = loggedInUserId ?? null;
        // Persist anomaly parameter changes
        updateAlert.anomalyParameter = anomalyParameter;
        await em.persistAndFlush(anomalyParameter);
      }

      await this.alertUsers.nativeDelete({ alert: updateAlert.id });

      updateAlert.alertUsers = users.map((user) => {
        const alertUser = new AlertUsers();
        alertUser.user = user;
        alertUser.notificationtype =
          alert.users.find((u) => u.id === user.id).notificationType ?? 0;
        return alertUser;
      });

      await this.alert.persistAndFlush(updateAlert);
      await em.commit();

      const newAgg = await this.aggregates.findOne(alert.agg);

      await this.httpUpdateAlertProxy(
        this.alertScheduleURL + "alert",
        {
          alert_id: updateAlert.id,
          customer_id: customer.id,
          previous_aggregate: olderAgg,
          // previous_aggregate_period: olderPeriod,
           ...(olderPeriod && { previous_aggregate_period: olderPeriod }),
          aggregate: newAgg.value,
          // aggregate_period: newPeriod.label,
          ...(newPeriod && { aggregate_period: newPeriod.label }),
        },
        {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        }
      );
    });
  }

  async enableAlert(id: number, headers: Request["headers"]) {
    return await this.transactionFactory.run(async (em) => {
      const alert = await this.alert.findOne(id);
      if (!alert) throw new NotFoundError("Alert not found");
      const customerId = await this.customer.findOne(alert.customerId);
      alert.enabled = alert.enabled ? false : true;
      alert.updatedAt = new Date();
      await this.alert.persistAndFlush(alert);

      const oldAgg = await this.aggregates.findOne(alert.agg.id);
      const oldPeriod = await this.periods.findOne(alert.period.id);
      // const newAgg = await this.aggregates.findOne(alert.agg);
      // const newPeriod = await this.periods.findOne(alert.period);
      if (alert.enabled) {
        await em.commit();
        await this.httpProxy(
          this.alertScheduleURL + "alert",
          {
            alert_id: alert.id,
            customer_id: customerId.id,
            aggregate: alert.agg.value,
            aggregate_period: alert.period.label,
          },
          {
            "be-csrftoken": headers["be-csrftoken"],
            Cookie: headers["cookie"],
          }
        );
      } else {
        await em.commit();
        await this.httpDeleteAlertProxy(
          this.alertScheduleURL + "alert",
          {
            alert_id: alert.id,
            customer_id: customerId.id,
            aggregate: oldAgg.value,
            aggregate_period: oldPeriod.label,
          },
          {
            "be-csrftoken": headers["be-csrftoken"],
            Cookie: headers["cookie"],
          }
        );
      }
    });
  }

  async getMeasurementAlerts(
    measureId: number
  ): Promise<{ total: number; items: Alerts[] }> {
    const items = await this.alert.find({ measurement: measureId });
    return {
      total: items.length,
      items,
    };
  }

  async deleteAlert(id: number, headers: Request["headers"], user: User) {
    return await this.transactionFactory.run(async (em) => {
      const alert = await this.alert.findOne(id);
      if (!alert) throw new NotFoundError("Alert not found");
      
      const agg = await this.aggregates.findOne(alert.agg.id);
      
      // Fix: Handle case where period might be null
      let period = null;
      let periodLabel = null;
      if (alert.period) {
        period = await this.periods.findOne(alert.period.id);
        periodLabel = period?.label ?? null;
      }
      
      alert.deletedBy = Reference.createFromPK(User, user.id);
      alert.deletedAt = new Date();
      await this.alert.persistAndFlush(alert);
      await em.commit();
      
      await this.httpDeleteAlertProxy(
        this.alertScheduleURL + "alert",
        {
          alert_id: id,
          customer_id: alert.customerId,
          aggregate: agg.value,
          // Only include aggregate_period if it exists
          ...(periodLabel && { aggregate_period: periodLabel }),
        },
        {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        }
      );
    });
  }
}
