import { Migration } from '@mikro-orm/migrations';

export class Migration20240102195330 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_default_units_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop column "default_units";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add column "unit_of_measure" int null, add column "created_at" timestamptz(6) null, add column "created_by" int null, add column "datasource" int null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_unit_of_measure_foreign" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "default_description" to "description";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "default_meter_factor" to "meter_factor";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_datasource_id_foreign" foreign key ("datasource") references "datasource" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_datasource_id_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_unit_of_measure_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_created_by_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop column "datasource";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop column "unit_of_measure";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop column "created_at";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop column "created_by";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "description" to "default_description";',
    );
    this.addSql(
      'alter table "asset_template_measurement" rename column "meter_factor" to "default_meter_factor";',
    );
    this.addSql(
      'alter table "asset_template_measurement" add column "default_units" int null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_default_units_foreign" foreign key ("default_units") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
  }
}
