apiVersion: v1
kind: ConfigMap
metadata:
  name: admin-api-prod-config
  namespace: application
data:
  NODE_ENV: "production"
  SERVER_PORT: "8081"
  
  # Database Configuration
  DB_HOST: "dataloggeraws.clclbj3j3ehf.us-east-1.rds.amazonaws.com"
  DB_NAME: "dataloggeraws"
  DB_PORT: "5432"
  DB_USER: "eks_rw"
  DB_SSL: "true"
  
  # Frontend URL
  FRONTEND_URL: "https://pivotol.ai"
  
  # API Configuration
  FAST_API: "https://pivotol.ai/api/timeseries"
  TS_API_HOST: "timeseries-api-service.application.svc.cluster.local"
  TS_API_PORT: "443"
  TS_API_SSL: "true"
  TS_API_VERSION: "v1_0"
  
  # MQTT Configuration
  MQTT_URL: "mqtt://mosquitto.mosquitto.svc.cluster.local"
  MQTT_USERNAME: "mosquitto"
  MQTT_TOPIC_ALERT: "be-alert-dev-new-3"
  MQTT_TOPIC_NOTIFICATION: "be-notification-dev"
  MQTT_CLIENT_ID: "beasdf"
  MQTT_ROLE: "publisher"
  
  # Notification Configuration
  SEND_NOTIFICATIONS: "true"
  SMS_FROM_NO: "+13854427050"
  EMAIL_FROM: "<EMAIL>"
  
  # Scheduler Configuration
  SCHEDULER_ENDPOINT: "http://scheduler-api.application.svc.cluster.local/"
  
  # AWS Configuration
  REGION: "us-east-1"
  
  # Kafka Configuration
  KAFKA_BROKER: "strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092"
  KAFKA_CLIENT_ID: "nestjs-client"
  KAFKA_TOPIC_ALERT: "be-alert-dev-new"
  KAFKA_TOPIC_NOTIFICATION: "be-notification-dev-new"
  KAFKA_GROUP_ID: "nestjs-consumer"
  KAFKA_GROUP_ID_INSTANCE: "nestjs-consumer-instance"
  KAFKA_NOTIFICATION_GROUP_ID: "notification-consumer"
  KAFKA_NOTIFICATIONP_ID_INSTANCE: "notification-consumer-instance"
  
  # Security Configuration
  SECURITY_CORS_ORIGIN_URL: "https://pivotol.ai"
  X_ACTIVE_CUSTOMER: "x-active-customer-id"
  AUTH_COOKIE_DOMAIN: "pivotol.ai"
  AUTH_SESSION_DURATION_MIN: "43200"
  
  # Weather API Configuration
  TEMPEST_WEATHER_API_URL: "https://swd.weatherflow.com/swd/rest"
  TEMPEST_WEATHER_API_TOKEN: "16f1648e-fd77-4fd4-ba09-b711e7de8733"
  
  # TLS Configuration
  NODE_TLS_REJECT_UNAUTHORIZED: "0"
