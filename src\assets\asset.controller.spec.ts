import { Test } from '@nestjs/testing';
import { AssetService } from './asset.service';
import { AssetApiController } from './asset.controller';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { testUserFactory } from 'src/users/__tests__/factories';
import {
  CustomerUserRole,
  Role,
} from 'src/authorization/domain/customer-user-role.entity';
import { User } from 'src/users/domain/user.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';

describe('AssetApiController', () => {
  let assetApiController: AssetApiController;
  let assetServiceMock: jest.Mocked<
    Pick<AssetService, 'create' | 'remove' | 'getAll' | 'findById'>
  >;
  let globalAdmin: User;
  let globalUser: User;
  let scopedAdmin: User;
  const newOfficeAssetParams = {
    tag: 'Office',
    type_id: 0,
    parent_ids: [],
  };

  beforeEach(async () => {
    assetServiceMock = {
      create: jest.fn(),
      remove: jest.fn(),
      getAll: jest.fn(async ({}) => []),
      findById: jest.fn(),
    };
    const moduleRef = await Test.createTestingModule({
      imports: [createMikroOrmTestModule([User, Customer, CustomerUserRole])],
      providers: [{ provide: AssetService, useValue: assetServiceMock }],
      controllers: [AssetApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    globalAdmin = testUserFactory.createGlobalScopeUser(
      'global_admin',
      Role.ADMIN,
    );
    globalUser = testUserFactory.createGlobalScopeUser(
      'global_user',
      Role.USER,
    );
    scopedAdmin = testUserFactory.createCustomerScopedUser('scoped_admin', [
      { role: Role.ADMIN, customerIds: [4] },
    ]);

    assetApiController = moduleRef.get(AssetApiController);
  });

  describe('create', () => {
    test('global admin should be able to create any asset', async () => {
      const globalAdmin = testUserFactory.createGlobalScopeUser(
        'global_admin',
        Role.ADMIN,
      );

      await assetApiController.create(globalAdmin, '42', newOfficeAssetParams);

      expect(assetServiceMock.create.mock.calls.length).toBe(1);
    });

    test('scoped admin should not be able to create asset on customer outside scope', () => {
      expect(() =>
        assetApiController.create(scopedAdmin, '42', newOfficeAssetParams),
      ).rejects.toThrow('Forbidden');
    });

    test('scoped admin should be able to create asset on customer within scope', async () => {
      await assetApiController.create(scopedAdmin, '4', newOfficeAssetParams);

      expect(assetServiceMock.create.mock.calls.length).toBe(1);
    });
  });

  describe('removeById', () => {
    test('global admin should be able to remove any asset', async () => {
      await assetApiController.removeById(globalAdmin, '34', '42');

      expect(assetServiceMock.remove.mock.calls.length).toBe(1);
    });

    test('scoped admin should not be able to remove asset on customer outside scope', () => {
      expect(() =>
        assetApiController.removeById(scopedAdmin, '34', '42'),
      ).rejects.toThrow('Forbidden');
    });

    test('scoped admin should be able to remove asset on customer within scope', async () => {
      await assetApiController.removeById(scopedAdmin, '34', '4');

      expect(assetServiceMock.remove.mock.calls.length).toBe(1);
    });
  });

  describe('getAll', () => {
    test('global admin should get all', async () => {
      await assetApiController.getAll(globalAdmin, '42');

      expect(assetServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('global user should get all customers', async () => {
      await assetApiController.getAll(globalUser, '42');

      expect(assetServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('scoped admin should get all if in scope', async () => {
      await assetApiController.getAll(scopedAdmin, '4');

      expect(assetServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('scoped admin should get an exception if not in scope', () => {
      expect(() =>
        assetApiController.getAll(scopedAdmin, '42'),
      ).rejects.toThrow('Forbidden');
    });
  });

  describe('getById', () => {
    test('global admin should get any asset', async () => {
      await assetApiController.getById(globalAdmin, '34', '42');

      expect(assetServiceMock.findById.mock.calls.length).toBe(1);
    });

    test('global user should get any asset', async () => {
      await assetApiController.getById(globalAdmin, '34', '42');

      expect(assetServiceMock.findById.mock.calls.length).toBe(1);
    });

    test('scoped admin should get if in scope', async () => {
      await assetApiController.getById(scopedAdmin, '34', '4');

      expect(assetServiceMock.findById.mock.calls.length).toBe(1);
    });

    test('scoped admin should get an exception if not in scope', () => {
      expect(() =>
        assetApiController.getById(scopedAdmin, '34', '42'),
      ).rejects.toThrow('Forbidden');
    });
  });
});
