import {
  Entity,
  PrimaryKey,
  Property,
  ManyToOne,
  Ref,
  Reference,
  Filter,
  Index,
} from '@mikro-orm/core';
import { User, UserId } from '../../users/domain/user.entity';
import { Asset, AssetId } from './asset.entity';

export type AssetHierarchyCreationParams = Omit<
  AssetHierarchy,
  | 'id'
  | 'child'
  | 'parent'
  | 'createdById'
  | 'createdBy'
  | 'createdAt'
  | 'updatedAt'
  | 'updatedBy'
  | 'deletedAt'
  | 'deletedBy'
  | 'deletedById'
>;

export const AssetHierarchyFactory = {
  create(params: AssetHierarchyCreationParams): AssetHierarchy {
    const { childId, parentId, isDefault } = params;
    return new AssetHierarchy(childId, parentId, isDefault);
  },
};

@Entity({ tableName: 'asset_hier' })
@Index({
  name: 'pa_chi_unique',
  expression:
    'create unique index "pa_chi_unique" on "asset_hier"("child", "parent") where "deleted_at" is null',
})
@Filter({
  name: 'isNotDeleted',
  cond: { deletedAt: { $eq: null } },
  default: true,
})
export class AssetHierarchy {
  constructor(childId: AssetId, parentId?: AssetId, isDefault?: boolean) {
    this.childId = childId;
    this.child = Reference.createFromPK(Asset, childId);
    this.parentId = parentId;
    if (parentId) {
      this.parent = Reference.createFromPK(Asset, parentId);
    }
    this.isDefault = isDefault;
  }

  @PrimaryKey()
  id!: number;

  @ManyToOne({ fieldName: 'child', onDelete: 'cascade' })
  child: Ref<Asset>;

  @Property({ hidden: true, fieldName: 'child', persist: false })
  childId: AssetId;

  @ManyToOne({ fieldName: 'parent', nullable: true, onDelete: 'set null' })
  parent?: Ref<Asset>;

  @Property({
    hidden: true,
    nullable: true,
    fieldName: 'parent',
    persist: false,
  })
  parentId?: AssetId;

  @Property({ nullable: true })
  isDefault?: boolean;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdById?: UserId;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;

  @Property({ hidden: true, length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: 'deleted_by',
  })
  deletedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: 'deleted_by' })
  deletedById?: UserId;
}
