import { MikroORM, RequestContext } from '@mikro-orm/core';
import { TestingModule } from '@nestjs/testing';
import { CustomerService } from 'src/customers/customer.service';
import { Customer } from 'src/customers/domain/customer.entity';

export async function deleteCustomer(
  testingModule: TestingModule,
  customer: string,
) {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(Customer, {
      name: customer,
    });
  });
}

export async function createCustomer(
  testingModule: TestingModule,
  name: string,
  address: string,
  createdById: number,
) {
  const orm = await testingModule.resolve(MikroORM);
  const customerService = await testingModule.get(CustomerService);

  return await RequestContext.createAsync(orm.em, async () => {
    return await customerService.create(
      {
        nameId: name.toLowerCase(),
        name,
        address,
      },
      createdById,
    );
  });
}

export const customerFixtureFactory = {
  createKawasaki: async (testingModule: TestingModule, createdById: number) => {
    const customer = await createCustomer(
      testingModule,
      'Kawasaki',
      'Somewhere in japan',
      createdById,
    );

    return {
      customerId: customer.id,
      cleanUp: async () => {
        await deleteCustomer(testingModule, customer.name);
      },
    };
  },
};
