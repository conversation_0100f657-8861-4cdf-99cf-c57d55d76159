import { Migration } from '@mikro-orm/migrations';

export class Migration20231212194735 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" drop constraint "meas_type_fk";',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_m_type_foreign" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );

    this.addSql('alter table "units_group_unit" drop constraint "uom_fk";');
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_unit_of_measure_foreign" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade;',
    );

    this.addSql('alter table "units_group_unit" drop constraint "uom_g_fk";');
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_units_group_foreign" foreign key ("units_group") references "units_group" ("id") on update cascade on delete cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_m_type_foreign";',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "meas_type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_unit_of_measure_foreign";',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "uom_fk" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade;',
    );

    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_units_group_foreign";',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "uom_g_fk" foreign key ("units_group") references "units_group" ("id") on update cascade;',
    );
  }
}
