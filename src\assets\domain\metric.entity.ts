import {
  <PERSON><PERSON>ty,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON>ey,
  Property,
  Ref,
  Unique,
} from '@mikro-orm/core';
import { InvalidInputException } from '../../errors/exceptions';
import { User } from 'src/users/domain/user.entity';
import { MeasurementType as AssetType } from '../../measurements/domain/measurement-type.entity';

export type MetricId = number;

@Entity()
@Unique({ properties: ['name', 'assetType'] })
export class Metric {
  constructor(creationParams: { name: string; assetType: AssetType }) {
    const { name, assetType } = creationParams;

    if (this.containsInvalidNameCharacters(name)) {
      throw new InvalidInputException('Name contains invalid characters');
    }

    this.name = name;
    this.assetType = assetType;
  }

  private containsInvalidNameCharacters(name: string) {
    return /[^a-zA-Z0-9_\-:\/\\]/.test(name);
  }

  @PrimaryKey()
  id!: MetricId;

  @Property({ length: 150 })
  name!: string;

  @ManyToOne({ hidden: true, eager: true, fieldName: 'asset_type' })
  assetType!: AssetType;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'created_by' })
  createdBy?: Ref<User>;

  @ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
  updatedBy?: Ref<User>;
}
