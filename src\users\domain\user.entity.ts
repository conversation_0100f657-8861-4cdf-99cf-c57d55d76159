import {
  <PERSON>,
  <PERSON><PERSON>ty,
  Enum,
  ManyToOne,
  OneToMany,
  PrimaryKey,
  Property,
  Ref,
  Reference,
  Unique,
} from "@mikro-orm/core";
import {
  CustomerUserRole,
  Role,
  RoleKey,
} from "../../authorization/domain/customer-user-role.entity";
import { Customer, CustomerId } from "../../customers/domain/customer.entity";

export type ScopedRole = {
  role: Role;
  customerIds: CustomerId[];
};

export type UserCreationParams = Omit<
  User,
  | "id"
  | "roleId"
  | "createdAt"
  | "createdBy"
  | "updatedAt"
  | "updatedBy"
  | "enabled"
  | "globalRoleName"
  | "assignGlobalRole"
  | "assignScopedRoles"
  | "hasRole"
  | "scopedRoles"
  | "scopedRole"
  | "hasGlobalRole"
  | "hasCustomerScope"
  | "hasCustomerScopeWithRole"
  | "hasCustomerScopeWithAnyAllowedRole"
  | "customerRoles"
  | "customerScope"
  | "toJSON"
> & { scopedRoles?: ScopedRole[] };

export type UserId = number;

export const UserFactory = {
  create(params: UserCreationParams): User {
    const {
      username,
      password,
      email,
      firstName,
      lastName,
      globalRole,
      scopedRoles,
      phone_no,
      country_code,
    } = params;

    const user = new User(
      username,
      password,
      email,
      firstName,
      lastName,
      phone_no,
      country_code
    );

    if (globalRole !== undefined) {
      user.assignGlobalRole(globalRole);
    } else if (scopedRoles !== undefined) {
      user.assignScopedRoles(scopedRoles);
    }

    return user;
  },
};

@Entity()
export class User {
  constructor(
    username: string,
    password: string,
    email: string,
    firstName: string,
    lastName: string,
    phone_no: string,
    country_code: string
  ) {
    this.username = username;
    this.password = password;
    this.email = email;
    this.firstName = firstName;
    this.lastName = lastName;
    this.phone_no = phone_no;
    this.country_code = country_code;
  }

  @PrimaryKey()
  id!: UserId;

  @Property({ length: 50 })
  @Unique()
  username!: string;

  @Property({ length: 100 })
  password!: string;

  @Property({ length: 300 })
  @Unique()
  email!: string;


  @Property({ length: 50 })
  firstName!: string;

  @Property({ length: 50 })
  lastName!: string;

  @Enum()
  globalRole?: Role;

  @Property()
  globalRoleName(): RoleKey | null {
    return this.globalRole !== undefined
      ? (Role[this.globalRole] as RoleKey)
      : null;
  }

  assignGlobalRole(globalRole: Role) {
    this.globalRole = globalRole;
    this.customerRoles.removeAll();
  }

  assignScopedRoles(scopedRoles: ScopedRole[]) {
    this.globalRole = undefined;
    this.customerRoles.removeAll();
    scopedRoles.map((scopedRole) => {
      scopedRole.customerIds.map((customerId) => {
        if (
          this.customerRoles
            .getItems()
            .find((customerRole) => customerRole.customer.id === customerId)
        ) {
          throw new Error("Customer can only belong to one scope");
        }

        const customerRole = new CustomerUserRole();
        customerRole.customer = Reference.createFromPK(Customer, customerId);
        customerRole.role = scopedRole.role;
        this.customerRoles.add(customerRole);
      });
    });
  }

  hasRole(role: Role) {
    return (
      this.globalRole === role ||
      this.customerRoles
        .getItems()
        .some((customerRole) => customerRole.role === role)
    );
  }

  @OneToMany(() => CustomerUserRole, (role) => role.user, {
    eager: true,
    orphanRemoval: true,
  })
  customerRoles = new Collection<CustomerUserRole>(this);

  @Property({ persist: false })
  hasGlobalRole() {
    return this.globalRole !== undefined && this.globalRole !== null;
  }

  @Property({ persist: false })
  hasCustomerScope(customerId: CustomerId) {
    return (
      this.hasGlobalRole() ||
      this.customerRoles
        .getItems()
        .find((customerRole) => customerRole.customer.id === customerId) !==
        undefined
    );
  }

  @Property({ persist: false })
  hasCustomerScopeWithRole(customerId: CustomerId, role: Role) {
    return (
      this.globalRole === role ||
      this.customerRoles
        .getItems()
        .find(
          (customerRole) =>
            customerRole.customer.id === customerId &&
            customerRole.role === role
        ) !== undefined
    );
  }

  @Property({ persist: false })
  hasCustomerScopeWithAnyAllowedRole(
    customerId: CustomerId,
    allowedRoles: Role[]
  ) {
    return (
      (this.globalRole !== undefined &&
        allowedRoles.includes(this.globalRole)) ||
      this.customerRoles
        .getItems()
        .find(
          (customerRole) =>
            customerRole.customer.id === customerId &&
            allowedRoles.includes(customerRole.role)
        ) !== undefined
    );
  }

  scopedRole(role: Role): CustomerId[] {
    return this.customerRoles
      .getItems()
      .filter((customerRole) => customerRole.role === role)
      .map((customerRole) => customerRole.customer.id);
  }

  @Property({
    persist: false,
  })
  get scopedRoles() {
    return this.customerRoles
      .getItems()
      .reduce((roles: Role[], customerRole) => {
        if (!roles.includes(customerRole.role)) {
          roles.push(customerRole.role);
        }
        return roles;
      }, [])
      .map((role) => ({ role: role, customerIds: this.scopedRole(role) }));
  }

  @Property({ persist: false })
  customerScope(): CustomerId[] {
    return this.customerRoles
      .getItems()
      .map((customerRole) => customerRole.customer.id);
  }

  @Property({ length: 6, hidden: true })
  createdAt = new Date();

  @ManyToOne({ hidden: true, nullable: true, fieldName: "created_by" })
  createdBy?: Ref<User>;

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true })
  enabled?: boolean = true;

  @Property()
  country_code?: string;

  @Property()
  phone_no?: string;
}
