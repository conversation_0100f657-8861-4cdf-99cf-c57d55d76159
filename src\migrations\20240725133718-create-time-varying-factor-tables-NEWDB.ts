import { Migration } from '@mikro-orm/migrations';
import { NewDbMigration } from '../../src/db/ConditionalMigration';

export class Migration20240725133718 extends NewDbMigration {

  async conditionalUp(): Promise<void> {
    this.addSql('create table "calc_data_types" ("id" serial primary key);');

    this.addSql('create table "factor_type" ("id" serial primary key, "name" varchar(50) not null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null, "created_by" int null, "updated_by" int null);');

    this.addSql('create table "time_varying_factor" ("id" serial primary key, "measurement" int null, "factor_type" int null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null, "created_by" int null, "updated_by" int null, "seasonal" boolean not null default false);');
    this.addSql('alter table "time_varying_factor" add constraint "tv_factors_meas_unique" unique ("measurement");');
    this.addSql('create index "time_var_factor_factor_type_idx" on "time_varying_factor" ("factor_type");');

    this.addSql('create table "factor_schedule" ("id" serial primary key, "factor" int null, "effective_date" date not null default now(), "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null, "created_by" int null, "updated_by" int null);');
    this.addSql('alter table "factor_schedule" add constraint "factor_schedule_unique" unique ("factor", "effective_date");');

    this.addSql('create table "factor_time_of_day_value" ("id" serial primary key, "factor_schedule" int null, "time_of_day" time(0) not null default \'00:00:00\', "weekday" int not null default 0, "value" double precision not null default 0.0, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null, "deleted_at" timestamptz(6) null, "created_by" int null, "updated_by" int null, "deleted_by" int null);');
    this.addSql('alter table "factor_time_of_day_value" add constraint "factor_time_of_day_value_unique" unique ("factor_schedule", "time_of_day", "weekday");');

    this.addSql('alter table "factor_type" add constraint "factor_type_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_type" add constraint "factor_type_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');

    this.addSql('alter table "time_varying_factor" add constraint "time_varying_factor_measurement_foreign" foreign key ("measurement") references "measurement" ("id") on update cascade on delete set null;');
    this.addSql('alter table "time_varying_factor" add constraint "time_varying_factor_factor_type_foreign" foreign key ("factor_type") references "factor_type" ("id") on update cascade on delete set null;');
    this.addSql('alter table "time_varying_factor" add constraint "time_varying_factor_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "time_varying_factor" add constraint "time_varying_factor_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');

    this.addSql('alter table "factor_schedule" add constraint "factor_schedule_factor_foreign" foreign key ("factor") references "time_varying_factor" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_schedule" add constraint "factor_schedule_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_schedule" add constraint "factor_schedule_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');

    this.addSql('alter table "factor_time_of_day_value" add constraint "factor_time_of_day_value_factor_schedule_foreign" foreign key ("factor_schedule") references "factor_schedule" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_time_of_day_value" add constraint "factor_time_of_day_value_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_time_of_day_value" add constraint "factor_time_of_day_value_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;');
    this.addSql('alter table "factor_time_of_day_value" add constraint "factor_time_of_day_value_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;');
  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "time_varying_factor" drop constraint "time_varying_factor_factor_type_foreign";');

    this.addSql('alter table "factor_schedule" drop constraint "factor_schedule_factor_foreign";');

    this.addSql('alter table "factor_time_of_day_value" drop constraint "factor_time_of_day_value_factor_schedule_foreign";');

    this.addSql('drop table if exists "calc_data_types" cascade;');

    this.addSql('drop table if exists "factor_type" cascade;');

    this.addSql('drop table if exists "time_varying_factor" cascade;');

    this.addSql('drop table if exists "factor_schedule" cascade;');

    this.addSql('drop table if exists "factor_time_of_day_value" cascade;');
  }

}
