import { Migration } from "@mikro-orm/migrations";

export class Migration20250113115258_add_composite_unique_constraint_dashboard extends Migration {
  async up(): Promise<void> {
    // Drop the old unique constraint on the title field
    this.addSql(
      'alter table "dashboard" drop constraint if exists "dashboard_unique";'
    );

    // Remove the foreign key for customer_id before adding the new constraint
    this.addSql(
      'alter table "dashboard" drop constraint if exists "dashboard_customer_id_foreign";'
    );

    // Add the composite unique constraint for title and customer_id
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_title_customer_id_unique" unique ("title", "customer_id");'
    );

    // Re-add the foreign key for customer_id
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade on delete set null;'
    );
  }

  async down(): Promise<void> {
    // Drop the composite unique constraint
    this.addSql(
      'alter table "dashboard" drop constraint if exists "dashboard_title_customer_id_unique";'
    );

    // Remove the foreign key for customer_id
    this.addSql(
      'alter table "dashboard" drop constraint if exists "dashboard_customer_id_foreign";'
    );

    // Re-add the old unique constraint on title
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_unique" unique ("title");'
    );

    // Re-add the foreign key for customer_id
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade on delete set null;'
    );
  }
}
