import { Migration } from '@mikro-orm/migrations';

export class Migration20240306125616 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "dashboard" add column "deleted_at" timestamptz(6) null, add column "deleted_by" int null;');
    this.addSql('alter table "dashboard" add constraint "dashboard_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "dashboard" drop constraint "dashboard_deleted_by_foreign";');

    this.addSql('alter table "dashboard" drop column "deleted_at";');
    this.addSql('alter table "dashboard" drop column "deleted_by";');
  }

}
