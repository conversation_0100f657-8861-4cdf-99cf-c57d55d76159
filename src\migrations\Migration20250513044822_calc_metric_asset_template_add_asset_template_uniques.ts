import { Migration } from '@mikro-orm/migrations';

export class Migration20250513044822_calc_metric_asset_template_add_asset_template_uniques extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" drop constraint "calculation_metric_instance_output_metric_unique";');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_assetTemplate_output_metric_unique" unique ("assetTemplate", "output_metric");');
  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" drop constraint "calculation_metric_instance_assetTemplate_output_metric_unique";');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_output_metric_unique" unique ("output_metric");');
  }

}
