import { FilterQuery, Reference } from '@mikro-orm/core';
import { getRepositoryToken } from '@mikro-orm/nestjs';
import { Test } from '@nestjs/testing';
import { CustomerService } from './customer.service';
import { Customer } from './domain/customer.entity';

describe('CustomerService', () => {
  describe('create', () => {
    beforeEach(() => {
      Reference.createFromPK = jest.fn();
    });

    test('customer with unique name id should be created successfully', async () => {
      const { customerService, repositoryMock } = await createCustomerService();

      await customerService.create(
        {
          nameId: 'apple',
          name: 'Apple',
          address: 'California',
        },
        1,
      );

      const createdCustomer = repositoryMock.persistAndFlush.mock
        .calls[0][0] as Customer;
      expect(createdCustomer.name).toBe('Apple');
      expect(createdCustomer.enabled).toBeTruthy();
      expect(createdCustomer.address).toBe('California');
    });

    test('customer with existing name id should throw an exception', async () => {
      const { customerService, repositoryMock } = await createCustomerService();
      repositoryMock.count = () => 1;

      await expect(
        customerService.create(
          {
            nameId: 'not_new',
            name: 'NotNew',
            address: 'California',
          },
          1,
        ),
      ).rejects.toThrow('Customer with name id "not_new" already exists');
    });

    test('customer with invalid name id should throw an exception', async () => {
      const { customerService } = await createCustomerService();

      await expect(
        customerService.create(
          {
            nameId: 'NotNew',
            name: 'NotNew',
            address: 'California',
          },
          1,
        ),
      ).rejects.toThrow('Invalid customer name id');
    });
  });

  describe('get by name id', () => {
    beforeEach(() => {
      Reference.createFromPK = jest.fn();
    });

    test('given a name id, repository findOne should be called with the correct filter', async () => {
      const { customerService, repositoryMock } = await createCustomerService();

      await customerService.findByNameId('apple');

      const customerQuery = repositoryMock.findOne.mock
        .calls[0][0] as FilterQuery<Customer>;
      expect(customerQuery).toStrictEqual({ nameId: 'apple' });
    });
  });
});

const createCustomerService = async () => {
  const repositoryMock = {
    count: () => 0,
    create: (id) => id,
    persistAndFlush: jest.fn(),
    findOne: jest.fn(),
  };
  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(Customer),
        useValue: repositoryMock,
      },
      CustomerService,
    ],
  }).compile();
  const customerService = moduleRef.get(CustomerService);
  return { customerService, repositoryMock };
};
