import {
  ApiProperty,
} from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class GetWeatherDto {
  @ApiProperty({ type: 'number', required: true })
  @IsNumber()
  @IsNotEmpty()
  station_id: number;
}

export class StationUnits {
  @ApiProperty({ type: 'string', example: "degrees" })
  units_direction: string;

  @ApiProperty({ type: 'string', example: "mi" })
  units_distance: string;

  @ApiProperty({ type: 'string', example: "imperial" })
  units_other: string;

  @ApiProperty({ type: 'string', example: "in" })
  units_precip: string;

  @ApiProperty({ type: 'string', example: "mb" })
  units_pressure: string;

  @ApiProperty({ type: 'string', example: "f" })
  units_temp: string;

  @ApiProperty({ type: 'string', example: "mph" })
  units_wind: string;
}

export class GetWeatherResDto {
  @ApiProperty({ type: 'number', example: 5.4 })
  air_temperature: number;

  @ApiProperty({ type: 'number', example: 5.4 })
  feels_like: number;

  @ApiProperty({ type: 'number', example: 90 })
  relative_humidity: number;

  @ApiProperty({ type: 'number', example: 1023.5 })
  sea_level_pressure: number;

  @ApiProperty({ type: 'number', example: 103 })
  wind_direction: number;

  @ApiProperty({ type: 'number', example: 0 })
  wind_direction_cardinal: number;

  @ApiProperty({ type: 'number', example: 0 })
  conditions: number;

  @ApiProperty({ type: 'number', example: 0.2 })
  wind_avg: number;

  @ApiProperty({ type: 'number', example: 0.06 })
  uv: number;

  @ApiProperty({ type: StationUnits })
  station_units: StationUnits;
}

export class GetWeatherNotFoundResDto {
  @ApiProperty({ required: true, example: 404 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Station not found', examples: ['Station not found', 'Station forecast not found'] })
  message: string;

  @ApiProperty({ required: true, example: 'Not Found' })
  error: string;
}