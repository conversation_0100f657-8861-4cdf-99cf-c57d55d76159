import { Migration } from '@mikro-orm/migrations';

export class Migration20230821203924 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset" add column "deleted_at" timestamptz(6) null, add column "deleted_by" int null;',
    );
    this.addSql(
      'alter table "asset" add constraint "asset_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;',
    );

    this.addSql(
      'alter table "asset_hier" add column "deleted_at" timestamptz(6) null, add column "deleted_by" int null;',
    );
    this.addSql(
      'alter table "asset_hier" add constraint "asset_hier_deleted_by_foreign" foreign key ("deleted_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset" drop constraint "asset_deleted_by_foreign";',
    );

    this.addSql(
      'alter table "asset_hier" drop constraint "asset_hier_deleted_by_foreign";',
    );

    this.addSql('alter table "asset" drop column "deleted_at";');
    this.addSql('alter table "asset" drop column "deleted_by";');

    this.addSql('alter table "asset_hier" drop column "deleted_at";');
    this.addSql('alter table "asset_hier" drop column "deleted_by";');
  }
}
