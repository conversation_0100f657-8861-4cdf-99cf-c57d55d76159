apiVersion: v1
kind: Secret
metadata:
  name: admin-api-dev-secrets
  namespace: application
type: Opaque
data:
  # Database Credentials
  DB_PASSWORD: "QnIwbXB0MG4hMFQ="
  
  # MQTT Credentials
  MQTT_PASSWORD: ""
  
  # Auth Credentials
  AUTH_JWT_SECRET: "dGhpcyBpcyBhIHZlcnkgc2VjcmV0IHNlY3JldA=="
  
  # AWS Credentials
  ACCESS_KEY_ID: "****************************"
  SECRET_ACCESS_KEY: "Y1A2eFN1amhkUGVYck52MEx1WFU4TUhvUkRIdTRXWlBqemk4Vm9VdQ=="
  
  # Twilio Credentials
  TWILLIO_ACCOUNT_SID: "************************************************"
  TWILLIO_AUTH_TOKEN: "NDY5MTBmODg4ZWE0NjFiY2RlMzg4YTY4MmQwNjk1OTI="
  TWILLIO_EMAIL_API_TOKEN: "********************************************************************************************"
  
  
  # Auth Credentials
  AUTH_JWT_SECRET: "dGhpcyBpcyBhIHZlcnkgc2VjcmV0IHNlY3JldA=="
  AUTH_SESSION_DURATION_MIN: "NDMyMDA="
 
 # Rabbitmq Password
  RABBIT_MQ_PASSWORD: "QnIwbXB0MG4hMFQ="
