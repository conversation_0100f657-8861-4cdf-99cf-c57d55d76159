import {Modu<PERSON>} from '@nestjs/common';
import {KafkaService} from './kafka.service';
import {KafkaController} from './kafka.controller';
import {AlertModule} from "../alert/alert.module";

@Module({
    imports: [AlertModule], // No imports are needed
    providers: [KafkaService], // Kafka service is directly provided here
    controllers: [KafkaController], // Controller for handling API requests
})
export class KafkaModule {}