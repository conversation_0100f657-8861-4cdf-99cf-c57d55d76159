import { Injectable } from "@nestjs/common";
import {
  ASSET_BATCH_REMOVE_EVENT_KEY,
  AssetBatchRemoveEvent,
} from "src/assets/domain/events/asset-batch-removed.event";
import { AssetMeasurementService } from "./asset-measurement.service";
import { OnEvent } from "@nestjs/event-emitter";
import {
  ASSET_UPDATE_EVENT_KEY,
  AssetUpdateEvent,
} from "src/assets/domain/events/asset-update.event";

@Injectable()
export class AssetMeasurementSubscriber {
  constructor(
    private readonly assetMeasurementService: AssetMeasurementService
  ) {}

  @OnEvent(ASSET_BATCH_REMOVE_EVENT_KEY)
  async handleAssetBatchRemove(payload: AssetBatchRemoveEvent) {
    const { assetIds, occuredAt, runById } = payload;

    await this.assetMeasurementService.batchRemoveByAssetId(
      assetIds,
      occuredAt,
      runById
    );

    return true;
  }

  @OnEvent(ASSET_UPDATE_EVENT_KEY)
  async handleAssetUpdate(payload: AssetUpdateEvent) {
    const {
      id: assetId,
      cust_id: customerId,
      headers,
      runById,
      emManager,
    } = payload;

    // update asset
    await this.assetMeasurementService.handleAssetUpdate(
      assetId,
      customerId,
      headers,
      runById,
      emManager
    );

    return true;
  }
}
