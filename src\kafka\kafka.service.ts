import {
  Injectable,
  Logger,
  OnModuleD<PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Consumer, Kafka, Producer } from "kafkajs";
import { Alerts } from "src/alert/domain/alert.entity";
import { NotificationDTO } from "src/notification/notification.dto";
import { AlertService } from "../alert/alert.service";

@Injectable()
export class KafkaService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaService.name);
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;
  private kafkaConfig: {
    brokers: string;
    topicAlert: string;
    clientId: string;
    topicNotification: string;
    kafkaGroupId: string;
    kafkaGroupIdInstance: string;
    frontendUrl: string;
  };
  constructor(
    private alertService: AlertService,
    private configService: ConfigService
  ) {
    const {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
    } = configService.get("kafka");
    this.kafkaConfig = {
      brokers,
      topicAlert,
      clientId,
      topicNotification,
      kafkaGroupId,
      kafkaGroupIdInstance,
      frontendUrl,
    };
    this.alertService = alertService;
    this.kafka = new Kafka({
      clientId: this.kafkaConfig.clientId, // Client ID
      brokers: [this.kafkaConfig.brokers], // Kafka brokers
      connectionTimeout: 30000, // Connection timeout
      requestTimeout: 30000, // Request timeout
    });
    this.producer = this.kafka.producer();
    this.consumer = this.kafka.consumer({
      groupId: this.kafkaConfig.kafkaGroupId, // Consumer group ID
      // @ts-ignore
      groupInstanceId: this.kafkaConfig.kafkaGroupIdInstance, // Static group instance ID
    });
  }

  // Initialize Kafka producer and consumer on module startup
  async onModuleInit() {
    await this.connectProducer();
    await this.connectConsumer();
  }

  async onModuleDestroy() {
    await this.disconnectProducer();
    await this.disconnectConsumer();
  }

  async connectProducer() {
    this.logger.log("Connecting Kafka producer...");
    await this.producer.connect();
    this.logger.log("Kafka producer connected.");
  }

  async disconnectProducer() {
    this.logger.log("Disconnecting Kafka producer...");
    await this.producer.disconnect();
    this.logger.log("Kafka producer disconnected.");
  }

  private async htmlEmailTemplate(
    alert: Alerts,
    inputValue: string,
    state: string,
    timestamp: number,
    alertCondition: string,
    event_id: string,
    threshold_type?: string
  ) {
    const now = new Date();
    const start = new Date(now.getTime() - 4 * 60 * 60 * 1000).getTime();
    const formattedTimestamp = new Date(timestamp).toUTCString();
    const link = event_id
      ? `<a href="${this.kafkaConfig.frontendUrl}/customer/${alert.customerId}/dashboard/0?event_id=${event_id}">Visit Dashboard</a>`
      : "";
    return `
    <html>
      <body>
        <h3>
          ${
            threshold_type !== undefined && threshold_type === "STALE"
              ? state === "STALE"
                ? "Stale Alert Raised"
                : "Stale Alert Resolved"
              : threshold_type !== undefined && threshold_type === "DEAD"
              ? state === "DEAD"
                ? "Dead Measurement Alert Raised"
                : "Dead Measurement Resolved"
              : state === "EXCEEDED"
              ? "Limit Alert"
              : "Returned to Normal"
          }
        </h3>
        <p><strong>At:</strong> ${formattedTimestamp}</p>
        <p><strong>Asset:</strong> ${alert.asset.tag}</p>
        <p><strong>Measurement:</strong> ${alert.measurement.tag}</p>
        <p><strong>Current Value:</strong> ${inputValue}</p>
        <h2>Settings:</h2>
        <p><strong>Threshold:</strong> ${
          threshold_type === "DEAD"
            ? state === "DEAD"
              ? `No data received for ${alert.thresholdValue} minutes`
              : `New Data received for ${alert.thresholdValue} minutes`
            : threshold_type === "STALE"
              ? state === "STALE"
                ? `No fresh data received for ${alert.thresholdValue} minutes`
                : `Fresh data received after ${alert.thresholdValue} minutes`
              : `${alertCondition} ${alert.thresholdValue}`
        }</p>
        ${
          threshold_type !== "DEAD" && threshold_type !== "STALE"
            ? `<p><strong>Return to Normal:</strong> ${alertCondition} ${alert.resetDeadband}</p>` 
            : ''
        }
        ${link}
      </body>
    </html>
  `;
  }

  private async emailTemplate(
    alert: Alerts,
    inputValue: string,
    state: string,
    timestamp: number,
    alertCondition: string,
    event_id: string
  ) {
    const now = new Date();
    const start = new Date(now.getTime() - 4 * 60 * 60 * 1000).getTime();
    return `${state === "EXCEEDED" ? "Limit Alert" : "Returned to Normal"}
    At: ${new Date(new Date(timestamp).getTime()).toUTCString()}
    Asset: ${alert.asset.tag}
    Measurement: ${alert.measurement.tag}
    CurrentValue: ${inputValue}
    Settings:
    Threshold: ${alertCondition} ${alert.thresholdValue}
    Return to Normal: ${alertCondition} ${alert.resetDeadband}
    ${
      event_id !== undefined
        ? `Link : ${this.kafkaConfig.frontendUrl}/customer/${alert.customerId}/dashboard/0?event_id=${event_id}`
        : ""
    }`;
  }

  private async smsTemplate(
    alert: Alerts,
    inputValue: string,
    state: string,
    timestamp: number,
    alertCondition: string,
    event_id: string,
    threshold_type?: string
  ) {
    const now = new Date();
    const start = new Date(now.getTime() - 4 * 60 * 60 * 1000).getTime();
    return `${
      threshold_type !== undefined && threshold_type === "STALE"
        ? state === "STALE"
          ? "Stale Alert Raised"
          : "Stale Alert Resolved"
        : threshold_type !== undefined && threshold_type === "DEAD"
        ? state === "DEAD"
          ? "Dead Measurement Alert Raised"
          : "Dead Measurement Resolved"
        : state === "EXCEEDED"
        ? "Limit Alert"
        : "Returned to Normal"
    }
      At: ${new Date(new Date(timestamp).getTime()).toUTCString()}
      Asset: ${alert.asset.tag}
      Measurement: ${alert.measurement.tag}
      CurrentValue: ${inputValue}
      Settings:
      Threshold: ${threshold_type === "DEAD"
        ? state === "DEAD"
          ? `No data received for ${alert.thresholdValue} minutes`
          : `New Data received for ${alert.thresholdValue} minutes`
        : threshold_type === "STALE"
          ? state === "STALE"
            ? `No fresh data received for ${alert.thresholdValue} minutes`
            : `Fresh data received after ${alert.thresholdValue} minutes`
          : `${alertCondition} ${alert.thresholdValue}`}
      ${threshold_type !== "DEAD" && threshold_type !== "STALE"
        ? `Return to Normal: ${alertCondition} ${alert.resetDeadband}` 
        : ''}
      ${
        event_id !== undefined
          ? `Link : ${this.kafkaConfig.frontendUrl}/customer/${alert.customerId}/dashboard/0?event_id=${event_id}`
          : ""
      }`;
  }
  async connectConsumer() {
    this.logger.log("Connecting Kafka consumer...");
    await this.consumer.connect();
    this.logger.log("Kafka consumer connected.");

    // Subscribe to the topic and process messages
    await this.consumer.subscribe({
      topic: this.kafkaConfig.topicAlert,
      fromBeginning: true,
    });

    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        const key = message.key?.toString() || "null";
        const value = message.value?.toString() || "null";
        this.logger.log(
          `Received message from topic ${topic} (partition ${partition}): key=${key}, value=${value}`
        );
        const parsedValue = JSON.parse(message.value.toString()) as {
          alert_id: number;
          input_value: number;
          state: string;
          timestamp: number;
          comparator: string;
          limit: number;
          measurement_id: number;
          event_id: number;
          deadband: number;
          aggregate: string;
          period: string;
          asset_id: number;
        };
        const alertId = parsedValue.alert_id;
        const state = parsedValue.state;
        const timestamp = parsedValue.timestamp;
        const inputValue = parsedValue.input_value;
        const comparator = parsedValue.comparator;
        const eventId = parsedValue.event_id;
        const alertDto = await this.alertService.getAlertNew(alertId, true);
        this.logger.log(alertDto.thresholdType);
        for (const user of alertDto.alertUsers) {
          let notify: NotificationDTO;
          try {
            user.user = await this.alertService.getAlertUsers(user.user);
            if (user.notificationtype === 2 || user.notificationtype === 3) {
              notify = new NotificationDTO(
                user.user.id,
                await this.smsTemplate(
                  alertDto,
                  inputValue.toString(),
                  state,
                  timestamp,
                  comparator,
                  eventId.toString(),
                  alertDto.thresholdType?.threshold ?? undefined
                ),
                new Date(),
                false,
                0,
                "sms",
                user.user.country_code + user.user.phone_no,
                (alertDto.thresholdType?.threshold !== undefined &&
                  alertDto.thresholdType?.threshold === "STALE") ??
                undefined
                  ? "STALE - Alert Notification"
                  : "Alert Notification"
              );
              if (
                user.user.phone_no &&
                user.user.phone_no !== null &&
                user.user.phone_no !== "" &&
                user.user.phone_no !== undefined &&
                user.user.country_code &&
                user.user.country_code !== null &&
                user.user.country_code !== "" &&
                user.user.country_code !== undefined
              ) {
                this.sendMessage(
                  this.kafkaConfig.topicNotification,
                  "key",
                  JSON.stringify(notify)
                );
              }
            }
            if (user.notificationtype === 1 || user.notificationtype === 3) {
              notify = new NotificationDTO(
                user.user.id,
                await this.htmlEmailTemplate(
                  alertDto,
                  inputValue.toString(),
                  state,
                  timestamp,
                  comparator,
                  eventId.toString(),
                  alertDto.thresholdType?.threshold ?? undefined
                ),
                new Date(),
                false,
                0,
                "email",
                user.user.email,
                (alertDto.thresholdType?.threshold !== undefined &&
                  alertDto.thresholdType?.threshold === "STALE") ??
                undefined
                  ? "STALE - Alert Notification"
                  : "Alert Notification"
              );
              if (
                user.user.email &&
                user.user.email !== null &&
                user.user.email !== "" &&
                user.user.email !== undefined
              ) {
                this.sendMessage(
                  this.kafkaConfig.topicNotification,
                  "key",
                  JSON.stringify(notify)
                );
              }
            }
          } catch (err) {
            console.log(err);
            this.sendMessage("notification-dlq", "key", JSON.stringify(notify));
          }
        }
      },
    });
  }

  async disconnectConsumer() {
    this.logger.log("Disconnecting Kafka consumer...");
    await this.consumer.disconnect();
    this.logger.log("Kafka consumer disconnected.");
  }

  // Method to send messages to Kafka
  async sendMessage(topic: string, key: string, value: string) {
    try {
      if (!key || !value) {
        this.logger.warn(
          `Skipping message send due to missing key/value for topic ${topic}: key=${key}, value=${value}`
        );
        return; // Prevent sending invalid messages
      }

      await this.producer.send({
        topic,
        messages: [{ key, value }],
      });

      this.logger.log(
        `Sent message to topic ${topic}: key=${key}, value=${value}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to send message to topic ${topic}: ${(error as Error).message}`,
        (error as Error).stack
      );
    }
  }
}
