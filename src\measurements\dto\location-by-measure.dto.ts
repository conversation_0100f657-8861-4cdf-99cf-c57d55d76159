import { ApiProperty } from "@nestjs/swagger";

export class LocationMeasurementResDto {
  @ApiProperty({ required: true, example: 33.529255 })
  latitude: number;

  @ApiProperty({ required: true, example: -117.706345 })
  longitude: number;
}

export class LocationMeasurementNotFoundResDto {
  @ApiProperty({ required: true, example: 404 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Location not found', examples: ['Measurement not found', 'Location not found'] })
  message: string;

  @ApiProperty({ required: true, example: 'Not Found' })
  error: string;
}