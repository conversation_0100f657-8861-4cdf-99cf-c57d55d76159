import { EntityManager } from "@mikro-orm/core";
import { Injectable } from "@nestjs/common";

@Injectable()
export class HealthService {
  constructor(private readonly em: EntityManager) {}

  async livenessCheck() {
    // Liveness check, just to ensure the app is up
    return { "status": "alive" };
  }

  async readinessCheck() {
    // Readiness check, just to ensure the app is up
    return { "status": "ready" };
  }

  async check() {
    const check = await this.em.getConnection().isConnected();
    const res = await this.em.getConnection().execute("SELECT 1 as result");
    return {
      check: check,
      res,
    };
  }
}
 
