import { MikroOrmModule } from "@mikro-orm/nestjs";
import { forwardRef, Lo<PERSON>, Module } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { DashboardService } from "src/dashboards/dashboard.service";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { AnnotationsController } from "./annotations.controller";
import { AnnotationsService } from "./annotations.service";
import { Annotation } from "./domain/annotation.entity";
import { AnnotationRepository } from "./repository/annotation.repository";
import { DashboardsModule } from "src/dashboards/dashboards.module";

@Module({
  imports: [
    MikroOrmModule.forFeature([Annotation, Measurement]),
    forwardRef(() => AuthModule),
    DashboardsModule,
  ],
  controllers: [AnnotationsController],
  providers: [
    AnnotationsService,
    AnnotationRepository,
    DashboardService,
    {
      provide: Logger,
      useValue: new Logger(AnnotationsService.name),
    },
  ],
})
export class AnnotationsModule {}
