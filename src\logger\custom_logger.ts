import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

export const WinstonLog<PERSON> = WinstonModule.createLogger({
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(), // Adds timestamp
        winston.format((info) => {
          if (!info.trace_id) {
            info.trace_id = '00000000000000000000000000000000';
          }
          if (!info.span_id) {
            info.span_id = '0000000000000000';
          }
          if (!info.customer_id) {
            info.customer_id = '00';
          }
          if (!info.measurement_id) {
            info.measurement_id = null;
          }
          if (!info.asset_id) {
            info.asset_id = null;
          }
          return info;
        })(),
        winston.format.printf((info) => {
          const logData = {
            timestamp: info.timestamp,
            level: info.level,
            trace_id: info.trace_id,
            span_id: info.span_id,
            customer_id: info.customer_id,
            measurement_id: info.measurement_id,
            asset_id: info.asset_id,
            message: info.message,
          };
          return JSON.stringify(logData);
        })
      ),
    }),
  ],
});