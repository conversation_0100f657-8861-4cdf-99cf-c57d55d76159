import { ApiProperty } from "@nestjs/swagger";
import { IsNumber } from "class-validator";
import { User } from "src/users/domain/user.entity";
import { FactorSchedule } from "../domain/factor-schedule.entity";
import { TimeVaryingFactor } from "../domain/TimeVaryingFactor.entity";
import { FactorTimeOfDayValue } from "../domain/factor-time-of-day-value.entity";
export class FactorTimeOfDayValueDTO
  implements
    Pick<
      FactorTimeOfDayValue,
      | "id"
      | "factorSchedule"
      | "weekday"
      | "timeOfDay"
      | "value"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
      | "deletedAt"
      | "deletedBy"
    >
{
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty({
    required: true,
  })
  @IsNumber()
  factor: TimeVaryingFactor;

  @ApiProperty()
  @IsNumber()
  factorSchedule: FactorSchedule;

  @ApiProperty()
  @IsNumber()
  weekday: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  createdBy: User;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  updatedBy: User;

  @ApiProperty()
  deletedAt: Date;

  @ApiProperty()
  deletedBy?: User;

  @ApiProperty()
  @IsNumber()
  value: string;

  @ApiProperty()
  timeOfDay: string;

  @ApiProperty({ required: true })
  effectiveDate: string;
}

export const FactorTimeOfDayValueDTOs = [new FactorTimeOfDayValueDTO()];
