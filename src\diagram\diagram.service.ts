import { NotFoundError } from "@mikro-orm/core";
import { Injectable } from "@nestjs/common";
import { User } from "src/users/domain/user.entity";
import { DiagramRepository } from "./diagram.repository";
import { Diagram } from "./domain/diagram.entity";

@Injectable()
export class DiagramService {
  constructor(private readonly diagramTemplate: DiagramRepository) {}

  async findAll() {
    const templates = await this.diagramTemplate.findAll();
    return {
      total: templates.length,
      items: templates,
    };
  }

  async findById(id: number) {
    return await this.diagramTemplate.findById(id);
  }

  async create({ data, name }: { name: string; data: string }, user: User) {
    const diagram = new Diagram();
    diagram.name = name;
    diagram.data = data;
    diagram.created_by = user;
    return await this.diagramTemplate.add(diagram);
  }

  async update(
    { id, data, name }: { id: number; name: string; data: string },
    user: User
  ) {
    const diagram = await this.diagramTemplate.findById(id);
    if (!diagram) {
      throw new NotFoundError("Diagram does not exists");
    }
    diagram.name = name;
    diagram.data = data;
    diagram.updated_by = user;
    return await this.diagramTemplate.update(diagram);
  }
}
