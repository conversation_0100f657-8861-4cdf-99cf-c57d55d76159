import { InjectRepository } from "@mikro-orm/nestjs";
import { Asset, AssetId } from "../domain/asset.entity";
import { EntityRepository, FilterQuery, Reference } from "@mikro-orm/core";
import { EntityManager } from "@mikro-orm/postgresql";
import { User, UserId } from "src/users/domain/user.entity";
import { CustomerId } from "src/customers/domain/customer.entity";
import {
  HierarchyEdge,
  HierarchyGraphService,
} from "../hierarchy-graph.service";
import { AssetHierarchy } from "../domain/asset-hierarchy.entity";
import { Injectable } from "@nestjs/common";

@Injectable()
export class AssetRepository {
  constructor(
    @InjectRepository(Asset)
    private readonly entityRepository: EntityRepository<Asset>,
    private readonly em: EntityManager,
    private readonly hierarchyGraphService: HierarchyGraphService
  ) {}

  async add(asset: Asset, createdById?: UserId): Promise<Asset> {
    if (createdById) {
      asset.createdById = createdById;
    }
    asset.createdAt = new Date();

    await this.entityRepository.persistAndFlush(asset);

    return asset;
  }

  async findById(
    id: AssetId,
    filter: { customerId?: CustomerId } = {}
  ): Promise<Asset | null> {
    const condition = filter.customerId
      ? { id, customer: filter.customerId }
      : { id };
    return await this.entityRepository.findOne(
      {
        ...condition,
        deletedAt: null,
      },
      {
        populate: ["assetTemplate"],
      }
    );
  }

  async getAllByCustomerId(
    customerId: CustomerId,
    filter: {
      ids?: AssetId[];
      parentIds?: AssetId[];
    } = {}
  ): Promise<Asset[]> {
    const { ids, parentIds } = filter;

    const conditions: FilterQuery<Asset> = [
      {
        customer: customerId,
        deletedAt: null,
        parentHierarchies: { $ne: null },
      },
    ];

    if (ids) {
      conditions.push({ id: { $in: ids } });
    }

    if (parentIds) {
      if (parentIds.includes(-1)) {
        conditions.push({
          $or: [
            { parentHierarchies: { parent: { $eq: null }, deletedAt: null } },
            {
              parentHierarchies: {
                parent: { $in: parentIds },
                deletedAt: null,
              },
            },
          ],
        });
      } else {
        conditions.push({
          parentHierarchies: { parent: { $in: parentIds }, deletedAt: null },
        });
      }
    }

    return await this.entityRepository.find(
      {
        $and: conditions,
      },
      { orderBy: { tag: "ASC" } }
    );
  }

  async getAllByAssetTemplateId(assetTemplateId: number): Promise<Asset[]> {
    return await this.entityRepository.find({
      assetTemplate: assetTemplateId,
      deletedAt: null,
    });
  }
  // async getAllByCustomerId(
  //   customerId: number,
  //   filter: {
  //     ids?: number[];
  //     parentIds?: number[];
  //   } = {}
  // ): Promise<any[]> {
  //   const { ids, parentIds } = filter;
  //   // Build the conditions array
  //   const conditions: any[] = [
  //     { customer: customerId },
  //     { deletedAt: null },
  //     // { parentHierarchies: { $ne: null } },
  //   ];
  //   if (ids && ids.length > 0) {
  //     conditions.push({ id: { $in: ids } });
  //   }
  //   if (parentIds && parentIds.length > 0) {
  //     if (parentIds.includes(-1)) {
  //       conditions.push({
  //         $or: [
  //           { parentHierarchies: { parent: { $eq: null }, deletedAt: null } },
  //           {
  //             parentHierarchies: {
  //               parent: { $in: parentIds },
  //               deletedAt: null,
  //             },
  //           },
  //         ],
  //       });
  //     } else {
  //       conditions.push({
  //         parentHierarchies: { parent: { $in: parentIds }, deletedAt: null },
  //       });
  //     }
  //   }

  //   const whereClause = { $and: [] };

  //   // Fetch assets using the EM. The eager relations (parentHierarchies and childHierarchies)
  //   // will automatically load due to your entity configuration.
  //   const assets = await this.em.find(
  //     Asset,
  //     {},
  //     {
  //       orderBy: { tag: "ASC" },
  //     }
  //   );
  //   console.log(whereClause, ids, parentIds);
  //   // Transform the fetched assets into the desired output format
  //   const result = assets.map((asset) => ({
  //     id: asset.id,
  //     tag: asset.tag,
  //     latitude: asset.latitude,
  //     longitude: asset.longitude,
  //     description: asset.description,
  //     time_zone: asset.timeZone,
  //     type_id: asset.assetTypeId,
  //     customer_id: asset.customerId,
  //     parent_ids: asset.parentIds,
  //     children_ids: asset.childrenIds,
  //   }));

  //   return result;
  // }

  async remove(asset: Asset, deletedById?: UserId) {
    if (deletedById) {
      asset.deletedById = deletedById;
    }
    asset.deletedAt = new Date();

    this.entityRepository.persistAndFlush(asset);
  }

  /**
   * Soft deletes a given asset and its entire linked hierarchy from the system.
   *
   * @param rootAsset The root asset to be removed.
   * @param runById The ID of the user who initiated the removal.
   * @returns An array of IDs of the removed assets, including the root asset.
   * @throws If any unexpected error occurs during the removal process.
   */
  async removeHierarchy(rootAsset: Asset, runById: UserId) {
    // Build query to fetch the root asset hierarchy
    const qb = this.getAssetHierarchyQueryBuilder(rootAsset.id);

    // Execute the query to obtain the hierarchy as parent-child graph edges
    const hierarchyDbRows = (await this.em.execute(qb)) as HierarchyEdge[];
    // Map the database rows to parent-children and child-parents relationships
    const { parentChildrenMap, childParentsMap } =
      this.hierarchyGraphService.mapDbRowsToHierarchy(hierarchyDbRows);

    // Find the IDs of the assets to be removed (all which are not descendants of another ancestor too)
    const { linkIds, nodeIds } =
      this.hierarchyGraphService.findCascadeRemoveIds(
        rootAsset.id,
        parentChildrenMap,
        childParentsMap
      );

    const deletedAt = new Date();

    // Update the AssetHierarchy records to mark them as deleted
    await this.em
      .createQueryBuilder(AssetHierarchy)
      .update({ deletedAt, deletedById: runById })
      .where({ id: { $in: linkIds } })
      .execute();

    // Update the Asset records to mark them as deleted
    await this.em
      .createQueryBuilder(Asset)
      .update({ deletedAt, deletedById: runById })
      .where({ id: { $in: nodeIds } })
      .execute();

    return nodeIds;
  }

  async update(asset: Asset, runById: UserId) {
    asset.updatedBy = Reference.createFromPK(User, runById);
    asset.updatedAt = new Date();

    await this.entityRepository.persistAndFlush(asset);
  }

  /**
   * Builds and returns a Knex query builder for retrieving the hierarchy of assets
   * starting from the specified parent asset ID.
   * @param parentAssetId - The ID of the parent asset for which to build the hierarchy query.
   * @returns The Knex query builder for the asset hierarchy.
   */
  private getAssetHierarchyQueryBuilder(parentAssetId: number) {
    const qb = this.em.createQueryBuilder(AssetHierarchy).getKnex();

    // query sub-tree
    return qb
      .withRecursive("rec_ah", (qb) =>
        qb
          .select("id", "parent", "child")
          .from("asset_hier")
          .where("parent", parentAssetId)
          .whereNull("deleted_at")
          .orWhere("child", parentAssetId)
          .union((qb) =>
            qb
              .select("ah.id", "ah.parent", "ah.child")
              .from({ ah: "asset_hier" })
              .join("rec_ah", "ah.parent", "rec_ah.child")
              .whereNull("deleted_at")
          )
      )
      .select("rec_ah.id", "rec_ah.parent", "rec_ah.child")
      .distinct()
      .from("rec_ah")
      .join({ ah: "asset_hier" }, "rec_ah.child", "ah.child");
  }

  async countHierarchyNodes(parentAssetId: number, filterById: number[]) {
    const qb = this.getAssetHierarchyQueryBuilder(parentAssetId);

    if (filterById.length > 0) {
      qb.whereIn("rec_ah.child", filterById);
    }

    return (await this.em.execute(qb)).length;
  }
}
