import {
  Role,
  RoleKey,
} from "src/authorization/domain/customer-user-role.entity";
import { User } from "../domain/user.entity";
import { CustomerId } from "src/customers/domain/customer.entity";
import {
  ApiProperty,
  ApiPropertyOptional,
  OmitType,
  PartialType,
} from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

const RoleDto = Object.values(Role)
  .filter((role) => isNaN(Number(role)))
  .map((role) => role as <PERSON><PERSON><PERSON>);

export class CustomerData {
  @ApiProperty({ type: Number, example: 1 })
  id!: CustomerId;

  @ApiProperty({ example: "ABC" })
  nameId!: string;

  @ApiProperty({ example: "ABC Company" })
  name!: string;

  @ApiProperty({ example: "123 Main St" })
  address!: string;
}

export class ScopedRoleDto {
  @ApiProperty({ enum: RoleDto })
  role!: RoleKey;

  @ApiProperty({ type: [Number] })
  customer_ids!: CustomerId[];

  @ApiProperty({ type: CustomerData, required: false, isArray: true })
  customers?: CustomerData[];
}

export class UserDto
  implements Pick<User, "id" | "username" | "email" | "enabled">
{
  @ApiProperty()
  id!: number;

  @ApiProperty({ example: "<EMAIL>" })
  email!: string;

  @ApiProperty({ example: "John" })
  first_name!: string;

  @ApiProperty({ example: "Doe" })
  last_name!: string;

  @ApiProperty({ example: "johndoe" })
  username!: string;

  @ApiPropertyOptional({ enum: RoleDto })
  global_role?: RoleKey | null;

  @ApiPropertyOptional({ type: [ScopedRoleDto] })
  scoped_roles?: ScopedRoleDto[] = [];

  @ApiProperty({ example: true })
  enabled?: boolean;

  @ApiProperty()
  country_code?: string;

  @ApiProperty({})
  phone_number?: string;
}

export class UserCreationDto
  extends OmitType(UserDto, ["id", "enabled"])
  implements Pick<User, "password">
{
  @ApiProperty()
  password!: string;
}

export class UserUpdateDto extends PartialType(
  OmitType(UserCreationDto, ["username"])
) {}

export class SelfUserUpdateDto extends OmitType(UserUpdateDto, [
  "global_role",
  "scoped_roles",
]) {}

export class ResetPasswordDto {
  @ApiProperty({
    required: true,
  })
  @IsString()
  current_password: string;

  @ApiProperty({ required: true })
  @IsString()
  new_password: string;

  @ApiProperty({ required: true })
  @IsString()
  confirm_password: string;
}

export class UserFilterDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  customer_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  customer_id?: CustomerId;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  user_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  role?: string;
}

export type UserFilters = {
  customerName?: string;
  customerId?: string;
  userName?: string;
  userEmail?: string;
  role?: string;
};
