import { Test, TestingModule } from "@nestjs/testing";
import { AssetMeasurementsListController } from "./asset-measurements-list.controller";
import { AssetMeasurementService } from "./asset-measurement.service";
import { AssetWithMeasurementsDto } from "./dto/asset-measurement.dto";

describe("AssetMeasurementsListController", () => {
  let controller: AssetMeasurementsListController;
  let service: AssetMeasurementService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AssetMeasurementsListController],
      providers: [
        {
          provide: AssetMeasurementService,
          useValue: {
            getAllByAssetIdsMeasureIds: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AssetMeasurementsListController>(
      AssetMeasurementsListController
    );
    service = module.get<AssetMeasurementService>(AssetMeasurementService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  it("should call service with correct params and return formatted result", async () => {
    const customerId = "123";
    const body: AssetWithMeasurementsDto[] = [
      { asset_id: 1, measurement_ids: [10, 11] },
      { asset_id: 2, measurement_ids: [20] },
    ];
    const mockResult = [
      { assetId: 1, measurementId: 10 },
      { assetId: 1, measurementId: 11 },
      { assetId: 2, measurementId: 20 },
    ];
    (service.getAllByAssetIdsMeasureIds as jest.Mock).mockResolvedValue(
      mockResult
    );

    const result = await controller.list(customerId, body);

    expect(service.getAllByAssetIdsMeasureIds).toHaveBeenCalledWith({
      customerId: 123,
      data: [
        { assetId: 1, measurementId: [10, 11] },
        { assetId: 2, measurementId: [20] },
      ],
    });
    expect(result).toEqual({
      total: mockResult.length,
      items: mockResult,
    });
  });
});
