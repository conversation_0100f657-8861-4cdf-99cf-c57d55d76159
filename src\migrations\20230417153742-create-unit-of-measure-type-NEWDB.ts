import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230417153742 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "unit_of_measure" ("id" serial primary key, "name" varchar(50) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null, "m_type" int not null);',
    );
    this.addSql(
      'alter table "unit_of_measure" add constraint "unit_of_measure_name_m_type_key" unique ("name", "m_type");',
    );

    this.addSql(
      'alter table "unit_of_measure" add constraint "type_fk" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "unit_of_measure" cascade;');
  }
}
