import { <PERSON><PERSON>ty, ManyTo<PERSON>ne, PrimaryKey, Property } from '@mikro-orm/core';
import { CalculationTemplate } from './calculation-template.entity';
import { Measurement } from '../../measurements/domain/measurement.entity';

@Entity()
export class CalculationInstance {

  @PrimaryKey()
  id!: number;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;

  @ManyToOne({ entity: () => CalculationTemplate, fieldName: 'calculation' })
  calculation!: CalculationTemplate;

  @ManyToOne({ unique: true, entity: () => Measurement, fieldName: 'output_measurement' })
  outputMeasurement!: Measurement;

  @Property()
  ispersisted!: boolean;

  @Property({ nullable: true })
  pollPeriod?: number;

}