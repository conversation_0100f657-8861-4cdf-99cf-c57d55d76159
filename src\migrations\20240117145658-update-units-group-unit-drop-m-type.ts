import { Migration } from '@mikro-orm/migrations';

export class Migration20240117145658 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" drop constraint "units_group_unit_m_type_foreign";',
    );

    this.addSql('drop index "units_group_unit_m_type_default";');
    this.addSql('alter table "units_group_unit" drop column "m_type";');
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "units_group_unit" add column "m_type" int not null;',
    );
    this.addSql(
      'alter table "units_group_unit" add constraint "units_group_unit_m_type_foreign" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'create unique index "units_group_unit_m_type_default" on "units_group_unit"("units_group", "m_type", "is_m_type_default") where "is_m_type_default" is true;',
    );
  }
}
