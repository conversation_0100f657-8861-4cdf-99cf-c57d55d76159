import { ApiProperty, ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayNotEmpty,
  IsArray,
  IsNumber,
  ValidateNested,
} from "class-validator";
import { AssetMeasurementUpdataData } from "../asset-measurement.service";
import { AssetMeasurement } from "../domain/asset-measurement.entity";
import { Measurement } from "../domain/measurement.entity";

export class AssetMeasurementDto
  implements Pick<AssetMeasurement, "id">, Pick<Measurement, "tag">
{
  @ApiProperty()
  id!: number;

  @ApiProperty()
  measurement_id!: number;

  @ApiProperty()
  tag!: string;

  @ApiProperty()
  type_id!: number;

  @ApiProperty()
  data_type_id!: number;

  @ApiProperty()
  value_type_id!: number;

  @ApiProperty({ nullable: true, type: String })
  description!: string | null;

  @ApiProperty({ nullable: true, type: Number })
  metric_id!: number | null;

  @ApiProperty({ nullable: true, type: Number })
  unit_of_measure_id!: number | null;

  @ApiProperty({ nullable: true, type: Number })
  location_id!: number | null;

  @ApiProperty({ nullable: true, type: Number })
  datasource_id!: number | null;

  @ApiProperty({ nullable: true, type: Number })
  meter_factor!: number | null;

  @ApiProperty({ nullable: true, type: Boolean })
  writeback!: boolean | null;
}

export class AssetMeasurementCreationDto extends OmitType(AssetMeasurementDto, [
  "id",
  "measurement_id",
  "description",
  "metric_id",
  "unit_of_measure_id",
  "location_id",
  "datasource_id",
  "meter_factor",
]) {
  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional()
  metric_id?: number;

  @ApiPropertyOptional()
  unit_of_measure_id?: number;

  @ApiPropertyOptional()
  location_id?: number;

  @ApiPropertyOptional()
  datasource_id?: number;

  @ApiPropertyOptional()
  meter_factor?: number;

  @ApiPropertyOptional()
  asset_tag?: string;
}

type AssetMeasurementUpdateDtoType = Omit<
  AssetMeasurementUpdataData,
  "typeId" | "unitOfMeasureId" | "valueTypeId" | "locationId" | "meterFactor"
> & {
  type_id?: number;
  unit_of_measure_id?: number;
  value_type_id?: number;
  location_id?: number;
  meter_factor?: number;
};

export class AssetMeasurementUpdateDto
  implements AssetMeasurementUpdateDtoType
{
  @ApiPropertyOptional()
  tag?: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional()
  type_id?: number;

  @ApiPropertyOptional()
  unit_of_measure_id?: number;

  @ApiPropertyOptional()
  value_type_id?: number;

  @ApiPropertyOptional()
  location_id?: number;

  @ApiPropertyOptional()
  meter_factor?: number;

  @ApiPropertyOptional()
  metric_id?: number;

  @ApiPropertyOptional()
  data_type_id?: number;

  @ApiPropertyOptional()
  asset_tag?: string;
}

export class AssetWithMeasurementsDto {
  @ApiProperty({ example: 101 })
  @IsNumber()
  asset_id!: number;

  @ApiProperty({ type: [Number], example: [1, 2, 3] })
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  measurement_ids!: number[];
}

export class BulkAssetMeasurementsDto {
  @ApiProperty({ type: [AssetWithMeasurementsDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AssetWithMeasurementsDto)
  body!: AssetWithMeasurementsDto[];
}
