import { <PERSON><PERSON><PERSON>, ManyToOne, <PERSON><PERSON>ey, Property } from "@mikro-orm/core";
import { User } from "../../users/domain/user.entity";
import { Alerts } from "./alert.entity";

@Entity()
export class AlertUsers {

    @PrimaryKey()
    id!: number;

    @ManyToOne({ entity: () => User, fieldName: 'user' })
    user!: User;

    @ManyToOne({ entity: () => Alerts, fieldName: 'alert', onUpdateIntegrity: 'cascade', onDelete: 'cascade' })
    alert!: Alerts;

    @Property()
    notificationtype!: number;

}