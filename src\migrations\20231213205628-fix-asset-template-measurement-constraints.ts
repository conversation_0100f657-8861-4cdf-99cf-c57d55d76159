import { Migration } from '@mikro-orm/migrations';

export class Migration20231213205628 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_data_type_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_m_type_foreign" foreign key ("m_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_data_type_foreign" foreign key ("data_type") references "data_type" ("id") on update cascade;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_m_type_foreign";',
    );
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_data_type_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_data_type_foreign" foreign key ("data_type") references "measurement_type" ("id") on update cascade;',
    );
  }
}
