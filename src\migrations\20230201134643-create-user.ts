import { Migration } from '@mikro-orm/migrations';

export class Migration20230201134643 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'create table "user" ("id" serial primary key, "username" varchar(50) not null, \
      "password" varchar(100) not null, "email" varchar(300) not null, \
      "first_name" varchar(50) not null, "last_name" varchar(50) not null, \
      "role_id" smallint not null, "created_at" timestamptz(6) not null, \
      "enabled" boolean null default true, "created_by" int null);',
    );

    this.addSql(
      'alter table "user" add constraint "user_username_unique" unique ("username");',
    );
    this.addSql(
      'alter table "user" add constraint "user_email_unique" unique ("email");',
    );

    this.addSql(
      'alter table "user" add constraint "user_created_by_foreign" \
      foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "user" cascade;');
  }
}
