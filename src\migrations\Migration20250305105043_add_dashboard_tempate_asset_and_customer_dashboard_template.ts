import { Migration } from "@mikro-orm/migrations";

export class Migration20250305105043_add_dashboard_template_asset_and_customer_dashboard_template extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "dashboard_template" add column "customer" int null;'
    );
    this.addSql(
      'alter table "dashboard_template" add constraint "dashboard_template_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade on delete set null;'
    );

    this.addSql(
      'alter table "dashboard" add column "asset" int null, add column "dashboardTemplate" int null;'
    );
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_asset_foreign" foreign key ("asset") references "asset" ("id") on update cascade on delete set null;'
    );
    this.addSql(
      'alter table "dashboard" add constraint "dashboard_dashboardTemplate_foreign" foreign key ("dashboardTemplate") references "dashboard_template" ("id") on update cascade on delete set null;'
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "dashboard" drop constraint "dashboard_asset_foreign";'
    );
    this.addSql(
      'alter table "dashboard" drop constraint "dashboard_dashboardTemplate_foreign";'
    );

    this.addSql(
      'alter table "dashboard_template" drop constraint "dashboard_template_customer_foreign";'
    );

    this.addSql('alter table "dashboard" drop column "asset";');
    this.addSql('alter table "dashboard" drop column "dashboardTemplate";');

    this.addSql('alter table "dashboard_template" drop column "customer";');
  }
}
