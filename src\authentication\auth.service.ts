import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import crypto from "crypto";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { PasswordEncoder } from "src/security/password-encoder.service";
import { User } from "src/users/domain/user.entity";
import { UserService } from "src/users/user.service";
import authConfiguration from "./auth.config";
import { JwtPayload } from "./infra/jwt.strategy";

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly passwordEncoder: PasswordEncoder,
    private readonly jwtService: JwtService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>,
    private logger: Logger
  ) {}

  async isValidUser(username: string, password: string): Promise<User | null> {
    const user = await this.userService.findByUsername(username);

    if (
      user !== null &&
      user.enabled &&
      (await this.passwordEncoder.verify(password, user.password))
    ) {
      return user;
    }

    // failed validation
    return null;
  }

  isValidCsrfToken(accessToken: string, csrfToken: string): boolean {
    return this.hash(accessToken) === csrfToken;
  }

  hasCustomerRole(accessToken: string, customerId: number, requiredRole: Role) {
    const payload: JwtPayload = this.jwtService.decode(
      accessToken
    ) as JwtPayload;
    let hasRole = false;
    switch (requiredRole) {
      case Role.USER:
        hasRole =
          payload.roles.USER.includes(customerId) ||
          payload.roles.POWER_USER.includes(customerId) ||
          payload.roles.ADMIN.includes(customerId);
        break;
      case Role.POWER_USER:
        hasRole =
          payload.roles.POWER_USER.includes(customerId) ||
          payload.roles.ADMIN.includes(customerId);
        break;
      case Role.ADMIN:
        hasRole = payload.roles.ADMIN.includes(customerId);
        break;
      default:
        hasRole = false;
    }
    return hasRole;
  }

  hasRoles(accessToken: string, requiredRoles: Role) {
    const payload: JwtPayload = this.jwtService.decode(
      accessToken
    ) as JwtPayload;
    this.logger.log(payload, requiredRoles);
    switch (requiredRoles) {
      case Role.USER:
        return payload.roles.USER.length > 0;
      case Role.POWER_USER:
        return payload.roles.POWER_USER.length > 0;
      case Role.ADMIN:
        return payload.roles.ADMIN.length > 0;
      default:
        return false;
    }
  }

  hasMaxRoles(
    accessToken: string,
    requiredRole: Role,
    activeCustomer: number
  ): boolean {
    const payload: JwtPayload = this.jwtService.decode(
      accessToken
    ) as JwtPayload;

    let hasMaxRole = false;
    if (requiredRole === Role.ADMIN) {
      hasMaxRole = payload.roles.ADMIN?.includes(activeCustomer) ?? false;
    } else if (requiredRole === Role.POWER_USER) {
      hasMaxRole =
        payload.roles.POWER_USER?.includes(activeCustomer) ?? false;
      if (!hasMaxRole)
        hasMaxRole = payload.roles.ADMIN?.includes(activeCustomer) ?? false;
    } else {
      hasMaxRole = payload.roles.USER?.includes(activeCustomer) ?? false;
      if (!hasMaxRole) 
        hasMaxRole = payload.roles.POWER_USER?.includes(activeCustomer) ?? false;
      if (!hasMaxRole)
        hasMaxRole = payload.roles.ADMIN?.includes(activeCustomer) ?? false;
    }
    return hasMaxRole;

    // switch (requiredRole) {
    //   case Role.USER:
    //     return payload.roles.USER?.includes(activeCustomer) ?? false;
    //   case Role.POWER_USER:
    //     return payload.roles.POWER_USER?.includes(activeCustomer) ?? false;
    //   case Role.ADMIN:
    //     return payload.roles.ADMIN?.includes(activeCustomer) ?? false;
    //   default:
    //     return false;
    // }
  }

  async login(user: User) {
    let roles: {
      ADMIN: number[];
      USER: number[];
      POWER_USER: number[];
    } = { ADMIN: [], USER: [], POWER_USER: [] };
    if (user.hasGlobalRole()) {
      const customers = await this.userService.allCustomers();
      roles.ADMIN = customers;
      roles.USER = customers;
      roles.POWER_USER = customers;
    } else {
      roles = user.customerRoles.reduce(
        (acc, role) => ({
          ...acc,
          [Role[role.role]]: [
            ...(acc[Role[role.role]] || []),
            role.customer.id,
          ],
        }),
        { ADMIN: [], USER: [], POWER_USER: [] }
      );
    }
    const payload: JwtPayload = {
      username: user.username,
      sub: user.id,
      roles: roles,
    };
    const accessToken = this.jwtService.sign(payload, {
      secret: this.authConfig.jwtSecret,
      expiresIn: this.authConfig.sessionDurationMinutes * 60,
    });

    const csrfToken = this.hash(accessToken);

    return {
      accessToken,
      csrfToken,
    };
  }

  private hash(data: string) {
    return crypto.createHash("sha256").update(data).digest("base64");
  }
}
