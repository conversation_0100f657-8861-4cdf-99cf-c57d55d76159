import { Migration } from "@mikro-orm/migrations";

export class Migration20250408085502_alert_softdelete_keys extends Migration {
  async up(): Promise<void> {
    this.addSql(`
      alter table "alerts"
      add column "deleted_at" timestamptz(6) null,
      add column "deleted_by" int null;
    `);

    this.addSql(`
      alter table "alerts"
      add constraint "alerts_deleted_by_foreign"
      foreign key ("deleted_by") references "user" ("id")
      on update cascade on delete set null;
    `);
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "alerts" drop constraint "alerts_deleted_by_foreign";'
    );
    this.addSql('alter table "alerts" drop column "deleted_at";');
    this.addSql('alter table "alerts" drop column "deleted_by";');
  }
}
