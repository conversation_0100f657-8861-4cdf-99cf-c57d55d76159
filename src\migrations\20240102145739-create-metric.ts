import { Migration } from '@mikro-orm/migrations';

export class Migration20240102145739 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'create table "metric" ("id" serial primary key, "name" varchar(150) not null, "measurement_type" int not null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null, "created_by" int null, "updated_by" int null);',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_name_unique" unique ("name");',
    );

    this.addSql(
      'alter table "metric" add constraint "metric_measurement_type_foreign" foreign key ("measurement_type") references "measurement_type" ("id") on update cascade;',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "metric" add constraint "metric_updated_by_foreign" foreign key ("updated_by") references "user" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "metric" cascade;');

    this.addSql(
      'alter table "asset" alter column "enabled" type boolean using ("enabled"::boolean);',
    );
    this.addSql('alter table "asset" alter column "enabled" set default true;');
  }
}
