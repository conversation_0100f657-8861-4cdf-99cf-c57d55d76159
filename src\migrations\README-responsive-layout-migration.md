# Dashboard Responsive Layout Migration

## Overview

This migration adds responsive layout support to the dashboard system, allowing different widget layouts for desktop and mobile views.

## Migration: `20250604151745-add-responsive-layouts-to-dashboard.ts`

### What it does

1. **Adds new properties to dashboard data JSON:**
   - `desktopMobile: 0 | 1` - Current device mode (0 = desktop, 1 = mobile)
   - `responsiveLayouts` - Object containing separate layouts for desktop and mobile

2. **Preserves existing data:**
   - Keeps the original `widget.widgetLayout` array for backward compatibility
   - Copies current layout to both desktop and mobile layouts initially

3. **Safe migration:**
   - <PERSON>les malformed JSON gracefully
   - Skips already migrated dashboards
   - Provides detailed logging
   - Supports rollback

### New Data Structure

**Before Migration:**
```json
{
  "widget": {
    "widgets": [...],
    "widgetLayout": [
      { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
      { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
    ]
  },
  "topPanel": {...},
  "chart": {...}
}
```

**After Migration:**
```json
{
  "widget": {
    "widgets": [...],
    "widgetLayout": [
      { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
      { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
    ]
  },
  "topPanel": {...},
  "chart": {...},
  "desktopMobile": 0,
  "responsiveLayouts": {
    "desktop": {
      "widgetLayout": [
        { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
        { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
      ]
    },
    "mobile": {
      "widgetLayout": [
        { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
        { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
      ]
    }
  }
}
```

## Type Definitions

New TypeScript types have been added to `src/dashboards/domain/dashboard.types.ts`:

- `Layout` - React-grid-layout compatible layout object
- `DeviceMode` - Device mode type (0 | 1)
- `ResponsiveLayouts` - Structure for responsive layouts
- `DashboardState` - Complete dashboard data structure

## Running the Migration

```bash
# Run the migration
npm run migration:up

# Rollback if needed
npm run migration:down
```

## Testing

A test script is provided to verify the migration logic:

```bash
cd src/migrations
npx ts-node test-responsive-layout-migration.ts
```

## Backward Compatibility

- The original `widget.widgetLayout` property is preserved
- Existing code that reads this property will continue to work
- New code should use `responsiveLayouts.desktop.widgetLayout` or `responsiveLayouts.mobile.widgetLayout`

## Usage in Application Code

```typescript
import { DashboardState, DeviceMode } from 'src/dashboards/domain/dashboard.types';

// Parse dashboard data
const dashboardData: DashboardState = JSON.parse(dashboard.data);

// Get current device mode
const currentMode: DeviceMode = dashboardData.desktopMobile || 0;

// Get layout for current device
const currentLayout = currentMode === 0 
  ? dashboardData.responsiveLayouts?.desktop.widgetLayout 
  : dashboardData.responsiveLayouts?.mobile.widgetLayout;

// Fallback to legacy layout if responsive layouts not available
const layout = currentLayout || dashboardData.widget.widgetLayout;
```

## Error Handling

The migration includes comprehensive error handling:
- Skips dashboards with invalid JSON
- Logs detailed error messages
- Continues processing other dashboards if one fails
- Provides summary of successful vs failed migrations

## Rollback Support

The migration can be safely rolled back:
- Removes `desktopMobile` and `responsiveLayouts` properties
- Preserves original `widget.widgetLayout` data
- No data loss during rollback
