import { MikroOrmModule } from "@mikro-orm/nestjs";
import { forwardRef, Module } from "@nestjs/common";
import { Alerts } from "src/alert/domain/alert.entity";
import { AuthModule } from "src/authentication/auth.module";
import { Alert<PERSON>tatsController } from "./alert-stats.controller";
import { AlertStatsService } from "./alert-stats.service";
import { ExcursionStats } from "./domain/excursion-stats.entity";
import { ExcursionController } from "./excursions.controller";
import { AlertStatsRepository } from "./repository/alert-stats.repository";

@Module({
  imports: [
    MikroOrmModule.forFeature([ExcursionStats, Alerts]),
    forwardRef(() => AuthModule),
  ],
  controllers: [AlertStatsController, ExcursionController],
  providers: [AlertStatsService, AlertStatsRepository],
  exports: [AlertStatsService],
})
export class AlertStatsModule {}
