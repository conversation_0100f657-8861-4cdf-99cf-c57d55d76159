import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230802185555 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'alter table "measurement" add column "deleted" timestamptz(6) null, add column "deletedby" int null;',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('alter table "measurement" drop column "deleted";');
    this.addSql('alter table "measurement" drop column "deletedby";');
  }
}
