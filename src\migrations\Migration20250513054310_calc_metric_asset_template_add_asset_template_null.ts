import { Migration } from '@mikro-orm/migrations';

export class Migration20250513054310_calc_metric_asset_template_add_asset_template_null extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" drop constraint "calculation_metric_instance_assetTemplate_foreign";');

    this.addSql('alter table "calculation_metric_instance" alter column "assetTemplate" type int using ("assetTemplate"::int);');
    this.addSql('alter table "calculation_metric_instance" alter column "assetTemplate" set not null;');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_assetTemplate_foreign" foreign key ("assetTemplate") references "asset_template" ("id") on update cascade;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" drop constraint "calculation_metric_instance_assetTemplate_foreign";');

    this.addSql('alter table "calculation_metric_instance" alter column "assetTemplate" type int using ("assetTemplate"::int);');
    this.addSql('alter table "calculation_metric_instance" alter column "assetTemplate" drop not null;');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_assetTemplate_foreign" foreign key ("assetTemplate") references "asset_template" ("id") on update cascade on delete set null;');
  }

}
