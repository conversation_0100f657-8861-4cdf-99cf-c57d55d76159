import { Entity, <PERSON><PERSON>ey } from "@mikro-orm/core";
import { ApiProperty } from "@nestjs/swagger";
import { IsString } from "class-validator";

@Entity()
export class CalcDataTypes {
  @PrimaryKey()
  id!: number;

  @ApiProperty({ type: "string", required: true, example: 23245 })
  @IsString()
  inputId: string;

  @ApiProperty({ type: "number", example: 1, nullable: true })
  created_by: number;

  @ApiProperty({ type: "number", example: 1 })
  created_at: Date;

  @ApiProperty({ type: "number", example: 1, nullable: true })
  updated_by: number;

  @ApiProperty({ type: "number", example: 1 })
  updated_at: Date;
}
