import { MikroOrmModule } from '@mikro-orm/nestjs';
import { forwardRef, Logger, Module } from '@nestjs/common';
import { AuthModule } from 'src/authentication/auth.module';
import { SecurityModule } from 'src/security/security.module';
import { User } from './domain/user.entity';
import { UserApiController } from './user.controller';
import { UserService } from './user.service';
import { UserRepository } from './repository/user.repository';
import { CustomersModule } from 'src/customers/customers.module';
import { CustomerUserApiController } from './customer-user.controller';
import { UserMapper } from './dto/user.mapper';
import { UserPreferences } from './domain/user_preferences.entity';

@Module({
  imports: [
    MikroOrmModule.forFeature([User, UserPreferences]),
    SecurityModule,
    CustomersModule,
    forwardRef(() => AuthModule),
  ],
  providers: [
    {
      provide: Logger,
      useValue: new Logger(UserService.name),
    },
    UserMapper,
    UserService,
    UserRepository,
  ],
  controllers: [UserApiController, CustomerUserApiController],
  exports: [UserService],
})
export class UserModule { }
