import { Test } from '@nestjs/testing';
import { AssetMeasurementRepository } from './asset-measurement.repository';
import { AssetMeasurement } from '../domain/asset-measurement.entity';
import { EntityManager, EntityRepository } from '@mikro-orm/postgresql';
import { MikroOrmModule, getRepositoryToken } from '@mikro-orm/nestjs';
import { assetMeasurementFactory } from '../__tests__/factories';
import config from 'src/mikro-orm.config';
import { User } from 'src/users/domain/user.entity';
import { Measurement } from '../domain/measurement.entity';
import { Location } from '../domain/location.entity';
import { MeasurementType } from '../domain/measurement-type.entity';
import { UnitOfMeasure } from '../domain/unit-of-measure.entity';
import { DataType } from '../domain/data-type.entity';
import { ValueType } from '../domain/value-type.entity';
import { Datasource } from '../domain/datasource.entity';
import { Customer } from 'src/customers/domain/customer.entity';
import { AssetType } from 'src/assets/domain/asset-type.entity';
import { Metric } from '../../assets/domain/metric.entity';

describe('Asset Measurement Repository', () => {
  let entityRepositoryMock: jest.Mocked<
    Pick<EntityRepository<AssetMeasurement>, 'persistAndFlush' | 'findOne'>
  >;
  let repository: AssetMeasurementRepository;
  const assetMeasurementId = 3;
  const assetId = 40;
  const customerId = 55;

  beforeEach(async () => {
    entityRepositoryMock = {
      persistAndFlush: jest.fn(),
      findOne: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      imports: [
        MikroOrmModule.forRoot({
          ...config,
          entitiesTs: [
            User,
            AssetMeasurement,
            Measurement,
            Location,
            MeasurementType,
            UnitOfMeasure,
            DataType,
            ValueType,
            Datasource,
            Customer,
            AssetType,
            Metric,
          ],
          allowGlobalContext: true,
          connect: false,
          dbName: 'test',
        }),
      ],
      providers: [
        {
          provide: getRepositoryToken(AssetMeasurement),
          useValue: entityRepositoryMock,
        },
        {
          provide: EntityManager,
          useValue: {
            count: jest.fn(),
          },
        },
        AssetMeasurementRepository,
      ],
    }).compile();
    repository = moduleRef.get(AssetMeasurementRepository);
  });

  describe('findById', () => {
    test('with filters should use them in where condition', async () => {
      repository.findById(assetMeasurementId, { assetId, customerId });

      const whereFilter = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(whereFilter).toStrictEqual({
        id: assetMeasurementId,
        deletedAt: null,
        asset: {
          id: assetId,
          customer: { id: customerId },
          deletedAt: null,
          $or: [{ enabled: true }, { enabled: null }],
        },
        measurement: {
          $or: [{ enabled: true }, { enabled: null }],
        },
      });
    });

    test('with only asset should use it in where condition', async () => {
      repository.findById(assetMeasurementId, { assetId });

      const whereFilter = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(whereFilter).toStrictEqual({
        id: assetMeasurementId,
        deletedAt: null,
        asset: {
          id: assetId,
          deletedAt: null,
          $or: [{ enabled: true }, { enabled: null }],
        },
        measurement: {
          $or: [{ enabled: true }, { enabled: null }],
        },
      });
    });

    test('with only customer should use it in where condition', async () => {
      repository.findById(assetMeasurementId, { customerId });

      const whereFilter = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(whereFilter).toStrictEqual({
        id: assetMeasurementId,
        deletedAt: null,
        asset: { customer: { id: customerId } },
        measurement: {
          $or: [{ enabled: true }, { enabled: null }],
        },
      });
    });

    test('without filter should only use asset measurement id', async () => {
      repository.findById(assetMeasurementId);

      const whereFilter = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(whereFilter).toStrictEqual({
        id: assetMeasurementId,
        deletedAt: null,
        measurement: {
          $or: [{ enabled: true }, { enabled: null }],
        },
      });
    });
  });

  describe('remove', () => {
    test('without user should only set deletedAt', async () => {
      const pumpMeasurement =
        assetMeasurementFactory.createPumpVoltage(assetId);

      await repository.remove(pumpMeasurement);

      const persistedMeasurement = entityRepositoryMock.persistAndFlush.mock
        .calls[0][0] as AssetMeasurement;
      expect(persistedMeasurement.deletedAt).not.toBeUndefined();
      expect(persistedMeasurement.deletedById).toBeUndefined();
    });

    test('with user should set deletedAt and deletedById', async () => {
      const pumpMeasurement =
        assetMeasurementFactory.createPumpVoltage(assetId);
      const userId = 42;

      await repository.remove(pumpMeasurement, userId);

      const persistedMeasurement = entityRepositoryMock.persistAndFlush.mock
        .calls[0][0] as AssetMeasurement;
      expect(persistedMeasurement.deletedAt).not.toBeUndefined();
      expect(persistedMeasurement.deletedById).toBe(userId);
    });
  });

  describe('update', () => {
    it('should set up updatedAt and updatedBy', async () => {
      const pumpMeasurement =
        assetMeasurementFactory.createPumpVoltage(assetId);
      const userId = 42;

      await repository.update(pumpMeasurement, userId);

      const updatedMeasurement = entityRepositoryMock.persistAndFlush.mock
        .calls[0][0] as AssetMeasurement;
      expect(updatedMeasurement.updatedAt).not.toBeUndefined();
      expect(updatedMeasurement.updatedBy?.id).toBe(userId);
    });

    it('should call upsert', async () => {
      const pumpMeasurement =
        assetMeasurementFactory.createPumpVoltage(assetId);
      const userId = 42;

      await repository.update(pumpMeasurement, userId);

      expect(entityRepositoryMock.persistAndFlush.mock.calls.length).toBe(1);
    });
  });
});
