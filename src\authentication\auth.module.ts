import { forwardRef, <PERSON><PERSON>, <PERSON>dule } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { SecurityModule } from "src/security/security.module";
import { UserModule } from "src/users/user.module";
import { AuthService } from "./auth.service";
import { JwtStrategy } from "./infra/jwt.strategy";
import { LocalStrategy } from "./infra/local.strategy";
import { SessionsApiController } from "./sessions.controller";

@Module({
  imports: [
    SecurityModule,
    forwardRef(() => UserModule),
    PassportModule,
    JwtModule.register({}),
  ],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    {
      provide: Logger,
      useValue: new Logger(AuthService.name),
    },
  ],
  exports: [AuthService],
  controllers: [SessionsApiController],
})
export class AuthModule {}
