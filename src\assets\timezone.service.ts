import { EntityManager } from '@mikro-orm/postgresql';
import { Injectable } from '@nestjs/common';
import { TimeZone } from './domain/time-zone.entity';

@Injectable()
export class TimeZoneService {
  constructor(private readonly entityManager: EntityManager) {}

  async getAll(): Promise<TimeZone[]> {
    return await this.entityManager.find(
      TimeZone,
      {},
      { orderBy: { name: 'asc' } },
    );
  }

  async exists(timeZone: string) {
    return (
      Number(await this.entityManager.count(TimeZone, { name: timeZone })) === 1
    );
  }
}
