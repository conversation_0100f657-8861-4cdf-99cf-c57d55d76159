import { Migration } from '@mikro-orm/migrations';

export class Migration20250512190439_calc_metric_asset_template_add_asset_template_nullable extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" add column "assetTemplate" int null;');
    this.addSql('alter table "calculation_metric_instance" add constraint "calculation_metric_instance_assetTemplate_foreign" foreign key ("assetTemplate") references "asset_template" ("id") on update cascade on delete set null;');

    this.addSql('alter table "asset_template_measurement" add column "calculation_metric_instance" int null;');
    this.addSql('alter table "asset_template_measurement" add constraint "asset_template_measurement_calculation_metric_instance_foreign" foreign key ("calculation_metric_instance") references "calculation_metric_instance" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "calculation_metric_instance" drop constraint "calculation_metric_instance_assetTemplate_foreign";');

    this.addSql('alter table "asset_template_measurement" drop constraint "asset_template_measurement_calculation_metric_instance_foreign";');

    this.addSql('alter table "calculation_metric_instance" drop column "assetTemplate";');

    this.addSql('alter table "asset_template_measurement" drop column "calculation_metric_instance";');
  }

}
