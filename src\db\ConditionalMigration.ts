import { Migration } from '@mikro-orm/migrations';

const isNewDatabase = () => String(process.env.NEW_DB).toLowerCase() === 'true';

export abstract class NewDbMigration extends Migration {
  abstract conditionalUp(): Promise<void>;

  async conditionalDown(): Promise<void> {
    super.down();
  }

  async up(): Promise<void> {
    if (isNewDatabase()) {
      await this.conditionalUp();
    } else {
      console.log('[EXISTING DB] applying empty migration');
    }
  }

  async down(): Promise<void> {
    if (isNewDatabase()) {
      await this.conditionalDown();
    } else {
      console.log('[EXISTING DB] reverting empty migration');
    }
  }
}

export abstract class ExistingDbMigration extends Migration {
  abstract conditionalUp(): Promise<void>;

  async conditionalDown(): Promise<void> {
    super.down();
  }

  async up(): Promise<void> {
    if (isNewDatabase()) {
      console.log('[NEW DB] applying empty migration');
    } else {
      await this.conditionalUp();
    }
  }

  async down(): Promise<void> {
    if (isNewDatabase()) {
      console.log('[NEW DB] reverting empty migration');
    } else {
      await this.conditionalDown();
    }
  }
}
