import { Migration } from '@mikro-orm/migrations';

export class Migration20240202191853 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "units_group_default_unit" drop constraint "units_group_default_unit_units_group_id_foreign";',
    );

    this.addSql(
      'alter table "units_group_default_unit" drop constraint "units_group_default_unit_pkey";',
    );
    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_units_group_id_foreign" foreign key ("units_group_id") references "units_group" ("id") on update cascade on delete cascade;',
    );
    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_pkey" primary key ("unit_group_unit_id", "units_group_id");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "units_group_default_unit" drop constraint "units_group_default_unit_units_group_id_foreign";',
    );

    this.addSql(
      'alter table "asset" alter column "enabled" type bool using ("enabled"::bool);',
    );
    this.addSql('alter table "asset" alter column "enabled" set default true;');

    this.addSql(
      'alter table "units_group_default_unit" drop constraint "units_group_default_unit_pkey";',
    );
    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_units_group_id_foreign" foreign key ("units_group_id") references "units_group" ("id") on update cascade on delete no action;',
    );
    this.addSql(
      'alter table "units_group_default_unit" add constraint "units_group_default_unit_pkey" primary key ("units_group_id", "unit_group_unit_id");',
    );
  }
}
