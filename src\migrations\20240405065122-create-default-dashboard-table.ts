import { Migration } from '@mikro-orm/migrations';

export class Migration20240405065122 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "dashboard_default" ("customer_id" int not null, "user_id" int not null, "dashboard_id" int not null, "created_at" date not null, "created_by" int not null, constraint "dashboard_default_pkey" primary key ("customer_id", "user_id"));');

    this.addSql('alter table "dashboard_default" add constraint "dashboard_default_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade on delete cascade;');
    this.addSql('alter table "dashboard_default" add constraint "dashboard_default_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete cascade;');
    this.addSql('alter table "dashboard_default" add constraint "dashboard_default_dashboard_id_foreign" foreign key ("dashboard_id") references "dashboard" ("id") on update cascade on delete cascade;');
    this.addSql('alter table "dashboard_default" add constraint "dashboard_default_created_by_foreign" foreign key ("created_by") references "user" ("id") on update cascade on delete cascade;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "dashboard_default" cascade;');
  }

}
