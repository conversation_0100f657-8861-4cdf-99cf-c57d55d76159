import { Injectable } from "@nestjs/common";
import { Datasource } from "./domain/datasource.entity";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/core";

@Injectable()
export class DatasourceService {
  constructor(
    @InjectRepository(Datasource)
    private readonly datasourceRepository: EntityRepository<Datasource>
  ) {}

  async getAll(extra?: boolean) {
    const datasources = await this.datasourceRepository.find(
      {
        name: extra ? {} : { $ne: "CO2e" }, // Exclude "CO2e" directly at the query level
      },
      {
        orderBy: { name: "ASC" },
      }
    );
    return datasources;
  }

  async getById(id: number): Promise<Datasource | null> {
    return await this.datasourceRepository.findOne({ id });
  }
}
