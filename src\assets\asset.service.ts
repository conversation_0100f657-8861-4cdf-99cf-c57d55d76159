import { Injectable, NotFoundException } from "@nestjs/common";
import { CustomerService } from "src/customers/customer.service";
import { CustomerId } from "src/customers/domain/customer.entity";
import { InvalidInputException } from "src/errors/exceptions";
import { UserId } from "src/users/domain/user.entity";
import { AssetTypeService } from "./asset-type.service";
import {
  Asset,
  AssetCreationParams,
  AssetFactory,
  AssetId,
} from "./domain/asset.entity";
import { TimeZoneService } from "./timezone.service";
import { AssetRepository } from "./repository/asset.repository";
import { AssetHierarchyService } from "./asset-hierarchy.service";
import { TransactionFactory } from "src/db/TransactionFactory";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  ASSET_BATCH_REMOVE_EVENT_KEY,
  AssetBatchRemoveEvent,
} from "./domain/events/asset-batch-removed.event";
import {
  ASSET_UPDATE_EVENT_KEY,
  AssetUpdateEvent,
} from "./domain/events/asset-update.event";
import { Reference } from "@mikro-orm/core";
import { AssetType } from "./domain/asset-type.entity";

export type AssetCreationData = AssetCreationParams & {
  parentIds: AssetId[];
  customerId: number;
};

export type AssetUpdateData = Partial<
  Omit<
    AssetCreationData,
    "customerId" | "assetType" | "customer" | "createdBy" | "deletedBy"
  >
>;

@Injectable()
export class AssetService {
  constructor(
    private readonly customerService: CustomerService,
    private readonly assetTypeService: AssetTypeService,
    private readonly timeZonesService: TimeZoneService,
    private readonly assetRepository: AssetRepository,
    private readonly assetHierarchyService: AssetHierarchyService,
    private readonly transactionFactory: TransactionFactory,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async create(asset: AssetCreationData, runById: UserId): Promise<Asset> {
    return await this.transactionFactory.run(async () => {
      const { parentIds, customerId, assetTypeId, timeZone, ...rest } = asset;

      if ((await this.customerService.findById(customerId)) === null) {
        throw new InvalidInputException("Customer does not exist");
      }

      if ((await this.assetTypeService.findById(assetTypeId)) == null) {
        throw new InvalidInputException("Asset type does not exist");
      }

      if (timeZone && !(await this.timeZonesService.exists(timeZone))) {
        throw new InvalidInputException("Time zone does not exist");
      }

      const newAsset = AssetFactory.create({
        timeZone,
        assetTypeId,
        customerId,
        ...rest,
      });

      await this.assetRepository.add(newAsset);

      if (parentIds.length > 0) {
        await Promise.all(
          parentIds.map(
            async (parentId) =>
              await this.assetHierarchyService.addChild(
                parentId,
                newAsset.id,
                runById
              )
          )
        );
      } else {
        await this.assetHierarchyService.addRoot(newAsset.id, runById);
      }

      return newAsset;
    });
  }

  async findById(id: AssetId, customerId?: CustomerId): Promise<Asset | null> {
    return await this.assetRepository.findById(id, { customerId });
  }

  async getAll({
    customerId,
    ids,
    parentIds,
  }: {
    customerId: CustomerId;
    ids?: AssetId[];
    parentIds?: AssetId[];
  }): Promise<Asset[]> {
    return await this.assetRepository.getAllByCustomerId(customerId, {
      ids,
      parentIds,
    });
  }

  async remove(rootAssetId: AssetId, customerId: CustomerId, runById: UserId) {
    await this.transactionFactory.run(async () => {
      const rootAsset = await this.findById(rootAssetId, customerId);

      if (rootAsset === null) {
        throw new NotFoundException("Asset does not exist");
      }

      // Remove root asset and its related hierarchy
      const removedAssetIds = await this.assetRepository.removeHierarchy(
        rootAsset,
        runById
      );

      const batchRemoveResults: boolean[] = await this.eventEmitter.emitAsync(
        ASSET_BATCH_REMOVE_EVENT_KEY,
        new AssetBatchRemoveEvent(removedAssetIds, new Date(), runById)
      );

      if (batchRemoveResults.filter((result) => result !== true).length > 0) {
        throw new Error("Failed to apply asset batch remove event");
      }
    });
  }

  async update(
    customerId: CustomerId,
    assetId: AssetId,
    update: AssetUpdateData,
    runBy: UserId,
    headers: Request["headers"]
  ) {
    return await this.transactionFactory.run(async (em) => {
      const asset = await this.findById(assetId, customerId);

      if (asset === null) {
        throw new NotFoundException("Asset does not exist");
      }

      if (update.description) {
        asset.description = update.description;
      }

      if (update.latitude) {
        asset.latitude = update.latitude;
      }

      if (update.longitude) {
        asset.longitude = update.longitude;
      }

      if (update.tag) {
        asset.tag = update.tag;
      }

      const checkAsset = await this.assetTypeService.findById(
        update.assetTypeId
      );
      if (update.assetTypeId && checkAsset == null) {
        throw new InvalidInputException("Asset type does not exist");
      } else if (update.assetTypeId) {
        asset.assetTypeId = checkAsset.id;
        asset.assetType = Reference.createFromPK(AssetType, checkAsset.id);
      }

      if (
        update.timeZone &&
        !(await this.timeZonesService.exists(update.timeZone))
      ) {
        throw new InvalidInputException("Time zone does not exist");
      } else if (update.timeZone) {
        asset.timeZone = update.timeZone;
      }

      if (update.parentIds) {
        const toAdd = update.parentIds.filter(
          (parentId) => !asset.parentIds.includes(parentId)
        );

        const toRemove = asset.parentIds.filter(
          (parentId) => update.parentIds && !update.parentIds.includes(parentId)
        );

        if (
          asset.parentIds.length > 0 &&
          asset.parentIds.length === toRemove.length &&
          toAdd.length === 0
        ) {
          await this.assetHierarchyService.addRoot(asset.id, runBy);
        }

        if (
          toAdd.length > 0 &&
          (await this.assetRepository.countHierarchyNodes(asset.id, toAdd)) > 0
        ) {
          throw new InvalidInputException("New parents create a cycle");
        }

        await Promise.all(
          toAdd.map(
            async (parentId) =>
              await this.assetHierarchyService.addChild(
                parentId,
                asset.id,
                runBy
              )
          )
        );

        await Promise.all(
          toRemove.map(
            async (parentId) =>
              await this.assetHierarchyService.removeChild(
                parentId,
                asset.id,
                runBy
              )
          )
        );
      }
      // if (asset.assetTemplate) {
      //   asset.assetTemplate = null;
      // }
      // if (asset.assetTemplateId) {
      //   asset.assetTemplateId = null;
      // }
      if (asset.unitsGroup) {
        asset.unitsGroup = null;
      }
      await this.assetRepository.update(asset, runBy);

      const updateEventResult: boolean[] = await this.eventEmitter.emitAsync(
        ASSET_UPDATE_EVENT_KEY,
        new AssetUpdateEvent(
          asset.id,
          new Date(),
          runBy,
          customerId,
          headers,
          em
        )
      );

      if (updateEventResult.filter((result) => result !== true).length > 0) {
        throw new Error("Failed to apply asset update event");
      }
    });
  }
}
