import { EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import { User, UserId } from "src/users/domain/user.entity";
import {
  AssetType,
  AssetTypeWithTemplateCount,
} from "../domain/asset-type.entity";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";

@Injectable()
export class AssetTypeRepository {
  constructor(
    @InjectRepository(AssetType)
    private readonly assetTypeRepository: EntityRepository<AssetType>,
    @InjectRepository(AssetTemplate)
    private readonly entityRepository: EntityRepository<AssetTemplate>
  ) {}

  async findById(id: number): Promise<AssetType | null> {
    return await this.assetTypeRepository.findOne({ id });
  }

  async getAll(customerId?: number): Promise<AssetTypeWithTemplateCount[]> {
    let assestTypes = await this.assetTypeRepository.findAll({
      orderBy: { name: "ASC" },
    });

    const getAllAssessIds = assestTypes?.map((item) => item?.id);

    const getAllAssetTemplates = await this.entityRepository.find({
      assetType: { id: getAllAssessIds },
      $or: [
        {
          customer: null,
        },
        ...(customerId ? [{ customer: { id: customerId } }] : []),
      ],
    });

    assestTypes = assestTypes?.map((item) => {
      const findAssessmentTemplate =
        getAllAssetTemplates?.filter(
          (template) => template?.assetType.id === item?.id
        ) ?? [];
      return {
        ...item,
        asset_template_count: findAssessmentTemplate?.length ?? 0,
      };
    });

    return assestTypes;
  }

  async add(assetType: AssetType, createdById: UserId): Promise<AssetType> {
    assetType.createdBy = Reference.createFromPK(User, createdById);
    assetType.createdAt = new Date();

    await this.assetTypeRepository.persistAndFlush(assetType);

    return assetType;
  }

  async update(assetType: AssetType, updatedById: UserId): Promise<AssetType> {
    assetType.updatedAt = new Date();
    assetType.updatedBy = Reference.createFromPK(User, updatedById);
    await this.assetTypeRepository.persistAndFlush(assetType);
    return assetType;
  }

  async delete(id: number) {
    const assetType = await this.findById(id);
    return await this.assetTypeRepository.nativeDelete(assetType);
  }
}
