version: '3.0'

services:
  postgres:
    image: postgres:15.1
    environment:
      #POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - type: bind
        source: ./postgresql.conf
        target: /etc/postgresql/postgresql.conf
      # - type: bind
      #   source: ./datalogger_db
      #   target: /db_dump
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
    restart: unless-stopped
  redis:
    image: redis/redis-stack-server:latest
    ports:
      - "6379:6379"
