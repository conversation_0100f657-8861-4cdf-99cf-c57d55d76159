import {
  <PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON>ey,
  Property,
  Ref,
  Unique,
} from "@mikro-orm/core";
import { Metric } from "../../assets/domain/metric.entity";
import { DataType } from "../../measurements/domain/data-type.entity";
import { Datasource } from "../../measurements/domain/datasource.entity";
import { Location } from "../../measurements/domain/location.entity";
import { MeasurementType } from "../../measurements/domain/measurement-type.entity";
import { ValueType } from "../../measurements/domain/value-type.entity";
import { User } from "../../users/domain/user.entity";
import { AssetTemplate } from "./asset-template.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";

@Entity()
@Unique({ properties: ["assetTemplate", "metric"] })
export class AssetTemplateMeasurement {
  @PrimaryKey()
  id!: number;

  @ManyToOne({ entity: () => Metric, eager: true, fieldName: "metric" })
  metric?: Metric;

  @ManyToOne({
    entity: () => AssetTemplate,
    fieldName: "asset_template",
    onDelete: "cascade",
  })
  assetTemplate!: AssetTemplate;

  @ManyToOne({ entity: () => MeasurementType, fieldName: "m_type" })
  measurementType!: MeasurementType;

  @ManyToOne({ entity: () => DataType, fieldName: "data_type" })
  dataType!: DataType;

  @ManyToOne({ entity: () => Location, fieldName: "location", nullable: true })
  location?: Location;

  @ManyToOne({ entity: () => ValueType, fieldName: "value_type" })
  valueType!: ValueType;

  @ManyToOne({
    entity: () => Datasource,
    nullable: true,
    fieldName: "datasource",
  })
  datasource?: Datasource;

  @ManyToOne({
    entity: () => CalculationMetricInstance,
    nullable: true,
    fieldName: "calculation_metric_instance",
  })
  calculation_metric_instance?: CalculationMetricInstance;

  @Property({ length: 150, nullable: true })
  description?: string;

  @Property({ columnType: "double precision", nullable: true })
  meterFactor?: number;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "created_by",
  })
  createdBy?: Ref<User>;

  @Property({ nullable: true })
  writeback?: boolean;
}
