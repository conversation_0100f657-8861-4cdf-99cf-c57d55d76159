import { Logger, LoggerService } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import * as bodyParser from "body-parser";
import { AppModule } from "./app.module";
import { FallbackExceptionFilter } from "./lib/fallback.filter";
import { <PERSON><PERSON>ogger } from "./logger/custom_logger";

const DEVELOPMENT_ENV = "development";
async function bootstrap() {
  const appType = process.env.APP_TYPE ?? "http";
  const environment = process.env.NODE_ENV?.toLowerCase() ?? DEVELOPMENT_ENV;

  let logger: LoggerService;

  if (environment === DEVELOPMENT_ENV) {
    // Use NestJS Logger in development mode
    logger = new Logger();
    logger.log(
      `Using config file located at: ${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env with app type: ${appType}`,
      "Bootstrap"
    );
  } else {
    // Use Winston JSON logger in non-development modes
    logger = WinstonLogger;
    logger.log({
      level: "info",
      message: `Using config file located at: ${AppModule.CONFIG_DIR}/.${AppModule.ENVIRONMENT}.env with app type: ${appType}`,
    });
  }

  const app = await NestFactory.create(AppModule, {
    logger, // Dynamically set the logger
    bufferLogs: true,
  });
  app.use(bodyParser.json({ limit: "5mb" })); // Set the limit to 5MB
  app.use(bodyParser.urlencoded({ limit: "5mb", extended: true })); // Set the limit to 5MB
  app.useGlobalFilters(new FallbackExceptionFilter());
  AppModule.configure(app);

  const serverPort = process.env.SERVER_PORT ?? 3030;
  await app.listen(serverPort);
  if (environment === DEVELOPMENT_ENV) {
    logger.log(`Application is running on port ${serverPort}`, "Bootstrap");
  } else {
    logger.log({
      level: "info",
      message: `Application is running on port ${serverPort}`,
    });
  }
}

bootstrap();
