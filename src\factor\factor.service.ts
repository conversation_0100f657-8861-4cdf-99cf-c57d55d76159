import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { Injectable } from "@nestjs/common";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { User } from "src/users/domain/user.entity";
import { FactorSchedule } from "./domain/factor-schedule.entity";
import { FactorTimeOfDayValue } from "./domain/factor-time-of-day-value.entity";
import { FactorType } from "./domain/factor-type.entity";
import { TimeVaryingFactor } from "./domain/TimeVaryingFactor.entity";
import { FactorTimeOfDayValueDTO } from "./dto/factor-time-of-day-value.dto";
import { TimeVaryingFactorCreateDTO } from "./dto/time-varing-factor.dto";
import { FactorRepository } from "./repository/factor.repository";
import { FactorTypeDto } from "./dto/factor-type.dto";

@Injectable()
export class FactorService {
  // Add your service methods here

  constructor(
    @InjectRepository(FactorType)
    private readonly factorType: EntityRepository<FactorType>,
    @InjectRepository(FactorSchedule)
    private readonly factorSchedule: EntityRepository<FactorSchedule>,
    @InjectRepository(FactorTimeOfDayValue)
    private readonly factorTimeOfDayValue: EntityRepository<FactorTimeOfDayValue>,
    @InjectRepository(TimeVaryingFactor)
    private readonly timeVaryingFactor: EntityRepository<TimeVaryingFactor>,
    @InjectRepository(User)
    private readonly user: EntityRepository<User>,
    @InjectRepository(Measurement)
    private readonly measurement: EntityRepository<Measurement>,
    private readonly factorRepository: FactorRepository
  ) {}

  async getFactorTypes(): Promise<FactorType[]> {
    return this.factorRepository.getFactorTypes();
  }

  async updateTimeVaryingFactor(
    id: number,
    authUser: User,
    factoryTimeVaring: TimeVaryingFactorCreateDTO
  ) {
    return this.factorRepository.updateTimeVaryingFactor(
      id,
      authUser,
      factoryTimeVaring
    );
  }

  async getFactorTypeById(id: number): Promise<FactorType> {
    return this.factorType.findOne({ id: id });
  }
  async getTimeVaryingFactorByMeasure(measureId: number) {
    return this.factorRepository.getTimeVaryingFactorByMeasure(measureId);
  }
  async createFactorType(factorType: FactorType) {
    factorType.createdAt = new Date();
    factorType.updatedAt = new Date();
    this.factorType.persistAndFlush(factorType);
  }
  async getFactorSchedules(factorTypeId: number): Promise<FactorSchedule[]> {
    return this.factorSchedule.find({ factor: factorTypeId });
  }

  async getFactorScheduleTimeOfDay(
    schduleId: number
  ): Promise<FactorTimeOfDayValue[]> {
    return this.factorTimeOfDayValue.find({
      factorSchedule: schduleId,
    });
  }

  async createTimeVaryingFactor(factor: TimeVaryingFactor) {
    factor.createdAt = new Date();
    factor.updatedAt = new Date();
    this.timeVaryingFactor.persist(factor);
  }

  async createTimeVaryingFactorSchedule(
    schedule: TimeVaryingFactorCreateDTO,
    authUser: User
  ) {
    const createTimeVaryingFactor = new TimeVaryingFactor();
    createTimeVaryingFactor.createdAt = new Date();
    createTimeVaryingFactor.createdAt = new Date();
    createTimeVaryingFactor.updatedAt = new Date();
    createTimeVaryingFactor.createdBy = authUser;
    createTimeVaryingFactor.updatedBy = authUser;
    createTimeVaryingFactor.seasonal = schedule.timeVaryingFactor.seasonal;

    const factorType = await this.factorType.findOne({
      id: schedule.timeVaryingFactor.factorType,
    });
    if (!factorType) {
      throw new Error("Factor type not found");
    }
    createTimeVaryingFactor.factorType = factorType;
    const loadedMeasurement = await this.measurement.findOne({
      id: schedule.timeVaryingFactor.measurement,
    });
    if (!loadedMeasurement) {
      throw new Error("Measurement not found");
    }
    createTimeVaryingFactor.measurement = loadedMeasurement;

    await this.timeVaryingFactor.persistAndFlush(createTimeVaryingFactor);

    if (createTimeVaryingFactor.id) {
      const factorSchedules = await Promise.all(
        schedule.factorSchedule.map(async (schedules) => {
          const factorSchedule = new FactorSchedule();
          factorSchedule.effectiveDate = schedules.effectiveDate;
          factorSchedule.createdAt = new Date();
          factorSchedule.createdBy = authUser;
          factorSchedule.updatedAt = new Date();
          factorSchedule.updatedBy = authUser;
          factorSchedule.factor = createTimeVaryingFactor;
          await this.factorSchedule.persistAndFlush(factorSchedule);
          return {
            id: factorSchedule.id,
            effectiveDate: factorSchedule.effectiveDate,
            schedules: factorSchedule,
          };
        })
      );
      const factorTimeOfDayValueToCreate: {
        [key: string]: FactorTimeOfDayValueDTO[];
      } = schedule.factorTimeOfDayValue.reduce((acc, factorTimeOfDay) => {
        const { effectiveDate } = factorTimeOfDay;
        if (!acc[effectiveDate]) {
          acc[effectiveDate] = [];
        }
        acc[effectiveDate].push(factorTimeOfDay);
        return acc;
      }, {});
      factorSchedules.map((schedules) => {
        if (factorTimeOfDayValueToCreate[schedules.effectiveDate]) {
          factorTimeOfDayValueToCreate[schedules.effectiveDate].map(
            async (times) => {
              const time = new FactorTimeOfDayValue();
              time.createdAt = new Date();
              time.createdBy = authUser;
              time.weekday = times.weekday;
              time.factorSchedule = schedules.schedules;
              time.updatedAt = new Date();
              time.updatedBy = authUser;
              time.value = times.value;
              time.timeOfDay = times.timeOfDay;
              await this.timeVaryingFactor.persistAndFlush(time);
            }
          );
        }
      });
    }
  }
}
