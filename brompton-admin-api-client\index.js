const axios = require('axios');
const {wrapper} = require('axios-cookiejar-support');
const {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>} = require('tough-cookie');

// Base URL for the API
const baseURL = 'http://localhost:3030'; // Replace with your API host

const jar = new CookieJar();

let csrfToken;
// Function to create an Axios instance with common configurations
function createAxiosClient() {
  const client = wrapper(axios.create({
    baseURL,
    jar,
  }));

  client.interceptors.request.use((request) => {
    if (csrfToken) {
      request.headers['BE-CSRFToken'] = csrfToken;
    }
    return request;
  });

  return client;
}

// Function to perform the POST request to /v0/sessions
async function login(client) {
  try {
    const response = await client.post('/v0/sessions', {
      username: 'test',
      password: 'asdfasdf',
    });

    csrfToken = response.data.csrf_token;

    const cookies = response.headers['set-cookie'];
    cookies.forEach((cookieString) => {
      const cookie = Cookie.parse(cookieString);
      jar.setCookieSync(cookie, baseURL); // Add each cookie to the cookie jar
    });
  } catch (error) {
    console.error('Error during login:', error.message);
    throw error;
  }
}

// Function to perform the GET request to /v0/users/me with stored tokens
async function getUserInfo(client) {
  try {
    const response = await client.get('/v0/users/me');


    console.log('User Info:', response.data);
  } catch (error) {
    console.error('Error getting user info:', error.message);
    throw error;
  }
}

// Main execution
(async () => {
  try {
    // Step 1: Create an Axios instance
    const httpClient = createAxiosClient();

    // Step 2: Login
    await login(httpClient);

    // Step 3: Use the logged client
    await getUserInfo(httpClient);
  } catch (error) {
    console.error('An error occurred:', error.message);
  }
})();
