import { Migration } from '@mikro-orm/migrations';

export class Migration20240409061141 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "user_preferences" ("id" serial primary key, "user_id" int not null, "prefer_key" varchar(50) not null, "prefer_value" varchar(255) not null, "created_at" timestamptz(6) not null, "updated_at" timestamptz(6) not null);');
    this.addSql('alter table "user_preferences" add constraint "user_preferences_unique" unique ("prefer_key", "user_id");');

    this.addSql('alter table "user_preferences" add constraint "user_preferences_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete cascade;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "user_preferences" cascade;');
  }

}
