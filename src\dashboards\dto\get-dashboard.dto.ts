import { ApiProperty } from '@nestjs/swagger';

export class GetDashboardByIdSuccessResDto {
  @ApiProperty({ required: true, example: 1 })
  id: number;

  @ApiProperty({ required: true, example: "dashboard-123" })
  title: string;

  @ApiProperty({ required: true, example: "{apple:ioioiio}" })
  data: string;

  @ApiProperty({ required: true, example: true })
  default: boolean;
}

export class GetDashboardByIdNotFoundResDto {
  @ApiProperty({ required: true, example: 404 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Dashboard Not Found.' })
  message: string;

  @ApiProperty({ required: true, example: 'Not Found' })
  error: string;
}

export class GetDashboardByIdForbiddenResDto {
  @ApiProperty({ required: true, example: 403 })
  statusCode: number;

  @ApiProperty({ required: true, example: 'Forbidden' })
  error: string;
}