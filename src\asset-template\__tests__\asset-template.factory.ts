import { AssetType } from 'src/assets/domain/asset-type.entity';
import { AssetTemplate } from '../domain/asset-template.entity';
import { MeasurementType } from 'src/measurements/domain/measurement-type.entity';
import { Metric } from 'src/assets/domain/metric.entity';
import { DataType } from 'src/measurements/domain/data-type.entity';
import { ValueType } from 'src/measurements/domain/value-type.entity';

export const assetTemplateFactory = {
  createWaterPumpTemplate: (assetTemplateId: number): AssetTemplate => {
    const pumpAssetType = new AssetType({ name: 'Pump' });
    const powerMeasurementType = new MeasurementType();
    powerMeasurementType.id = 34;

    const powerPumpMetric = new Metric({
      name: 'Pump/Power',
      assetType: pumpAssetType,
    });
    powerPumpMetric.id = 4;

    const realDataType = new DataType();
    realDataType.id = 5;

    const nominalValueType = new ValueType();
    nominalValueType.id = 6;

    const waterPumpTemplate = new AssetTemplate({
      manufacturer: 'DXH',
      modelNumber: 'GH-3',
      assetType: pumpAssetType,
      measurements: [
        {
          measurementType: powerMeasurementType,
          metric: powerPumpMetric,
          dataType: realDataType,
          valueType: nominalValueType,
        },
      ],
    });
    waterPumpTemplate.id = assetTemplateId;
    return waterPumpTemplate;
  },
};
