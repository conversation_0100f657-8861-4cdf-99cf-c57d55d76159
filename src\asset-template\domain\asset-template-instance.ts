import { Asset } from 'src/assets/domain/asset.entity';
import { AssetMeasurement } from 'src/measurements/domain/asset-measurement.entity';
import { AssetMeasurementCreationData } from 'src/measurements/domain/types';

export type UpdateRequiredAndNonNull<T, K extends keyof T> = T & {
  [P in K]-?: NonNullable<T[P]>;
};

export type AssetTemplateMeasurementInstance = UpdateRequiredAndNonNull<
  AssetMeasurement,
  'metricId'
>;

export type AssetTemplateInstance = {
  unitsGroupId: number;
  asset: Asset;
  measurements: AssetTemplateMeasurementInstance[];
};

export type AssetMeasurementOverride = Omit<
  AssetMeasurementCreationData,
  'typeId' | 'dataTypeId' | 'valueTypeId' | 'assetId' | 'tag'
> &
  Partial<Pick<AssetMeasurementCreationData, 'tag'>>;
