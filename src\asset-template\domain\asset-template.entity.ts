import {
  Collection,
  <PERSON>tity,
  ManyToOne,
  OneToMany,
  PrimaryKey,
  Property,
  Ref,
  Unique,
} from "@mikro-orm/core";
import { Metric } from "src/assets/domain/metric.entity";
import { Customer } from "src/customers/domain/customer.entity";
import { AssetType } from "../../assets/domain/asset-type.entity";
import { User } from "../../users/domain/user.entity";
import { AssetTemplateMeasurement } from "./asset-template-measurement.entity";

export type AssetTemplateId = number;

export type AssetTemplateCreationParams = Omit<
  AssetTemplate,
  "id" | "createdBy" | "createdAt" | "assetTypeId" | "measurements"
> & {
  measurements: Array<
    Omit<AssetTemplateMeasurement, "id" | "assetTemplate" | "metric"> & {
      metric: Metric;
    }
  >;
};

@Entity()
@Unique({ properties: ["manufacturer", "modelNumber"] })
export class AssetTemplate {
  constructor(assetTemplateCreationParams: AssetTemplateCreationParams) {
    this.assetType = assetTemplateCreationParams.assetType;
    this.manufacturer = assetTemplateCreationParams.manufacturer;
    this.modelNumber = assetTemplateCreationParams.modelNumber;

    assetTemplateCreationParams.measurements.map((measurementCreationData) => {
      const templateMeasurement = new AssetTemplateMeasurement();

      templateMeasurement.measurementType =
        measurementCreationData.measurementType;
      templateMeasurement.dataType = measurementCreationData.dataType;
      templateMeasurement.valueType = measurementCreationData.valueType;
      templateMeasurement.datasource = measurementCreationData.datasource;
      templateMeasurement.description = measurementCreationData.description;
      templateMeasurement.location = measurementCreationData.location;
      templateMeasurement.meterFactor = measurementCreationData.meterFactor;
      templateMeasurement.metric = measurementCreationData.metric;

      this.measurements.add(templateMeasurement);
    });
  }

  @PrimaryKey()
  id!: AssetTemplateId;

  @Property({ length: 50 })
  manufacturer!: string;

  @Property({ length: 50, fieldName: "model_no" })
  modelNumber!: string;

  @ManyToOne({ entity: () => AssetType, fieldName: "asset_type" })
  assetType!: AssetType;

  @ManyToOne({ entity: () => Customer, fieldName: "customer", nullable: true })
  customer?: Customer;

  @OneToMany(
    () => AssetTemplateMeasurement,
    (assetTemplateMeasurement) => assetTemplateMeasurement.assetTemplate,
    {
      eager: true,
      persist: false,
    }
  )
  measurements = new Collection<AssetTemplateMeasurement>(this);

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "created_by",
  })
  createdBy?: Ref<User>;
}
