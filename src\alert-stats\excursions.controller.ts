import { Controller, Get, Query } from "@nestjs/common";
import { AlertStatsService } from "./alert-stats.service";

@Controller({
  path: "excursions",
  version: "0",
})
export class ExcursionController {
  constructor(private readonly alertsStatsService: AlertStatsService) {}

  @Get("/")
  async getAlertExcursion(
    @Query("interval") interval: "daily" | "weekly" | "monthly",
    @Query("assetId") assetId?: number,
    @Query("measureId") measureId?: number,
    @Query("date") date?: string
  ) {
    return await this.alertsStatsService.getGroupedStats(
      interval,
      assetId,
      measureId,
      date
    );
  }
}
