import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { ScopedRole, UserFactory } from '../domain/user.entity';

const commonUserData = {
  password: 'somepass',
  email: '<EMAIL>',
  firstName: 'f',
  lastName: 'l',
};
export const testUserFactory = {
  createGlobalScopeUser: (username: string, globalRole: Role) => {
    const user = UserFactory.create({
      ...commonUserData,
      username,
      globalRole,
    });
    return user;
  },

  createCustomerScopedUser: (username: string, scopedRoles: ScopedRole[]) => {
    const user = UserFactory.create({
      ...commonUserData,
      username,
      scopedRoles,
    });
    return user;
  },
};
