import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module, forwardRef } from '@nestjs/common';
import { AuthModule } from 'src/authentication/auth.module';
import { CustomersApiController } from './customers.controller';
import { CustomerService } from './customer.service';
import { Customer } from './domain/customer.entity';

@Module({
  imports: [
    MikroOrmModule.forFeature([Customer]),
    forwardRef(() => AuthModule),
  ],
  providers: [CustomerService],
  controllers: [CustomersApiController],
  exports: [CustomerService],
})
export class CustomersModule {}
