import {
  <PERSON>tity,
  ManyToOne,
  OptionalProps,
  PrimaryKey,
  Property,
  Unique,
} from "@mikro-orm/core";
import { FactorSchedule } from "./factor-schedule.entity";
import { User } from "../../users/domain/user.entity";

@Entity()
@Unique({
  name: "factor_time_of_day_value_unique",
  properties: ["factorSchedule", "timeOfDay", "weekday"],
})
export class FactorTimeOfDayValue {
  [OptionalProps]?: "timeOfDay" | "value" | "weekday";

  @PrimaryKey()
  id!: number;

  @ManyToOne({
    entity: () => FactorSchedule,
    fieldName: "factor_schedule",
    nullable: true,
  })
  factorSchedule?: FactorSchedule;

  @Property({ columnType: "time(0)", default: "00:00:00" })
  timeOfDay!: string;

  @Property({ default: 0 })
  weekday: number = 0;

  @Property({ columnType: "double precision", defaultRaw: `0.0` })
  value!: string;

  @Property({ length: 6, nullable: true })
  createdAt?: Date;

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @Property({ length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne({
    entity: () => User,
    fieldName: "created_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  createdBy?: User;

  @ManyToOne({
    entity: () => User,
    fieldName: "updated_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  updatedBy?: User;

  @ManyToOne({
    entity: () => User,
    fieldName: "deleted_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  deletedBy?: User;
}
