import { ApiProperty, ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { AssetType } from "../domain/asset-type.entity";

export class AssetTypeDto implements Pick<AssetType, "id" | "name"> {
  @ApiProperty()
  id!: number;

  @ApiProperty()
  name!: string;

  @ApiProperty({ nullable: true, type: Number })
  parent_type_id!: number | null;

  @ApiPropertyOptional()
  asset_template_count?: number;
}

export class AssetTypeCreationDto extends OmitType(AssetTypeDto, [
  "id",
  "parent_type_id",
]) {
  @ApiPropertyOptional()
  parent_type_id?: number;
}

export class AssetTypeEditDto extends OmitType(AssetTypeDto, [
  "parent_type_id",
]) {
  @ApiPropertyOptional()
  parent_type_id?: number;
}
