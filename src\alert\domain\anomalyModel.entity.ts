import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON>, Property } from "@mikro-orm/core";
import { Alerts } from "./alert.entity";

@Entity({ tableName: "anomaly_model" })
export class AnomalyModel {
  @PrimaryKey()
  id!: number;

  @OneToOne(() => Alerts, (alert) => alert.anomalyModel, {
    fieldName: "alert",
    nullable: false,
    owner: true,
  })
  alert!: Alerts;

  @Property({ fieldName: "model", type: "integer", nullable: true })
  model?: number | null;

  @Property({ fieldName: "anomaly_measure", type: "bigint", nullable: false })
  anomalyMeasurement!: number;

  @Property({
    fieldName: "created_at",
    type: "timestamptz",
    nullable: false,
    defaultRaw: "now()",
  })
  createdAt!: Date;

  @Property({
    fieldName: "updated_at",
    type: "timestamptz",
    nullable: false,
    defaultRaw: "now()",
  })
  updatedAt!: Date;

  @Property({ fieldName: "created_by", type: "integer", nullable: true })
  createdBy?: number | null;

  @Property({ fieldName: "updated_by", type: "integer", nullable: true })
  updatedBy?: number | null;
}
