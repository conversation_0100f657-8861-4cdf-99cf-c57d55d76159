import {
  Body,
  Controller,
  Get,
  Param,
  <PERSON>,
  Post,
  Query,
} from "@nestjs/common";
import { AnnotationsService } from "./annotations.service";

@Controller({
  path: "annotations",
  version: "0",
})
export class AnnotationsController {
  constructor(private readonly annotationService: AnnotationsService) {}

  @Get("/:dashboardId/:widgetId/:measureId")
  async getAnnotation(
    @Param("dashboardId") dashboardId: number,
    @Param("widgetId") widgetId: number,
    @Param("measureId") measureId: number,
    @Query("startTime") startTime: number,
    @Query("endTime") endTime: number
  ) {
    const annotations = await this.annotationService.getAnnotation({
      measureId,
      dashboard_id: dashboardId,
      widget_id: widgetId,
      startTime: startTime,
      endTime: endTime,
    });

    return {
      total: annotations.length,
      items: annotations,
    };
  }

  @Post("/")
  async createAnnotation(
    @Body()
    annotation: {
      dashboard: number;
      measurement_id: number;
      widget_id: number;
      description: string;
      time_of_annotation: number;
      value: number;
      settings: string;
    }
  ) {
    return await this.annotationService.createAnnotation({
      ...annotation,
      dashboard_id: annotation.dashboard,
    });
  }

  @Patch("/:annotationId")
  async updateAnnotation(
    @Param("annotationId") annotationId: number,
    @Body()
    annotation: {
      dashboard: number;
      measurement_id: number;
      widget_id: number;
      description: string;
      time_of_annotation: number;
      value: number;
      settings: string;
    }
  ) {
    return this.annotationService.updateAnnotation({
      ...annotation,
      id: annotationId,
      dashboard_id: annotation.dashboard,
    });
  }
}
