import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { Request, Response } from "express";
import { ExceptionMessages } from "src/errors/exceptions.contants";

@Catch()
export class FallbackExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger("API");

  catch(exception: any, host: ArgumentsHost) {
    // Get the exception's status code or default to 500
    const statusCode = exception.getStatus
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Safely access exception.response properties
    const errorResponse = exception.response || {};

    // Lookup the predefined message for the status code
    const predefinedError = Object.values(ExceptionMessages).find(
      (e) => e.statusCode === statusCode
    );

    // Use the predefined message if available, otherwise fallback
    const errorMessage =
      predefinedError?.message ||
      errorResponse.message ||
      exception.message ||
      "Unexpected error occurred";

    // Log the error
    this.logger.error(
      `${request.method} ${request.url} ${statusCode} \n ${JSON.stringify(
        exception
      )}`
    );

    // Return the response
    return response.status(statusCode).json({
      statusCode,
      createdBy: "Fallback Exception",
      message: errorMessage,
      exception: exception.message || exception.name || "Exception occurred",
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
