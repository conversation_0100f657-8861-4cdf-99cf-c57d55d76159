import { MikroORM, RequestContext } from '@mikro-orm/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from 'src/app.module';
import { DEFAULT_CSRF_HEADER } from 'src/authentication/auth.config';
import { UnitOfMeasure } from 'src/measurements/domain/unit-of-measure.entity';
import { REDIS_CLIENT } from 'src/redis/redis.constants';
import request from 'supertest';
import { InfoReply } from '@redis/time-series/dist/commands/INFO';
import { transformReply } from '@redis/client/dist/lib/commands/XINFO_STREAM';
import { RedisClientType } from 'redis';

type XInfoStreamReply = ReturnType<typeof transformReply>;

export async function setupApp() {
  const testingModule: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  })
    // .setLogger(new Logger())
    .compile();

  const app = testingModule.createNestApplication();

  AppModule.configure(app);

  await app.init();

  return { testingModule, app };
}

export async function loginUser(
  httpServer: any,
  username: string,
  password: string,
) {
  const httpClient = request.agent(httpServer);

  const loginResponse = await httpClient
    .post('/v0/sessions')
    .send({ username, password });
  httpClient.set(DEFAULT_CSRF_HEADER, loginResponse.body['csrf_token']);

  return httpClient;
}

export async function createCustomerWithAPI(
  httpClient: request.SuperAgentTest,
  name: string,
) {
  return await httpClient.post('/v0/customers').send({
    name_id: name.toLowerCase(),
    name: name,
    address: 'Somewhere in japan',
  });
}

export const createUnitOfMeasure = async (
  testingModule: TestingModule,
  name: string,
  measurementTypeId: number,
) => {
  const orm = await testingModule.resolve(MikroORM);
  const unitOfMeasureTypeId = await RequestContext.createAsync(
    orm.em,
    async () => {
      const em = RequestContext.getEntityManager();
      if (!em) {
        throw new Error('No entity manager found');
      }

      const newUnitOfMeasureType = em.create(UnitOfMeasure, {
        name,
        measurementType: measurementTypeId,
      });
      await em.persistAndFlush(newUnitOfMeasureType);
      return newUnitOfMeasureType.id;
    },
  );
  return unitOfMeasureTypeId;
};

export const deleteUnitOfMeasureByName = async (
  testingModule: TestingModule,
  name: string,
) => {
  const orm = await testingModule.resolve(MikroORM);
  await RequestContext.createAsync(orm.em, async () => {
    await RequestContext.getEntityManager()?.nativeDelete(UnitOfMeasure, {
      name,
    });
  });
};

export async function getTimeSeriesById(
  testingModule: TestingModule,
  key: string,
): Promise<InfoReply> {
  const redisClient: RedisClientType = await testingModule.resolve(
    REDIS_CLIENT,
  );
  return await redisClient.ts.info(key);
}

export async function getStreamByKey(
  testingModule: TestingModule,
  key: string,
): Promise<XInfoStreamReply> {
  const redisClient: RedisClientType = await testingModule.resolve(
    REDIS_CLIENT,
  );
  return await redisClient.xInfoStream(key);
}

export async function deleteRedisKey(testingModule: TestingModule, id: number) {
  const redisClient: RedisClientType = await testingModule.resolve(
    REDIS_CLIENT,
  );
  await redisClient.del(id.toString());
}

export async function flushRedis(testingModule: TestingModule) {
  const redisClient: RedisClientType = await testingModule.resolve(
    REDIS_CLIENT,
  );
  await redisClient.flushAll();
}
