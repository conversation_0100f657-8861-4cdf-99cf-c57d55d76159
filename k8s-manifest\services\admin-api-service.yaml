apiVersion: v1
kind: Service
metadata:
  name: admin-api-${ENVIRONMENT}
  namespace: application
  labels:
    app.kubernetes.io/instance: admin-api-${ENVIRONMENT}
    app.kubernetes.io/name: admin-api-${ENVIRONMENT}
spec:
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 8081
    - name: https
      protocol: TCP
      port: 443
      targetPort: 8081
  selector:
    app.kubernetes.io/instance: admin-api-${ENVIRONMENT}
    app.kubernetes.io/name: admin-api-${ENVIRONMENT}
  type: ClusterIP