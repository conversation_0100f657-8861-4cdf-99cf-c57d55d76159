import { Migration } from '@mikro-orm/migrations';

export class Migration20240103193735 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" drop constraint "asset_template_measurement_unit_of_measure_foreign";',
    );

    this.addSql(
      'alter table "asset_template_measurement" drop column "unit_of_measure";',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template_measurement" add column "unit_of_measure" int null;',
    );
    this.addSql(
      'alter table "asset_template_measurement" add constraint "asset_template_measurement_unit_of_measure_foreign" foreign key ("unit_of_measure") references "unit_of_measure" ("id") on update cascade on delete set null;',
    );
  }
}
