import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON>, Property } from "@mikro-orm/core";
import { User } from "../../users/domain/user.entity";

@Entity()
export class FactorType {
  @PrimaryKey()
  id!: number;

  @Property({ length: 50 })
  name!: string;

  @Property({ length: 6, nullable: true })
  createdAt?: Date;

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @ManyToOne({
    entity: () => User,
    fieldName: "created_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  createdBy?: User;

  @ManyToOne({
    entity: () => User,
    fieldName: "updated_by",
    onUpdateIntegrity: "cascade",
    onDelete: "set null",
    nullable: true,
  })
  updatedBy?: User;
}
