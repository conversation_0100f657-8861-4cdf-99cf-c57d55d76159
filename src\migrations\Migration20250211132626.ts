import { Migration } from "@mikro-orm/migrations";

export class Migration20250211132626 extends Migration {
  async up(): Promise<void> {
    // Drop tables if they already exist before recreating them
    this.addSql('DROP TABLE IF EXISTS "anomaly_model" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "anomaly_parameter" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "anomaly_timeseries_audit" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "model" CASCADE;');

    this.addSql(`
      CREATE TABLE "anomaly_model" (
        "id" SERIAL PRIMARY KEY,
        "alert" INTEGER NOT NULL,
        "model" INTEGER,
        "anomaly_measure" BIGINT,
        "created_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "updated_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "created_by" INTEGER,
        "updated_by" INTEGER,
        CONSTRAINT "fk_anomaly_model_alert" FOREIGN KEY ("alert") REFERENCES "alerts"("id") ON DELETE CASCADE
      );
    `);

    this.addSql(`
      CREATE TABLE "anomaly_parameter" (
        "alert" INTEGER PRIMARY KEY,
        "learning_period" INTERVAL NOT NULL,
        "include_velocity" BOOLEAN NOT NULL,
        "include_momentum" BOOLEAN NOT NULL,
        "created_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "updated_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "created_by" INTEGER,
        "updated_by" INTEGER,
        CONSTRAINT "fk_anomaly_parameter_alert" FOREIGN KEY ("alert") REFERENCES "alerts"("id") ON DELETE CASCADE
      );
    `);

    this.addSql(`
      CREATE TABLE "anomaly_timeseries_audit" (
        "id" SERIAL PRIMARY KEY,
        "anomaly_measure" INTEGER NOT NULL,
        "created_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "updated_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "created_by" INTEGER,
        "updated_by" INTEGER,
        CONSTRAINT "fk_anomaly_timeseries_audit_measure" FOREIGN KEY ("anomaly_measure") REFERENCES "anomaly_model"("id") ON DELETE CASCADE
      );
    `);

    this.addSql(`
      CREATE TABLE "model" (
        "id" SERIAL PRIMARY KEY,
        "binary_model" BYTEA,
        "created_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "updated_at" TIMESTAMP(6) WITH TIME ZONE DEFAULT now(),
        "created_by" INTEGER,
        "updated_by" INTEGER
      );
    `);
  }

  async down(): Promise<void> {
    // Drop tables when rolling back the migration
    this.addSql('DROP TABLE IF EXISTS "anomaly_model" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "anomaly_parameter" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "anomaly_timeseries_audit" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "model" CASCADE;');
  }
}
