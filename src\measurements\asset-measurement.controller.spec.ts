import { User } from 'src/users/domain/user.entity';
import { AssetMeasurementApiController } from './asset-measurement.controller';
import { AssetMeasurementService } from './asset-measurement.service';
import { Test } from '@nestjs/testing';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import config from 'src/mikro-orm.config';
import { CsrfGuard } from 'src/authentication/infra/csrf.guard';
import { testUserFactory } from 'src/users/__tests__/factories';
import { Role } from 'src/authorization/domain/customer-user-role.entity';

describe('AssetMeasurementApiController', () => {
  let assetMeasurementsApiController: AssetMeasurementApiController;
  let assetMeasurementServiceMock: jest.Mocked<
    Pick<
      AssetMeasurementService,
      'create' | 'removeById' | 'getAll' | 'getById' | 'update'
    >
  >;
  let globalAdmin: User;
  let globalUser: User;
  let scopedAdmin: User;
  const newTemperatureMeasurementParams = {
    tag: 'Office/Temp',
    type_id: 0,
    data_type_id: 0,
    value_type_id: 0,
    meter_factor: 1,
  };

  beforeEach(async () => {
    assetMeasurementServiceMock = {
      create: jest.fn(),
      removeById: jest.fn(),
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getAll: jest.fn(async (customerId, assetId) => []),
      getById: jest.fn(),
      update: jest.fn(),
    };
    const moduleRef = await Test.createTestingModule({
      imports: [
        MikroOrmModule.forRoot({
          ...config,
          allowGlobalContext: true,
          connect: false,
          dbName: 'test',
        }),
      ],
      providers: [
        {
          provide: AssetMeasurementService,
          useValue: assetMeasurementServiceMock,
        },
      ],
      controllers: [AssetMeasurementApiController],
    })
      .overrideGuard(CsrfGuard)
      .useValue(jest.fn())
      .compile();

    globalAdmin = testUserFactory.createGlobalScopeUser(
      'global_admin',
      Role.ADMIN,
    );
    globalUser = testUserFactory.createGlobalScopeUser(
      'global_user',
      Role.USER,
    );
    scopedAdmin = testUserFactory.createCustomerScopedUser('scoped_admin', [
      { role: Role.ADMIN, customerIds: [4] },
    ]);

    assetMeasurementsApiController = moduleRef.get(
      AssetMeasurementApiController,
    );
  });

  describe('create', () => {
    test('global admin should be able to create any asset measurement', async () => {
      const globalAdmin = testUserFactory.createGlobalScopeUser(
        'global_admin',
        Role.ADMIN,
      );

      await assetMeasurementsApiController.create(
        globalAdmin,
        '42',
        '34',
        newTemperatureMeasurementParams,
      );

      expect(assetMeasurementServiceMock.create.mock.calls.length).toBe(1);
    });

    test('scoped admin should not be able to create asset measurement on customer outside scope', () => {
      expect(() =>
        assetMeasurementsApiController.create(
          scopedAdmin,
          '42',
          '34',
          newTemperatureMeasurementParams,
        ),
      ).rejects.toThrow('Forbidden');
    });

    test('scoped admin should be able to create asset measurement on customer within scope', async () => {
      await assetMeasurementsApiController.create(
        scopedAdmin,
        '4',
        '34',
        newTemperatureMeasurementParams,
      );

      expect(assetMeasurementServiceMock.create.mock.calls.length).toBe(1);
    });
  });

  describe('removeById', () => {
    test('global admin should be able to remove any asset measurement', async () => {
      await assetMeasurementsApiController.removeById(
        globalAdmin,
        '42',
        '34',
        '2',
      );

      expect(assetMeasurementServiceMock.removeById.mock.calls.length).toBe(1);
    });

    test('scoped admin should not be able to remove asset measurement on customer outside scope', () => {
      expect(() =>
        assetMeasurementsApiController.removeById(scopedAdmin, '42', '34', '2'),
      ).rejects.toThrow('Forbidden');
    });

    test('scoped admin should be able to remove asset measurement on customer within scope', async () => {
      await assetMeasurementsApiController.removeById(
        scopedAdmin,
        '4',
        '34',
        '2',
      );

      expect(assetMeasurementServiceMock.removeById.mock.calls.length).toBe(1);
    });
  });

  describe('getAll', () => {
    test('global admin should get all', async () => {
      await assetMeasurementsApiController.getAll(globalAdmin, '42', '34');

      expect(assetMeasurementServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('global user should get all customers', async () => {
      await assetMeasurementsApiController.getAll(globalUser, '42', '34');

      expect(assetMeasurementServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('scoped admin should get all if in scope', async () => {
      await assetMeasurementsApiController.getAll(scopedAdmin, '4', '34');

      expect(assetMeasurementServiceMock.getAll.mock.calls.length).toBe(1);
    });

    test('scoped admin should get an exception if not in scope', () => {
      expect(() =>
        assetMeasurementsApiController.getAll(scopedAdmin, '42', '34'),
      ).rejects.toThrow('Forbidden');
    });
  });

  describe('getById', () => {
    test('global admin should get any asset measurement', async () => {
      await assetMeasurementsApiController.getById(
        globalAdmin,
        '42',
        '34',
        '2',
      );

      expect(assetMeasurementServiceMock.getById.mock.calls.length).toBe(1);
    });

    test('global user should get any asset measurement', async () => {
      await assetMeasurementsApiController.getById(
        globalAdmin,
        '42',
        '34',
        '2',
      );

      expect(assetMeasurementServiceMock.getById.mock.calls.length).toBe(1);
    });

    test('scoped admin should get if in scope', async () => {
      await assetMeasurementsApiController.getById(scopedAdmin, '4', '34', '2');

      expect(assetMeasurementServiceMock.getById.mock.calls.length).toBe(1);
    });

    test('scoped admin should get an exception if not in scope', () => {
      expect(() =>
        assetMeasurementsApiController.getById(scopedAdmin, '42', '34', '2'),
      ).rejects.toThrow('Forbidden');
    });
  });

  describe('patchById', () => {
    test('global admin should be able to patch any asset measurement', async () => {
      await assetMeasurementsApiController.patchById(
        globalAdmin,
        '42',
        '34',
        '2',
        { description: 'new description' },
      );

      expect(assetMeasurementServiceMock.update.mock.calls.length).toBe(1);
    });

    test('scoped admin should get an exception if not in scope', () => {
      expect(() =>
        assetMeasurementsApiController.patchById(scopedAdmin, '42', '34', '2', {
          description: 'new description',
        }),
      ).rejects.toThrow('Forbidden');
    });
  });
});
