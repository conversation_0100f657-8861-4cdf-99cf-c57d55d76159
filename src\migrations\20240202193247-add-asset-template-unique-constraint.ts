import { Migration } from '@mikro-orm/migrations';

export class Migration20240202193247 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table "asset_template" add constraint "asset_template_manufacturer_model_no_unique" unique ("manufacturer", "model_no");',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "asset_template" drop constraint "asset_template_manufacturer_model_no_unique";',
    );
  }
}
