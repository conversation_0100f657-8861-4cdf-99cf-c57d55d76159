import { Controller, Get, Param } from "@nestjs/common";
import { AlertStatsService } from "./alert-stats.service";

@Controller({
  path: "alert-stats",
  version: "0",
})
export class AlertStatsController {
  constructor(private readonly alertsStatsService: AlertStatsService) {}

  @Get("/excursions")
  async getAllExcursionStats() {
    const excursionStats = await this.alertsStatsService.getAllExcursionStats();
    return {
      total: excursionStats.length,
      items: excursionStats,
    };
  }

  @Get("/:alert_id")
  async getAlertStatsById(@Param("alert_id") alert_id: number) {
    const alert_stats = await this.alertsStatsService.getAlertStatsById({
      alert_id,
    });
    return {
      total: alert_stats.length,
      items: alert_stats,
    };
  }
}
