import { Knex } from '@mikro-orm/postgresql';
import { setupMigrationTest } from './migration-testing-utils';

describe('Units group base migration', () => {
  let knex: Knex;
  let usUnitsGroupId: number;
  let baseUnitsGroupId: number;

  beforeAll(async () => {
    const { migrator, connection } = await setupMigrationTest();

    await migrator.up({
      to: '20240212151551-remove-asset-template-measurement-tag',
    });

    knex = connection.getKnex();

    const unitsGroupsCreation = await knex
      .insert([
        {
          name: 'us',
          is_base: false,
        },
        {
          name: 'base',
          is_base: false,
        },
      ])
      .returning('id')
      .into('units_group');
    usUnitsGroupId = unitsGroupsCreation[0].id;
    baseUnitsGroupId = unitsGroupsCreation[1].id;

    await migrator.up({
      to: '20240214151551-update-units-group-base-group',
    });
  });

  test('base group should be marked as base', async () => {
    const res = await knex()
      .select('is_base')
      .first()
      .from('units_group')
      .where('id', baseUnitsGroupId);

    expect(res.is_base).toBeTruthy();
  });

  test('us group should not be marked as base', async () => {
    const res = await knex()
      .select('is_base')
      .first()
      .from('units_group')
      .where('id', usUnitsGroupId);

    expect(res.is_base).toBeFalsy();
  });
});
