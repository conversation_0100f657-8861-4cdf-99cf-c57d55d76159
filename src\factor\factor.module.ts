import { MikroOrmModule } from "@mikro-orm/nestjs";
import { Module, forwardRef } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { CustomersModule } from "src/customers/customers.module";
import { SecurityModule } from "src/security/security.module";
import { User } from "src/users/domain/user.entity";
import { UserPreferences } from "src/users/domain/user_preferences.entity";
import { FactorSchedule } from "./domain/factor-schedule.entity";
import { FactorTimeOfDayValue } from "./domain/factor-time-of-day-value.entity";
import { FactorType } from "./domain/factor-type.entity";
import { TimeVaryingFactor } from "./domain/TimeVaryingFactor.entity";
import { FactorApiController } from "./factor.controller";
import { FactorService } from "./factor.service";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { FactorRepository } from "./repository/factor.repository";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      FactorType,
      FactorSchedule,
      FactorTimeOfDayValue,
      User,
      UserPreferences,
      TimeVaryingFactor,
      Measurement,
    ]),
    SecurityModule,
    CustomersModule,
    forwardRef(() => AuthModule),
  ],
  controllers: [FactorApiController],
  providers: [FactorService, FactorRepository],
  exports: [FactorService],
})
export class FactorModule {}
