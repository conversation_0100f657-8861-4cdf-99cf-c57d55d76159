import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsDefined,
  IsNotEmpty,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";

export enum PreferenceKeys {
  DATE_FORMAT = "DATE_FORMAT",
  CURRENCY = "CURRENCY",
  DEFAULT_CUSTOMER = "DEFAULT_CUSTOMER",
  THOUSAND_SEPARATOR = "THOUSAND_SEPARATOR",
}

class UserPreferences {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty()
  DATE_FORMAT: String;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty()
  CURRENCY: String;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty()
  DEFAULT_CUSTOMER: String;
}

@ValidatorConstraint({ name: "isValidKey<PERSON>", async: false })
export class IsValidKeys implements ValidatorConstraintInterface {
  validate(preferences: UserPreferences, args: ValidationArguments) {
    const allowedKeys = Object.values(PreferenceKeys);
    for (const key of Object.keys(preferences)) {
      if (!allowedKeys.includes(key as PreferenceKeys)) {
        return false;
      }
    }
    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return `Invalid preference key in ${args.property}`;
  }
}

function convertArrayToObject(array) {
  let obj = {};
  array.forEach((str) => {
    obj[str] = "";
  });
  return obj;
}

export class UserPreferencesDto {
  @ValidateNested()
  @Type(() => UserPreferences)
  @Validate(IsValidKeys, { message: "Invalid preference key" })
  @IsDefined({ message: "At least one preference must be provided" })
  @ApiProperty({
    required: true,
    example: JSON.stringify(
      convertArrayToObject(Object.values(PreferenceKeys))
    ),
  })
  preferences: Object;
}
