import { NewDbMigration } from '../db/ConditionalMigration';

export class Migration20230414203838 extends NewDbMigration {
  async conditionalUp(): Promise<void> {
    this.addSql(
      'create table "data_type" ("id" serial primary key, "name" varchar(50) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "data_type" add constraint "data_type_name_key" unique ("name");',
    );

    this.addSql(
      'create table "measurement_type" ("id" serial primary key, "name" varchar(50) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "measurement_type" add constraint "measurement_type_name_key" unique ("name");',
    );

    this.addSql(
      'create table "value_type" ("id" serial primary key, "name" varchar(50) not null, "created" timestamptz(6) null, "updated" timestamptz(6) null, "createdby" int null, "updatedby" int null);',
    );
    this.addSql(
      'alter table "value_type" add constraint "value_type_name_key" unique ("name");',
    );
  }

  async conditionalDown(): Promise<void> {
    this.addSql('drop table if exists "data_type" cascade;');

    this.addSql('drop table if exists "measurement_type" cascade;');

    this.addSql('drop table if exists "value_type" cascade;');
  }
}
