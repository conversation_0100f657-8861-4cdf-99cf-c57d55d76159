import { Migration } from "@mikro-orm/migrations";

export class Migration20250203114838_createExcursion extends Migration {
  async up(): Promise<void> {
    // Create excursion_stats table
    this.addSql(`
      CREATE TABLE "excursion_stats" (
        "id" SERIAL PRIMARY KEY,
        "alert_id" INT NOT NULL,
        "start_time" TIMESTAMP NOT NULL,
        "end_time" TIMESTAMP NOT NULL,
        "duration" VARCHAR(255) NOT NULL,
        "max_value" DOUBLE PRECISION,
        "min_value" DOUBLE PRECISION,
        "avg_value" DOUBLE PRECISION,
        "time_duration" INTERVAL,
        CONSTRAINT "fk_excursion_alert" FOREIGN KEY ("alert_id") REFERENCES "alerts"("id") ON DELETE CASCADE
      );
    `);

    // Alter column value type in annotation table
    this.addSql(
      'ALTER TABLE "annotation" ALTER COLUMN "value" TYPE DOUBLE PRECISION USING ("value"::double precision);'
    );
  }

  async down(): Promise<void> {
    this.addSql('DROP TABLE IF EXISTS "excursion_stats" CASCADE;');
    this.addSql(
      'alter table "annotation" alter column "value" type numeric(10,0) using ("value"::numeric(10,0));'
    );
  }
}
