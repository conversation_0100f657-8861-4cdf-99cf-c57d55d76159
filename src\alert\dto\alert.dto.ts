import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsOptional } from "class-validator";
import { Alerts } from "../domain/alert.entity";

export class AlertDTO implements Pick<Alerts, "id"> {
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsNumber()
  measurement: number;

  @ApiProperty()
  @IsNumber()
  asset: number;

  @ApiProperty()
  @IsNumber()
  agg: number;

  @ApiProperty({ required: false }) // Mark optional in Swagger
  @IsOptional() // ✅ Allows it to be omitted
  @IsNumber()
  period: number;

  @ApiProperty()
  @IsNumber()
  thresholdType: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  condition?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  thresholdValue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  resetDeadband?: number;

  @ApiProperty()
  description: string;

  @ApiProperty()
  customerId: number;

  @ApiProperty()
  users: {
    id: number;
    notificationType: number;
  }[];

  @ApiProperty({ required: false })
  learningPeriod?: string;

  @ApiProperty({ required: false })
  includeVelocity?: boolean;

  @ApiProperty({ required: false })
  includeMomentum?: boolean;

  @ApiProperty({ required: false })
  createdAt?: Date;

  @ApiProperty({ required: false })
  updatedAt?: Date;
}
