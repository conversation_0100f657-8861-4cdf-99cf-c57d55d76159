import { Migration } from '@mikro-orm/migrations';

export class Migration20230721186000 extends Migration {
  async up(): Promise<void> {
    this.addSql('alter table "measurement" drop constraint "datasource_fk";');
    this.addSql(
      'alter table "measurement" add constraint "measurement_datasource_foreign" foreign key ("datasource") references "datasource" ("id") on update cascade on delete set null;',
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "measurement" add constraint "datasource_fk" foreign key ("datasource") references "datasource" ("id") on update cascade on delete set null;',
    );
    this.addSql(
      'alter table "measurement" drop constraint "measurement_datasource_foreign";',
    );
  }
}
