import {
  Entity<PERSON><PERSON><PERSON>,
  EntityRepository,
  Reference,
  UniqueConstraintViolationException,
} from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { CustomerId } from "src/customers/domain/customer.entity";
import { User, UserId } from "../domain/user.entity";
import { UserPreferences } from "../domain/user_preferences.entity";
import { UserFilters } from "../dto/user.dto";
import { PreferenceKeys } from "../dto/user_perference.dto";

type UserPreferencesData = {
  [key in PreferenceKeys]?: string;
};

export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly entityRepository: EntityRepository<User>,
    @InjectRepository(UserPreferences)
    private readonly entityUserPreferenceRepository: EntityRepository<UserPreferences>,
    private readonly em: EntityManager
  ) {}

  async add(user: User, createdById?: UserId): Promise<User> {
    if (createdById) {
      user.createdBy = Reference.createFromPK(User, createdById);
    }
    user.createdAt = new Date();
    try {
      await this.entityRepository.persistAndFlush(user);
    } catch (error) {
      if (error instanceof UniqueConstraintViolationException) {
        throw new Error("User already exists");
      }
      throw error;
    }
    return user;
  }

  async findByUsername(username: string): Promise<User | null> {
    return await this.entityRepository.findOne({
      username: username.toLowerCase(),
    });
  }

  async findByEmail(email: string) {
    return await this.entityRepository.findOne({ email });
  }

  async getAllUsers(filters: UserFilters): Promise<User[]> {
    let customerQuery = `
      SELECT json_agg(
        json_build_object(
            'id', c.id,
            'name_id', c.name_id,
            'name', c.name,
            'address', c.address
        )
      )
      FROM customer c
      JOIN customer_user_role ucr2 ON c.id = ucr2.customer_id
      WHERE ucr2.user_id = u2.id
      and ucr2.role = ucr.role
    `;
    if (filters.customerId) {
      customerQuery = `
        SELECT json_agg(
          json_build_object(
              'id', c.id,
              'name_id', c.name_id,
              'name', c.name,
              'address', c.address
          )
        )
        FROM customer c
        JOIN customer_user_role ucr2 ON c.id = ucr2.customer_id
        WHERE ucr2.user_id = u2.id
        and ucr2.role = ucr.role
        AND c.id IN (${filters.customerId})
      `;
    }
    let rolesMappingQuery = "";
    Object.values(Role)
      ?.filter((item) => typeof item === "number")
      ?.map((item) => {
        rolesMappingQuery += ` WHEN ${item} THEN '${
          Role[item.toString().toUpperCase()]
        }'`;
      });

    let roleQuery = `
      SELECT json_agg(
        json_build_object(
          'role_id', ucr.role,
          'role', CASE ucr.role
            ${rolesMappingQuery}
            ELSE 'Unknown'
          END,
          'customers', (
            ${customerQuery}
          )
        )
      )
      FROM (SELECT DISTINCT role FROM customer_user_role WHERE user_id = u2.id) ucr
    `;
    if (filters.role) {
      let multipleRoleQuery = [];
      filters.role.split(",").map((item) => {
        multipleRoleQuery.push(Role[item.trim().toUpperCase()] ?? -1);
      });
      roleQuery = `
        SELECT json_agg(
          json_build_object(
            'role_id', ucr.role,
            'role', CASE ucr.role
              ${rolesMappingQuery}
              ELSE 'Unknown'
            END,
            'customers', (
              ${customerQuery}
            )
          )
        )
        FROM (SELECT DISTINCT role FROM customer_user_role WHERE user_id = u2.id and role in (${multipleRoleQuery.join(
          ","
        )})) ucr
      `;
    }
    let query = `
      SELECT
        u2.id AS id,
        u2.username AS username,
        u2.email AS email,
        u2.enabled AS enabled,
        u2.first_name AS first_name,
        u2.last_name AS last_name,
        u2.phone_no AS phone_no,
        u2.country_code AS country_code,
        (
          ${roleQuery}
        ) AS scoped_roles
      FROM "user" u2
    `;

    let queryWhereAndFlag = false;

    if (filters.userName) {
      query = query + ` WHERE u2.username ILIKE '%${filters.userName}%'`;
      queryWhereAndFlag = true;
    }

    if (filters.userEmail) {
      query =
        query +
        ` ${queryWhereAndFlag === true ? "AND" : "WHERE"} u2.email ILIKE '%${
          filters.userEmail
        }%'`;
      if (!queryWhereAndFlag) queryWhereAndFlag = true;
    }

    if (filters.customerId) {
      customerQuery = customerQuery + ` AND c.id IN (${filters.customerId})`;
      query =
        query +
        `
        ${queryWhereAndFlag === true ? "AND" : "WHERE"} u2.id IN (
          SELECT DISTINCT ucr.user_id
          FROM customer_user_role ucr
          JOIN customer c ON ucr.customer_id = c.id
          WHERE c.id IN (${filters.customerId})
        )
      `;
      if (!queryWhereAndFlag) queryWhereAndFlag = true;
    }

    if (filters.customerName) {
      customerQuery =
        customerQuery + ` AND c.name ILIKE '%${filters.customerName}&'`;
      query =
        query +
        `
        ${queryWhereAndFlag === true ? "AND" : "WHERE"} (u2.first_name
        ILIKE '%${filters.customerName}%' OR u2.last_name ILIKE '%${
          filters.customerName
        }%')
      `;
      if (!queryWhereAndFlag) queryWhereAndFlag = true;
    }

    if (filters.role) {
      let multipleRoleQuery = [];
      filters.role.split(",").map((item) => {
        multipleRoleQuery.push(Role[item.trim().toUpperCase()] ?? -1);
      });
      query =
        query +
        `
      ${queryWhereAndFlag === true ? "AND" : "WHERE"} 
          EXISTS (
            SELECT 1 FROM customer_user_role
            WHERE user_id = u2.id
            AND role IN (${multipleRoleQuery.join(",")})
          )
      `;
      if (!queryWhereAndFlag) queryWhereAndFlag = true;
    }
    const result = await this.em.getConnection().execute(query);
    return result;
  }

  async countByUsername(username: string) {
    return await this.entityRepository.count({ username });
  }

  async update(user: User, runById: UserId) {
    user.updatedAt = new Date();
    user.updatedBy = Reference.createFromPK(User, runById);

    await this.entityRepository.persistAndFlush(user);
  }

  async findById(
    id: UserId,
    filter: { customerId?: CustomerId } = {}
  ): Promise<User | null> {
    const condition = filter.customerId
      ? { id, customerRoles: { customer: { id: filter.customerId } } }
      : { id };

    return await this.entityRepository.findOne(condition);
  }

  async getUserPreference(userId: UserId) {
    const findAllUserPreferences =
      await this.entityUserPreferenceRepository.find({
        user: Reference.createFromPK(User, userId),
      });
    const userPreferences = {};
    findAllUserPreferences?.map((item) => {
      userPreferences[item.preferKey] = item.preferValue;
    });
    return {
      preferences: userPreferences,
    };
  }

  async addUpdateUserPreference(
    userId: UserId,
    preference: UserPreferencesData
  ) {
    const findAllUserPreferences =
      await this.entityUserPreferenceRepository.find({
        user: Reference.createFromPK(User, userId),
      });
    const existsKeys = findAllUserPreferences?.map((item) => item.preferKey);
    const preferenceKeys: PreferenceKeys[] = Object.keys(
      preference
    ) as PreferenceKeys[];

    for (const key of preferenceKeys) {
      if (existsKeys.includes(key)) {
        await this.entityUserPreferenceRepository.nativeUpdate(
          {
            preferKey: key,
            user: Reference.createFromPK(User, userId),
          },
          {
            preferValue: preference[key],
            updatedAt: new Date(),
          }
        );
      } else {
        // const userPreferences =
        //   await this.entityUserPreferenceRepository.create({
        //     user: Reference.createFromPK(User, userId),
        //     preferKey: key,
        //     preferValue: preference[key],
        //     createdAt: new Date(),
        //     updatedAt: new Date(),
        //   });
        const userPref = new UserPreferences();
        userPref.preferKey = key;
        userPref.preferValue = preference[key];
        userPref.createdAt = new Date();
        userPref.updatedAt = new Date();
        const user = await this.findById(userId);
        userPref.user = user;

        await this.entityUserPreferenceRepository.persistAndFlush(userPref);
      }
    }
  }
}
