import { Migration } from '@mikro-orm/migrations';

export class Migration20250311093540_nullable_alert_field_period extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "alerts" drop constraint "alerts_period_foreign";');

    this.addSql('alter table "alerts" alter column "period" type int using ("period"::int);');
    this.addSql('alter table "alerts" alter column "period" drop not null;');
    this.addSql('alter table "alerts" add constraint "alerts_period_foreign" foreign key ("period") references "periods" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "alerts" drop constraint "alerts_period_foreign";');

    this.addSql('alter table "alerts" alter column "period" type int using ("period"::int);');
    this.addSql('alter table "alerts" alter column "period" set not null;');
    this.addSql('alter table "alerts" add constraint "alerts_period_foreign" foreign key ("period") references "periods" ("id") on update cascade;');
  }

}
