
import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property, Unique } from '@mikro-orm/core';
import { CalculationInstance } from './calculation-instance.entity';
import { Measurement } from '../../measurements/domain/measurement.entity';
@Entity()
@Unique({ name: 'calc_instance_input_unique', properties: ['calculationInstance', 'inputLabel'] })
export class CalculationInput {

  @PrimaryKey()
  id!: number;

  @Property({ length: 6, nullable: true })
  created?: Date;

  @Property({ length: 6, nullable: true })
  updated?: Date;

  @Property({ nullable: true })
  createdby?: number;

  @Property({ nullable: true })
  updatedby?: number;

  @Property({ fieldName: 'input_label' })
  inputLabel!: string;

  @ManyToOne({ entity: () => CalculationInstance, fieldName: 'calculation_instance' })
  calculationInstance!: CalculationInstance;

  @ManyToOne({ entity: () => Measurement, fieldName: 'measurement', nullable: true })
  measurement?: Measurement;

  @Property({ nullable: true })
  constantNumber?: string;

  @Property({ length: 30, nullable: true })
  constantString?: string;

  @Property({ length: 50, nullable: true })
  comment?: string;

}