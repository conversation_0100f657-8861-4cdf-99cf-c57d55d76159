import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { HttpService } from "@nestjs/axios";
import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import qs from "qs";
import { firstValueFrom } from "rxjs";
import { CustomerId } from "src/customers/domain/customer.entity";
import { InvalidInputException } from "src/errors/exceptions";
import {
  TIMESERIES_CONFIG,
  TimeSeriesConfig,
} from "src/timeseries/timeseries.config";
import { User } from "src/users/domain/user.entity";
import { DataType } from "./domain/data-type.entity";
import { Location } from "./domain/location.entity";
import {
  MeasurementLocation,
  MeasurementType,
} from "./domain/measurement-type.entity";
import { MeasurementBackofficeType } from "./domain/types";
import { UnitOfMeasure } from "./domain/unit-of-measure.entity";
import { ValueType } from "./domain/value-type.entity";
import { AssetMeasurementRepository } from "./repository/asset-measurement.repository";

const alphabeticalOrder = {
  orderBy: { name: "asc" },
};
@Injectable()
export class MeasurementsBackofficeService {
  private readonly timeSeriesApiBaseUrl;
  constructor(
    @InjectRepository(DataType)
    private readonly dataTypeRepository: EntityRepository<DataType>,
    @InjectRepository(ValueType)
    private readonly valueTypeRepository: EntityRepository<ValueType>,
    @InjectRepository(MeasurementType)
    private readonly measurementTypeRepository: EntityRepository<MeasurementType>,
    @InjectRepository(Location)
    private readonly locationRepository: EntityRepository<Location>,
    private readonly em: EntityManager,
    private readonly assetMeasurementRepository: AssetMeasurementRepository,
    private readonly httpService: HttpService,
    @Inject(TIMESERIES_CONFIG)
    private readonly timeSeriesConfig: TimeSeriesConfig
  ) {
    const protocol = this.timeSeriesConfig.ssl ? "https" : "http";
    this.timeSeriesApiBaseUrl = `${protocol}://${this.timeSeriesConfig.host}/api/${this.timeSeriesConfig.version}/timeseries`;
  }

  async getAll(type: MeasurementBackofficeType) {
    if (type === "data") {
      return await this.dataTypeRepository.findAll(alphabeticalOrder);
    } else if (type === "value") {
      return await this.valueTypeRepository.findAll(alphabeticalOrder);
    } else if (type === "measurement") {
      return await this.measurementTypeRepository.findAll(alphabeticalOrder);
    } else if (type === "location") {
      return await this.locationRepository.findAll(alphabeticalOrder);
    } else {
      throw new InvalidInputException("Invalid metadata type");
    }
  }

  async getMeasurementTypeById(id: number): Promise<MeasurementType | null> {
    return await this.measurementTypeRepository.findOne({ id });
  }

  async createMeasurementType(params: { name: string; createdBy: User }) {
    if (!params.name) {
      throw new BadRequestException("Measurement type name is required");
    }

    return await this.em.transactional(async (em) => {
      const existingType = await this.measurementTypeRepository.findOne({
        name: params.name,
      });

      if (existingType) {
        throw new ConflictException(
          `Measurement type with name "${params.name}" already exists`
        );
      }

      const measurementType = em.create(MeasurementType, {
        name: params.name,
        createdBy: Reference.create(params.createdBy),
        createdAt: new Date(),
      });

      await em.persistAndFlush(measurementType);
      return measurementType;
    });
  }

  async updateMeasurementType(
    id: number,
    params: {
      name: string;
      updatedBy: User;
    }
  ) {
    if (!params.name) {
      throw new BadRequestException("Measurement type name is required");
    }

    return await this.em.transactional(async (em) => {
      const measurementTypeRepo = em.getRepository(MeasurementType);

      const measurementType = await measurementTypeRepo.findOne({ id });
      if (!measurementType) {
        throw new NotFoundException(`Measurement type with ID ${id} not found`);
      }

      const existingType = await measurementTypeRepo.findOne({
        name: params.name,
        id: { $ne: id },
      });
      if (existingType) {
        throw new ConflictException(
          `Another measurement type with name "${params.name}" already exists`
        );
      }

      measurementType.name = params.name;
      measurementType.updatedBy = Reference.create(params.updatedBy);
      measurementType.updatedAt = new Date();

      await em.persistAndFlush(measurementType);
      return measurementType;
    });
  }

  async deleteMeasurementType(id: number) {
    return await this.em.transactional(async (em) => {
      const measurementTypeRepo = em.getRepository(MeasurementType);
      const unitOfMeasureRepo = em.getRepository(UnitOfMeasure);

      // Get the measurement type
      const measurementType = await measurementTypeRepo.findOne({ id });
      if (!measurementType) {
        throw new NotFoundException(`Measurement type with ID ${id} not found`);
      }

      // Find any units associated with this measurement type
      const associatedUnits = await unitOfMeasureRepo.find({
        measurementType: { id },
      });

      // Remove the association (not deleting the units)
      for (const unit of associatedUnits) {
        unit.measurementType = null;
        em.persist(unit);
      }

      await em.flush();

      // Now delete the measurement type
      try {
        await measurementTypeRepo.removeAndFlush(measurementType);
      } catch (error) {
        // If there are other entities referencing this measurement type
        throw new ConflictException(
          `Cannot delete measurement type with ID ${id} as it is referenced by other entities`
        );
      }
    });
  }

  async getDataTypeById(id: number): Promise<DataType | null> {
    return await this.dataTypeRepository.findOne({ id });
  }

  async getValueTypeById(id: number): Promise<ValueType | null> {
    return await this.valueTypeRepository.findOne({ id });
  }

  async getLocationById(id: number): Promise<Location | null> {
    return await this.locationRepository.findOne({ id });
  }

  async getLocationByMeasurement(
    assetMeasurementId: number
  ): Promise<MeasurementLocation | null> {
    const measurement = await this.assetMeasurementRepository.findById(
      assetMeasurementId
    );

    if (!measurement) {
      throw new NotFoundException("Measurement not found");
    }

    const sql = `select * from v_measurements vm where vm.id = ${measurement.measurementId};`;
    const result = await this.em.getConnection().execute(sql);

    if (result?.length === 0) {
      throw new NotFoundException("Location not found");
    }

    return {
      latitude: result[0]?.latitude,
      longitude: result[0]?.longitude,
    };
  }

  async getAllMeasurementsByCustomer(
    customerId: CustomerId,
    headers: Request["headers"],
    lastReadings?: boolean
  ) {
    // const sql = `select id, tag,units,asset_path,datasource,a_type,parent_asset from v_measurements vm where vm.cust_id = ${customerId};`;
    const sql = lastReadings
      ? `
       SELECT DISTINCT m.id,
       am.id AS asset_measurement_id,
      m.tag,
      m.m_type,
      uom.name AS units,
      at.name::text || COALESCE('/'::text || pt.name::text, ''::text) AS asset_type,
      cu.name AS customer,
      rt.tag AS site,
      ah.path AS asset_path,
      ah.child AS parent_asset,
      m.datasource,
      a.a_type
     FROM measurement m
       JOIN data_type dt ON dt.id = m.data_type AND m.deleted_at IS NULL AND COALESCE(m.enabled, true) <> false
       LEFT JOIN value_type vt ON vt.id = m.value_type
       LEFT JOIN unit_of_measure uom ON uom.id = m.unit_of_measure
       JOIN asset_measurement am ON am.measurement = m.id
       JOIN measurement_type mt ON mt.id = m.m_type
       LEFT JOIN location l ON l.id = am.location
       JOIN asset a ON a.id = am.asset
       JOIN asset_type at ON a.a_type = at.id
       LEFT JOIN asset_type pt ON at.parent_type = pt.id
       JOIN customer cu ON a.customer = cu.id
       JOIN v_asset_hier ah ON ah.child = am.asset
       JOIN asset rt ON split_part(ah.path::text, ':'::text, 1) = rt.tag::text AND rt.customer = a.customer
       LEFT JOIN calculation_instance ci ON ci.output_measurement = m.id
       WHERE cu.id = ${customerId}
    ORDER BY cu.name, rt.tag, (at.name::text || COALESCE('/'::text || pt.name::text, ''::text));`
      : `select id, tag from v_measurements vm where vm.cust_id = ${customerId};`;

    const result = await this.em.getConnection().execute(sql);
    // let lastReadingsData = [];
    // if (lastReadings) {
    //   const ids = result.map((m) => m.id);
    //   const data = await this.httpProxy(
    //     `${this.timeSeriesApiBaseUrl}/lastreadings/${customerId}`,
    //     { meas_id: ids.join(",") },
    //     { "be-csrftoken": headers["be-csrftoken"], Cookie: headers["cookie"] }
    //   );
    //   lastReadingsData = data;
    // }
    // return { result, lastReadingsData };
    return result;
  }
  private async httpProxy(url: string, queryParams: any, headers?: any) {
    const { data } = await firstValueFrom(
      this.httpService.get(url, {
        params: queryParams,
        paramsSerializer: (params) =>
          qs.stringify(params, { arrayFormat: "repeat" }),
        headers: headers,
      })
    );
    return data;
  }
  async getMeasurementsByIds(measurements: number[]) {
    const assetMeasure =
      await this.assetMeasurementRepository.getMeasurementsByIds(measurements);
    return assetMeasure.map((m) => {
      return {
        ...JSON.parse(JSON.stringify(m)),
        metricName: m.measurement.metric?.name ?? null,
      };
    });
  }
}
