import { Test } from '@nestjs/testing';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { User } from 'src/users/domain/user.entity';
import { AssetTypeService } from './asset-type.service';
import { AssetType } from './domain/asset-type.entity';
import { AssetTypeRepository } from './repository/asset-type.repository';

describe('AssetTypeService', () => {
  describe('create', () => {
    test('parent asset type should be added to repository', async () => {
      const { assetTypeService, userId, assetTypeRepositoryMock } =
        await createAssetTypeService();

      await assetTypeService.create({ name: 'Fan' }, userId);

      expect(assetTypeRepositoryMock.add.mock.calls.length).toBe(1);
      expect(assetTypeRepositoryMock.add.mock.calls[0][0].name).toBe('Fan');
    });

    test('child asset type should be added to repository', async () => {
      const { assetTypeService, userId, assetTypeRepositoryMock } =
        await createAssetTypeService();

      await assetTypeService.create({ name: 'Rotor', parentTypeId: 4 }, userId);

      expect(assetTypeRepositoryMock.add.mock.calls.length).toBe(1);
      expect(assetTypeRepositoryMock.add.mock.calls[0][0].name).toBe('Rotor');
      expect(assetTypeRepositoryMock.add.mock.calls[0][0].parentType?.id).toBe(
        4,
      );
    });

    test('child asset type with non existing parent should throw an exception', async () => {
      const { assetTypeService, userId, nonExistingAssetTypeId } =
        await createAssetTypeService();

      expect(
        assetTypeService.create(
          { name: 'Rotor', parentTypeId: nonExistingAssetTypeId },
          userId,
        ),
      ).rejects.toThrow('Parent asset type does not exist');
    });
  });
});

const createAssetTypeService = async () => {
  const assetTypeRepositoryMock: jest.Mocked<
    Pick<AssetTypeRepository, 'add' | 'findById'>
  > = {
    add: jest.fn(),
    findById: jest.fn(async (id) => {
      if (id === nonExistingAssetTypeId) {
        return null;
      } else {
        return new AssetType({ name: 'fan' });
      }
    }),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [createMikroOrmTestModule([AssetType, User, Customer])],
    providers: [
      {
        provide: AssetTypeRepository,
        useValue: assetTypeRepositoryMock,
      },
      AssetTypeService,
    ],
  }).compile();
  const assetTypeService = moduleRef.get(AssetTypeService);
  const userId = 4;

  const nonExistingAssetTypeId = 404;

  return {
    assetTypeService,
    assetTypeRepositoryMock,
    userId,
    nonExistingAssetTypeId,
  };
};
